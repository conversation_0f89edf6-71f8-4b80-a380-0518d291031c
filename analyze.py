#!/usr/bin/env python3

"""
SIMPLIFIED Android App Analyzer - Based on analyze_proper_flow.py pattern
Removes complex classes that cause UiAutomator2 crashes
Implements hierarchical navigation: Main → Menu → Submenu → Sub-submenu → Back
"""

import os
import sys
import time
import yaml
import json
from datetime import datetime
from appium import webdriver
from appium.options.android import UiA<PERSON>mator2Options

def load_config():
    """Load configuration from YAML file"""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def start_simple_appium_session(package):
    """Start Appium session with simple, reliable settings - SAME AS analyze_proper_flow.py"""
    print(f"[SIMPLE] Starting Appium session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    # Prevent app restart - EXACT SAME as analyze_proper_flow.py
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    print("[SIMPLE] Connecting to Appium server...")
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)
    
    print("✅ Simple Appium session started!")
    return driver

def simple_wait_for_page_load(driver, timeout=10):
    """Simple page load wait - SAME AS analyze_proper_flow.py"""
    print(f"[WAIT] Waiting for page to load (max {timeout}s)...")
    
    start_time = time.time()
    stable_count = 0
    
    while time.time() - start_time < timeout:
        try:
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            element_count = len(elements)
            
            if element_count > 5:
                stable_count += 1
                if stable_count >= 2:
                    print(f"[WAIT] ✅ Page loaded with {element_count} elements")
                    return True
            else:
                stable_count = 0
            
            time.sleep(1)
            
        except Exception as e:
            print(f"[WAIT] Error during wait: {e}")
            time.sleep(1)
    
    print(f"[WAIT] ⚠️ Timeout reached, proceeding...")
    return False

def collect_all_elements_simple(driver, page_name="Unknown"):
    """Collect ALL elements on current page with scrolling - EXACT SAME as analyze_proper_flow.py"""
    print(f"\n[COLLECT] 📋 Starting element collection for: {page_name}")

    all_elements = []
    seen_elements = set()
    scroll_count = 0
    max_scrolls = 5
    menu_locations = {}  # Track menu locations - YOUR REQUIREMENT
    
    while scroll_count <= max_scrolls:
        try:
            print(f"[COLLECT] Collecting elements (scroll position {scroll_count}/{max_scrolls})...")
            
            # Get all meaningful elements
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='' or @clickable='true']")
            
            new_elements_found = 0
            for element in elements:
                try:
                    # Get element info
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    clickable = element.get_attribute('clickable') == 'true'
                    class_name = element.get_attribute('class') or ''
                    bounds = element.get_attribute('bounds') or ''

                    # Get location info - YOUR REQUIREMENT
                    location = element.location
                    size = element.size

                    # Create unique identifier
                    identifier = f"{text}|{desc}|{bounds}"
                    
                    # Filter out null/empty elements and duplicates - YOUR REQUIREMENT
                    has_valid_text = text and text != 'null' and text.strip() != ''
                    has_valid_desc = desc and desc != 'null' and desc.strip() != ''
                    
                    if (identifier not in seen_elements and 
                        (has_valid_text or has_valid_desc)):
                        seen_elements.add(identifier)
                        
                        element_info = {
                            'text': text,
                            'content_desc': desc,
                            'clickable': clickable,
                            'class': class_name,
                            'bounds': bounds,
                            'page': page_name,
                            'scroll_position': scroll_count,
                            # YOUR REQUIREMENT: Menu location tracking
                            'location': {
                                'x': location['x'],
                                'y': location['y'],
                                'width': size['width'],
                                'height': size['height'],
                                'center_x': location['x'] + size['width'] // 2,
                                'center_y': location['y'] + size['height'] // 2
                            }
                        }
                        
                        all_elements.append(element_info)
                        new_elements_found += 1

                        # Track menu locations - YOUR REQUIREMENT
                        if is_menu_item(text, desc):
                            menu_name = text or desc
                            menu_locations[menu_name] = {
                                'x': location['x'],
                                'y': location['y'],
                                'center_x': location['x'] + size['width'] // 2,
                                'center_y': location['y'] + size['height'] // 2,
                                'width': size['width'],
                                'height': size['height'],
                                'scroll_position': scroll_count,
                                'visible': True
                            }
                            print(f"[LOCATION] 📍 Menu '{menu_name}' found at y={location['y']}, center=({location['x'] + size['width'] // 2}, {location['y'] + size['height'] // 2})")

                        click_indicator = "🔘" if clickable else "⚪"
                        display_text = text or desc or 'No text'
                        print(f"[COLLECT]   {len(all_elements):3d}. {click_indicator} '{display_text[:50]}'")

                except Exception as e:
                    print(f"[COLLECT] Error getting element info: {e}")
                    continue
            
            print(f"[COLLECT] Found {new_elements_found} new elements at scroll position {scroll_count}")
            
            # If no new elements and we've scrolled, we're done
            if new_elements_found == 0 and scroll_count > 0:
                print(f"[COLLECT] ✅ No new elements found, collection complete")
                break
            
            # Scroll down to reveal more elements (if not at max)
            if scroll_count < max_scrolls:
                print(f"[COLLECT] Scrolling down to reveal more elements...")
                simple_safe_scroll_down(driver)
                time.sleep(1)  # Wait for scroll to complete
            
            scroll_count += 1
            
        except Exception as e:
            print(f"[COLLECT] Error during collection: {e}")
            break
    
    print(f"[COLLECT] ✅ Collection complete for {page_name}: {len(all_elements)} total elements")
    print(f"[LOCATION] 📍 Found {len(menu_locations)} menu locations")

    # Return both elements and menu locations - YOUR REQUIREMENT
    return all_elements, menu_locations

def simple_safe_scroll_down(driver):
    """Safe scroll down that avoids pull-to-refresh - SAME AS analyze_proper_flow.py"""
    try:
        size = driver.get_window_size()
        start_x = size['width'] // 2
        start_y = int(size['height'] * 0.6)  # Start from 60% down
        end_y = int(size['height'] * 0.3)    # End at 30% down
        
        driver.swipe(start_x, start_y, start_x, end_y, 1000)
        
    except Exception as e:
        print(f"[SCROLL] Error in safe scroll: {e}")

def is_menu_item(text: str, desc: str) -> bool:
    """Check if element is a menu item - YOUR REQUIREMENT"""
    content = (text + ' ' + desc).lower()

    # Main menu items
    main_menus = [
        'ruang gtk', 'ruang murid', 'ruang sekolah', 'ruang bahasa',
        'ruang pemerintah', 'ruang mitra', 'ruang publik', 'ruang orang tua',
        'sumber belajar', 'pusat perbukuan', 'pengelolaan kinerja'
    ]

    return any(menu in content for menu in main_menus)

def scroll_to_menu_position(driver, menu_name: str, menu_locations: dict) -> bool:
    """Scroll to bring menu into view based on its known location - YOUR REQUIREMENT"""
    try:
        if menu_name not in menu_locations:
            print(f"[SCROLL_TO_MENU] ❌ No location data for menu: {menu_name}")
            return False

        menu_location = menu_locations[menu_name]
        target_y = menu_location['center_y']

        print(f"[SCROLL_TO_MENU] 📍 Menu '{menu_name}' is at y={target_y}")

        # Get current screen dimensions
        size = driver.get_window_size()
        screen_height = size['height']

        # Define visible area (excluding status bar and navigation)
        visible_top = 200  # Account for status bar
        visible_bottom = screen_height - 200  # Account for navigation bar

        # Check if menu is already in visible area
        if visible_top <= target_y <= visible_bottom:
            print(f"[SCROLL_TO_MENU] ✅ Menu '{menu_name}' already in visible area (y={target_y})")
            return True

        print(f"[SCROLL_TO_MENU] 📍 Menu '{menu_name}' NOT in visible area (y={target_y})")
        print(f"[SCROLL_TO_MENU] Visible area: {visible_top} to {visible_bottom}")
        print(f"[SCROLL_TO_MENU] Need to scroll to bring menu into view...")

        # Calculate scroll direction and amount
        if target_y < visible_top:
            # Menu is above visible area - scroll up
            print(f"[SCROLL_TO_MENU] Menu above visible area, scrolling up...")
            scroll_direction = "up"
            scroll_amount = (visible_top - target_y) // 100 + 1
        else:
            # Menu is below visible area - scroll down
            print(f"[SCROLL_TO_MENU] Menu below visible area, scrolling down...")
            scroll_direction = "down"
            scroll_amount = (target_y - visible_bottom) // 100 + 1

        # Perform scrolling - YOUR REQUIREMENT: Actually scroll to position
        max_scrolls = min(scroll_amount, 5)  # Limit to 5 scrolls
        print(f"[SCROLL_TO_MENU] 🔄 Starting {scroll_direction} scroll ({max_scrolls} scrolls needed)")

        for i in range(max_scrolls):
            print(f"[SCROLL_TO_MENU] Performing scroll {i+1}/{max_scrolls} ({scroll_direction})...")

            if scroll_direction == "up":
                # Safe scroll up - bring content from bottom to top
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.6)  # Start from 60%
                end_y = int(size['height'] * 0.8)    # End at 80% (upward swipe)
                print(f"[SCROLL_TO_MENU]   Scrolling UP: ({start_x}, {start_y}) → ({start_x}, {end_y})")
                driver.swipe(start_x, start_y, start_x, end_y, 1000)
            else:
                # Safe scroll down - bring content from top to bottom
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.7)  # Start from 70%
                end_y = int(size['height'] * 0.3)    # End at 30% (downward swipe)
                print(f"[SCROLL_TO_MENU]   Scrolling DOWN: ({start_x}, {start_y}) → ({start_x}, {end_y})")
                driver.swipe(start_x, start_y, start_x, end_y, 1000)

            time.sleep(1.5)  # Wait for scroll to complete
            print(f"[SCROLL_TO_MENU] ✅ Scroll {i+1}/{max_scrolls} completed")

            # Check if menu is now in visible area after each scroll
            try:
                # Re-check menu position after scroll
                current_elements = driver.find_elements("xpath", f"//*[@text='{menu_name}' or @content-desc='{menu_name}']")
                if current_elements:
                    current_location = current_elements[0].location
                    current_y = current_location['y']
                    print(f"[SCROLL_TO_MENU] Menu now at y={current_y}")

                    if visible_top <= current_y <= visible_bottom:
                        print(f"[SCROLL_TO_MENU] ✅ Menu '{menu_name}' now in visible area after {i+1} scrolls!")
                        return True
                else:
                    print(f"[SCROLL_TO_MENU] Menu not found after scroll {i+1}")
            except Exception as e:
                print(f"[SCROLL_TO_MENU] Could not check menu position after scroll: {e}")

        print(f"[SCROLL_TO_MENU] ✅ Completed {max_scrolls} scrolls to bring '{menu_name}' into view")
        return True

    except Exception as e:
        print(f"[SCROLL_TO_MENU] Error scrolling to menu: {e}")
        return False

def scroll_to_position(driver, target_scroll_position: int) -> bool:
    """Scroll to a specific scroll position - YOUR REQUIREMENT"""
    try:
        print(f"[SCROLL_TO_POS] 📍 Scrolling to position {target_scroll_position}")

        if target_scroll_position == 0:
            print(f"[SCROLL_TO_POS] Target is position 0 (top), scrolling to top...")
            # Scroll to top
            size = driver.get_window_size()
            for i in range(5):  # Multiple scrolls to ensure we reach top
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.6)
                end_y = int(size['height'] * 0.9)
                driver.swipe(start_x, start_y, start_x, end_y, 1000)
                time.sleep(1)
                print(f"[SCROLL_TO_POS] Scroll to top {i+1}/5")
            print(f"[SCROLL_TO_POS] ✅ Reached top position")
            return True

        # For other positions, scroll down step by step
        print(f"[SCROLL_TO_POS] Scrolling down {target_scroll_position} positions...")
        size = driver.get_window_size()

        # First scroll to top
        for i in range(3):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.6)
            end_y = int(size['height'] * 0.9)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)

        # Then scroll down to target position
        for i in range(target_scroll_position):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.7)
            end_y = int(size['height'] * 0.3)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1.5)
            print(f"[SCROLL_TO_POS] Scroll down {i+1}/{target_scroll_position}")

        print(f"[SCROLL_TO_POS] ✅ Reached scroll position {target_scroll_position}")
        return True

    except Exception as e:
        print(f"[SCROLL_TO_POS] Error scrolling to position: {e}")
        return False

def simple_smart_click_menu(driver, menu_name, menu_locations=None):
    """Simple smart click menu with location awareness - YOUR REQUIREMENT"""
    print(f"\n[CLICK] 🎯 Attempting to click menu: {menu_name}")

    # Step 1: Scroll to menu position if location is known - YOUR REQUIREMENT
    if menu_locations and menu_name in menu_locations:
        menu_location = menu_locations[menu_name]
        scroll_position = menu_location.get('scroll_position', 0)

        print(f"[CLICK] 📍 Using known location for {menu_name}")
        print(f"[CLICK] Menu found at scroll position {scroll_position}, y={menu_location['y']}")

        # Method 1: Scroll to exact scroll position - YOUR REQUIREMENT
        print(f"[CLICK] 🔄 Scrolling to exact position {scroll_position}...")
        if scroll_to_position(driver, scroll_position):
            print(f"[CLICK] ✅ Scrolled to position {scroll_position}")
            time.sleep(2)  # Wait for scroll to settle
        else:
            print(f"[CLICK] ⚠️ Could not scroll to position, trying coordinate-based scroll...")
            # Method 2: Fallback to coordinate-based scrolling
            if not scroll_to_menu_position(driver, menu_name, menu_locations):
                print(f"[CLICK] ⚠️ Could not scroll to menu position, trying anyway...")
    else:
        print(f"[CLICK] ⚠️ No location data for {menu_name}, trying direct click...")
    
    # Try multiple patterns and methods
    patterns = [
        f"//*[@text='{menu_name}' or @content-desc='{menu_name}']",
        f"//*[contains(@text, '{menu_name}') or contains(@content-desc, '{menu_name}')]",
        f"//android.widget.ImageView[@content-desc='{menu_name}']",
        f"//android.widget.TextView[@text='{menu_name}']",
        f"//*[@clickable='true' and (@text='{menu_name}' or @content-desc='{menu_name}')]"
    ]
    
    for i, pattern in enumerate(patterns, 1):
        try:
            print(f"[CLICK] Trying pattern {i}: {pattern[:60]}...")
            elements = driver.find_elements("xpath", pattern)
            
            if elements:
                element = elements[0]
                print(f"[CLICK] Found element, attempting click...")
                
                # Method 1: Regular click
                try:
                    element.click()
                    time.sleep(2)  # Wait for navigation
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (regular click)")
                    return True
                except:
                    pass
                
                # Method 2: Tap using coordinates
                try:
                    location = element.location
                    size = element.size
                    x = location['x'] + size['width'] // 2
                    y = location['y'] + size['height'] // 2
                    
                    driver.tap([(x, y)])
                    time.sleep(2)
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (tap)")
                    return True
                except:
                    pass
                
        except Exception as e:
            print(f"[CLICK] Pattern {i} failed: {e}")
            continue
    
    print(f"[CLICK] ❌ Could not click {menu_name}")
    return False

def simple_go_back(driver):
    """Go back to previous page - SAME AS analyze_proper_flow.py"""
    try:
        print("\n[BACK] 🔙 Going back to previous page...")
        
        # Method 1: Back button
        try:
            driver.back()
            time.sleep(2)
            print("[BACK] ✅ Used back button")
            return True
        except:
            pass
        
        # Method 2: Home navigation
        try:
            # Look for home/main indicators
            home_patterns = [
                "//*[@content-desc='Home' or @text='Home']",
                "//*[contains(@content-desc, 'Beranda') or contains(@text, 'Beranda')]",
                "//*[@content-desc='Navigate up' or @text='Navigate up']"
            ]
            
            for pattern in home_patterns:
                elements = driver.find_elements("xpath", pattern)
                if elements:
                    elements[0].click()
                    time.sleep(2)
                    print("[BACK] ✅ Used home navigation")
                    return True
        except:
            pass
        
        print("[BACK] ⚠️ Could not navigate back")
        return False
        
    except Exception as e:
        print(f"[BACK] Error going back: {e}")
        return False

def find_submenu_items_simple(driver):
    """Find potential submenu items on current page - YOUR REQUIREMENT"""
    print(f"\n[SUBMENU] 🔍 Finding submenu items...")

    submenu_items = []

    try:
        # Get all clickable elements
        clickable_elements = driver.find_elements("xpath", "//*[@clickable='true']")

        for element in clickable_elements:
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                class_name = element.get_attribute('class') or ''

                # Filter out navigation elements and main menus
                if is_potential_submenu_simple(text, desc, class_name):
                    submenu_items.append({
                        'element': element,
                        'text': text,
                        'content_desc': desc,
                        'class': class_name
                    })

            except Exception as e:
                continue

        print(f"[SUBMENU] Found {len(submenu_items)} potential submenu items")
        return submenu_items[:10]  # Limit to first 10 to avoid too many

    except Exception as e:
        print(f"[SUBMENU] Error finding submenu items: {e}")
        return []

def is_potential_submenu_simple(text: str, desc: str, class_name: str) -> bool:
    """Check if element is a potential submenu item - YOUR REQUIREMENT"""
    # Skip empty elements
    if not text.strip() and not desc.strip():
        return False

    # Skip null elements - YOUR REQUIREMENT
    if text == 'null' or desc == 'null':
        return False

    # Skip navigation elements
    nav_keywords = ['beranda', 'home', 'back', 'kembali', 'tab', 'navigation']
    content = (text + ' ' + desc).lower()
    if any(keyword in content for keyword in nav_keywords):
        return False

    # Skip main menu items (they should be on main page)
    main_menus = ['ruang gtk', 'ruang murid', 'ruang sekolah', 'ruang bahasa']
    if any(menu in content for menu in main_menus):
        return False

    # Include if has meaningful content
    if len(text.strip()) > 2 or len(desc.strip()) > 2:
        return True

    return False

def crawl_submenus_simple(driver, parent_menu: str, submenu_locations=None, depth: int = 1):
    """Crawl through submenus hierarchically with location awareness - YOUR REQUIREMENT"""
    try:
        print(f"\n[SUBMENU_CRAWL] 📂 Starting submenu crawl for {parent_menu} at depth {depth}")

        if depth > 3:  # Limit depth to prevent infinite recursion
            print(f"[SUBMENU_CRAWL] Max depth reached for {parent_menu}")
            return

        # Find all potential submenu items
        submenus = find_submenu_items_simple(driver)

        # If we have location data, prioritize submenus with known locations - YOUR REQUIREMENT
        if submenu_locations:
            print(f"[SUBMENU_CRAWL] 📍 Using location data for {len(submenu_locations)} potential submenus")
            for submenu_name, location in submenu_locations.items():
                print(f"[SUBMENU_CRAWL]   - {submenu_name}: y={location['y']}")

        if not submenus:
            print(f"[SUBMENU_CRAWL] No submenus found in {parent_menu}")
            return

        print(f"[SUBMENU_CRAWL] Found {len(submenus)} potential submenus in {parent_menu}")

        # Navigate through each submenu
        for i, submenu_info in enumerate(submenus, 1):
            try:
                submenu_name = submenu_info.get('text', submenu_info.get('content_desc', f'Submenu_{i}'))
                print(f"\n[SUBMENU_CRAWL] Clicking submenu {i}/{len(submenus)}: {submenu_name}")

                # Click the submenu
                if click_submenu_item_simple(driver, submenu_info):
                    # Wait for page to load
                    simple_wait_for_page_load(driver, timeout=5)

                    # Collect elements in submenu
                    submenu_elements, sub_submenu_locations = collect_all_elements_simple(driver, f"{parent_menu} > {submenu_name}")
                    print(f"[SUBMENU_CRAWL] Collected {len(submenu_elements)} elements from {submenu_name}")

                    # Recursively crawl sub-submenus - YOUR REQUIREMENT
                    crawl_submenus_simple(driver, f"{parent_menu} > {submenu_name}", sub_submenu_locations, depth + 1)

                    # Go back to parent menu
                    simple_go_back(driver)
                    simple_wait_for_page_load(driver, timeout=5)

                else:
                    print(f"[SUBMENU_CRAWL] Could not click submenu: {submenu_name}")

            except Exception as e:
                print(f"[SUBMENU_CRAWL] Error processing submenu {i}: {e}")
                # Try to recover by going back
                simple_go_back(driver)
                time.sleep(1)
                continue

        print(f"[SUBMENU_CRAWL] ✅ Completed submenu crawl for {parent_menu}")

    except Exception as e:
        print(f"[SUBMENU_CRAWL] Error in submenu crawl for {parent_menu}: {e}")

def click_submenu_item_simple(driver, submenu_info: dict) -> bool:
    """Click a submenu item - YOUR REQUIREMENT"""
    try:
        element = submenu_info['element']
        name = submenu_info.get('text', submenu_info.get('content_desc', 'Unknown'))

        print(f"[CLICK_SUBMENU] Attempting to click: {name}")

        # Method 1: Regular click
        try:
            element.click()
            time.sleep(2)
            print(f"[CLICK_SUBMENU] ✅ Successfully clicked submenu: {name}")
            return True
        except:
            pass

        # Method 2: Tap using coordinates
        try:
            location = element.location
            size = element.size
            x = location['x'] + size['width'] // 2
            y = location['y'] + size['height'] // 2

            driver.tap([(x, y)])
            time.sleep(2)
            print(f"[CLICK_SUBMENU] ✅ Successfully clicked submenu: {name} (tap)")
            return True
        except:
            pass

        print(f"[CLICK_SUBMENU] ❌ Failed to click submenu: {name}")
        return False

    except Exception as e:
        print(f"[CLICK_SUBMENU] Error clicking submenu: {e}")
        return False

def go_back_to_main_simple(driver):
    """Navigate back to main page from any submenu - YOUR REQUIREMENT"""
    try:
        print("\n[NAVIGATE_MAIN] 🏠 Navigating back to main page...")

        max_attempts = 3
        for attempt in range(max_attempts):
            # Try to go back
            if simple_go_back(driver):
                time.sleep(2)

                # Check if we're on main page by looking for main menu items
                main_indicators = [
                    "//*[@text='Ruang GTK']",
                    "//*[@text='Ruang Murid']",
                    "//*[@text='Ruang Sekolah']"
                ]

                main_elements_found = 0
                for indicator in main_indicators:
                    try:
                        elements = driver.find_elements("xpath", indicator)
                        if elements:
                            main_elements_found += 1
                    except:
                        continue

                if main_elements_found >= 2:
                    print(f"[NAVIGATE_MAIN] ✅ Successfully reached main page (attempt {attempt + 1})")
                    return True
                else:
                    print(f"[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt {attempt + 1})")
            else:
                print(f"[NAVIGATE_MAIN] Back navigation failed (attempt {attempt + 1})")

        print("[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts")
        return False

    except Exception as e:
        print(f"[NAVIGATE_MAIN] Error navigating to main: {e}")
        return False

def run_hierarchical_crawl_simple(driver):
    """Run the complete hierarchical crawl - YOUR EXACT REQUIREMENT"""
    print("\n" + "="*80)
    print("🎯 STARTING HIERARCHICAL CRAWL - YOUR EXACT FLOW")
    print("="*80)
    print("Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu")
    print("="*80)

    all_collected_data = {}

    try:
        # Step 1: Collect all elements on main page first
        print("\n📋 STEP 1: Collecting all elements on MAIN PAGE")
        main_page_elements, main_menu_locations = collect_all_elements_simple(driver, "Main Page")
        all_collected_data["Main Page"] = main_page_elements

        # Store menu locations for smart clicking - YOUR REQUIREMENT
        print(f"[LOCATION] 📍 Stored locations for {len(main_menu_locations)} menus")
        for menu_name, location in main_menu_locations.items():
            print(f"[LOCATION]   - {menu_name}: y={location['y']}, center=({location['center_x']}, {location['center_y']})")

        # Step 2: Find all main menus
        main_menus = [
            "Ruang GTK", "Ruang Murid", "Ruang Sekolah", "Ruang Bahasa",
            "Ruang Pemerintah", "Ruang Mitra", "Ruang Publik", "Ruang Orang Tua"
        ]

        print(f"\n🎯 STEP 2: Found {len(main_menus)} main menus to crawl")

        # Step 3: Click each menu and follow your exact flow
        for menu_index, menu_name in enumerate(main_menus, 1):
            try:
                print(f"\n" + "="*60)
                print(f"🎯 MENU {menu_index}/{len(main_menus)}: {menu_name}")
                print("="*60)

                # YOUR REQUIREMENT: "when click menu 1 in main page will directly into another page or submenu"
                # Use menu location for smart clicking - YOUR REQUIREMENT
                if simple_smart_click_menu(driver, menu_name, main_menu_locations):
                    # Wait for page to load
                    simple_wait_for_page_load(driver, timeout=10)

                    # YOUR REQUIREMENT: "after that collect all element locators in the other page"
                    # YOUR REQUIREMENT: "and also you need to scroll down step by step to make element visible"
                    menu_elements, submenu_locations = collect_all_elements_simple(driver, f"Menu: {menu_name}")
                    all_collected_data[f"Menu: {menu_name}"] = menu_elements

                    # Store submenu locations for hierarchical navigation - YOUR REQUIREMENT
                    print(f"[LOCATION] 📍 Found {len(submenu_locations)} submenu locations in {menu_name}")

                    # YOUR REQUIREMENT: "when all element locators for submenu already collected"
                    # YOUR REQUIREMENT: "the code need to scroll to top and start click the submenu 1, 2, 3, n"
                    print(f"\n🔍 Starting submenu crawl for {menu_name}...")
                    crawl_submenus_simple(driver, menu_name, submenu_locations)

                    # YOUR REQUIREMENT: "after this you need to go back to main page"
                    print(f"\n🔙 Going back to main page from {menu_name}...")
                    go_back_to_main_simple(driver)

                    print(f"✅ Completed crawling menu: {menu_name}")

                else:
                    print(f"❌ Could not click menu: {menu_name}")

            except Exception as e:
                print(f"❌ Error processing menu {menu_name}: {e}")
                # Try to recover by going back to main
                go_back_to_main_simple(driver)
                continue

        # Step 4: Save all collected data
        save_collected_data_simple(all_collected_data)

        print("\n" + "="*80)
        print("🎉 HIERARCHICAL CRAWL COMPLETE!")
        print("="*80)
        print(f"✅ Crawled {len(main_menus)} main menus")
        print(f"✅ Collected data from {len(all_collected_data)} pages")
        print(f"✅ Followed your exact flow: Main → Menu → Submenu → Sub-submenu → Back")
        print("="*80)

        return all_collected_data

    except Exception as e:
        print(f"❌ Error in hierarchical crawl: {e}")
        return all_collected_data

def save_collected_data_simple(all_data):
    """Save collected data to JSON file"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"locators/simplified_hierarchical_collection_{timestamp}.json"

        # Create locators directory if it doesn't exist
        os.makedirs("locators", exist_ok=True)

        # Prepare data for saving
        save_data = {
            "timestamp": timestamp,
            "collection_type": "hierarchical_simplified_with_locations",
            "total_pages": len(all_data),
            "pages": {},
            "features": [
                "Menu location tracking",
                "Smart scroll to menu position",
                "Hierarchical navigation",
                "Null element filtering",
                "Step-by-step scrolling"
            ]
        }

        total_elements = 0
        for page_name, elements in all_data.items():
            save_data["pages"][page_name] = {
                "element_count": len(elements),
                "elements": elements
            }
            total_elements += len(elements)

        save_data["total_elements"] = total_elements

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Data saved to: {filename}")
        print(f"📊 Total elements collected: {total_elements}")

    except Exception as e:
        print(f"❌ Error saving data: {e}")

def main():
    """Main function - SIMPLIFIED VERSION"""
    print("🚀 SIMPLIFIED ANDROID APP ANALYZER")
    print("Based on analyze_proper_flow.py pattern")
    print("Implements your exact hierarchical navigation flow")
    print("="*60)

    try:
        # Load config
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"

        # Start simple Appium session
        driver = start_simple_appium_session(package)

        # Wait for app to load
        simple_wait_for_page_load(driver, timeout=15)

        # Run hierarchical crawl with your exact flow
        collected_data = run_hierarchical_crawl_simple(driver)

        print("\n🎉 SIMPLIFIED ANALYSIS COMPLETE!")
        print(f"✅ Successfully collected data from {len(collected_data)} pages")

        # Keep session open for inspection
        print("\nPress Enter to close session...")
        input()

        driver.quit()

    except Exception as e:
        print(f"❌ Error in main: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
