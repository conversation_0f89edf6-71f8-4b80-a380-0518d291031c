#!/usr/bin/env python3

"""
SIMPLIFIED Android App Analyzer - Based on analyze_proper_flow.py pattern
Removes complex classes that cause UiAutomator2 crashes
Implements hierarchical navigation: Main → Menu → Submenu → Sub-submenu → Back
"""

import os
import sys
import time
import yaml
import json
from datetime import datetime
from appium import webdriver
from appium.options.android import UiA<PERSON>mator2Options

def load_config():
    """Load configuration from YAML file"""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def start_simple_appium_session(package):
    """Start Appium session with simple, reliable settings - SAME AS analyze_proper_flow.py"""
    print(f"[SIMPLE] Starting Appium session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    # Prevent app restart - EXACT SAME as analyze_proper_flow.py
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    print("[SIMPLE] Connecting to Appium server...")
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)

    # Activate the app - THIS WAS MISSING!
    print("[SIMPLE] Activating app...")
    driver.activate_app(package)
    time.sleep(3)  # Wait for app to load

    print("✅ Simple Appium session started and app activated!")
    return driver

def handle_login_popup(driver):
    """Handle 'Anda Belum Login' popup by clicking close button - YOUR REQUIREMENT"""
    try:
        print("[POPUP] 🔍 Checking for login popup...")

        # Check for login popup text
        popup_indicators = [
            "anda belum login",
            "you haven't logged in",
            "belum login",
            "mohon gunakan email",
            "belajar.id",
            "madrasah.kemenag.go.id"
        ]

        # Find popup elements
        all_elements = driver.find_elements("xpath", "//*")
        popup_found = False

        for element in all_elements:
            try:
                text = (element.text or '').lower()
                desc = (element.get_attribute('content-desc') or '').lower()
                content = text + ' ' + desc

                if any(indicator in content for indicator in popup_indicators):
                    popup_found = True
                    print(f"[POPUP] ❌ Login popup detected: {text[:50]}...")
                    break
            except:
                continue

        if popup_found:
            print("[POPUP] 🚨 Login popup detected - attempting to close...")

            # Try to find and click close button
            close_patterns = [
                "//*[@text='×' or @content-desc='×']",
                "//*[@text='X' or @content-desc='X']",
                "//*[@text='Close' or @content-desc='Close']",
                "//*[@text='Tutup' or @content-desc='Tutup']",
                "//*[contains(@class, 'close')]",
                "//*[contains(@resource-id, 'close')]",
                "//android.widget.ImageButton",
                "//android.widget.Button[position()=1]"  # Often first button is close
            ]

            popup_closed = False
            for pattern in close_patterns:
                try:
                    # Check server health before attempting popup closure
                    if not check_server_health(driver):
                        print(f"[POPUP] ❌ Server crashed during popup handling")
                        return False

                    close_button = driver.find_element("xpath", pattern)
                    close_button.click()
                    print(f"[POPUP] ✅ Closed login popup using pattern: {pattern}")
                    time.sleep(3)  # Wait longer for popup to close
                    popup_closed = True
                    break
                except Exception as close_error:
                    error_msg = str(close_error)
                    if "instrumentation process is not running" in error_msg:
                        print(f"[POPUP] 🚨 Server crashed during popup closure")
                        return False
                    continue

            # If no close button found, try back button
            if not popup_closed:
                try:
                    # Check server health before back button
                    if not check_server_health(driver):
                        print(f"[POPUP] ❌ Server crashed, cannot use back button")
                        return False

                    driver.back()
                    print("[POPUP] ✅ Closed popup using back button")
                    time.sleep(3)  # Wait longer for navigation
                    popup_closed = True
                except Exception as back_error:
                    error_msg = str(back_error)
                    if "instrumentation process is not running" in error_msg:
                        print(f"[POPUP] 🚨 Server crashed during back button")
                        return False
                    pass

            if popup_closed:
                print("[POPUP] 🎯 Popup closed successfully - continuing with next menu")
                return True
            else:
                print("[POPUP] ⚠️ Could not close login popup")
                return False
        else:
            print("[POPUP] ✅ No login popup detected")
            return True

    except Exception as e:
        print(f"[POPUP] Error handling popup: {e}")
        return False

def simple_wait_for_page_load(driver, timeout=10):
    """Simple page load wait - SAME AS analyze_proper_flow.py"""
    print(f"[WAIT] Waiting for page to load (max {timeout}s)...")
    
    start_time = time.time()
    stable_count = 0
    
    while time.time() - start_time < timeout:
        try:
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            element_count = len(elements)
            
            if element_count > 5:
                stable_count += 1
                if stable_count >= 2:
                    print(f"[WAIT] ✅ Page loaded with {element_count} elements")

                    # YOUR REQUIREMENT: Check and handle login popup after page load
                    handle_login_popup(driver)
                    return True
            else:
                stable_count = 0
            
            time.sleep(1)
            
        except Exception as e:
            print(f"[WAIT] Error during wait: {e}")
            time.sleep(1)
    
    print(f"[WAIT] ⚠️ Timeout reached, proceeding...")
    return False

def collect_all_elements_simple(driver, page_name="Unknown"):
    """Collect ALL elements on current page with scrolling - EXACT SAME as analyze_proper_flow.py"""
    print(f"\n[COLLECT] 📋 Starting element collection for: {page_name}")

    # YOUR REQUIREMENT: Handle popup before starting collection
    handle_login_popup(driver)

    all_elements = []
    seen_elements = set()
    scroll_count = 0
    max_scrolls = 5
    menu_locations = {}  # Track menu locations - YOUR REQUIREMENT
    
    while scroll_count <= max_scrolls:
        try:
            print(f"[COLLECT] Collecting elements (scroll position {scroll_count}/{max_scrolls})...")
            
            # Get all meaningful elements
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='' or @clickable='true']")
            
            new_elements_found = 0
            for element in elements:
                try:
                    # Get element info
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    clickable = element.get_attribute('clickable') == 'true'
                    class_name = element.get_attribute('class') or ''
                    bounds = element.get_attribute('bounds') or ''

                    # Get location info - YOUR REQUIREMENT
                    location = element.location
                    size = element.size

                    # Create unique identifier - PREVENT DUPLICATES
                    identifier = f"{text}|{desc}|{bounds}|{scroll_count}"

                    # Filter out null/empty elements and duplicates - YOUR REQUIREMENT
                    has_valid_text = text and text != 'null' and text.strip() != ''
                    has_valid_desc = desc and desc != 'null' and desc.strip() != ''

                    # STRICT duplicate prevention
                    if (identifier not in seen_elements and
                        (has_valid_text or has_valid_desc) and
                        f"{text}|{desc}" not in [f"{e['text']}|{e['content_desc']}" for e in all_elements]):
                        seen_elements.add(identifier)
                        
                        element_info = {
                            'text': text,
                            'content_desc': desc,
                            'clickable': clickable,
                            'class': class_name,
                            'bounds': bounds,
                            'page': page_name,
                            'scroll_position': scroll_count,
                            # YOUR REQUIREMENT: Menu location tracking
                            'location': {
                                'x': location['x'],
                                'y': location['y'],
                                'width': size['width'],
                                'height': size['height'],
                                'center_x': location['x'] + size['width'] // 2,
                                'center_y': location['y'] + size['height'] // 2
                            }
                        }
                        
                        all_elements.append(element_info)
                        new_elements_found += 1

                        # Track menu locations - YOUR REQUIREMENT
                        if is_menu_item(text, desc):
                            menu_name = text or desc
                            menu_locations[menu_name] = {
                                'x': location['x'],
                                'y': location['y'],
                                'center_x': location['x'] + size['width'] // 2,
                                'center_y': location['y'] + size['height'] // 2,
                                'width': size['width'],
                                'height': size['height'],
                                'scroll_position': scroll_count,
                                'visible': True
                            }
                            print(f"[LOCATION] 📍 Menu '{menu_name}' found at y={location['y']}, center=({location['x'] + size['width'] // 2}, {location['y'] + size['height'] // 2})")

                        click_indicator = "🔘" if clickable else "⚪"
                        display_text = text or desc or 'No text'
                        print(f"[COLLECT]   {len(all_elements):3d}. {click_indicator} '{display_text[:50]}'")

                except Exception as e:
                    print(f"[COLLECT] Error getting element info: {e}")
                    continue
            
            print(f"[COLLECT] Found {new_elements_found} new elements at scroll position {scroll_count}")
            
            # If no new elements and we've scrolled, we're done
            if new_elements_found == 0 and scroll_count > 0:
                print(f"[COLLECT] ✅ No new elements found, collection complete")
                break
            
            # Scroll down to reveal more elements (if not at max)
            if scroll_count < max_scrolls:
                print(f"[COLLECT] Scrolling down to reveal more elements...")
                simple_safe_scroll_down(driver)
                time.sleep(1)  # Wait for scroll to complete
            
            scroll_count += 1
            
        except Exception as e:
            print(f"[COLLECT] Error during collection: {e}")
            break
    
    print(f"[COLLECT] ✅ Collection complete for {page_name}: {len(all_elements)} total elements")
    print(f"[LOCATION] 📍 Found {len(menu_locations)} menu locations")

    # Return both elements and menu locations - YOUR REQUIREMENT
    return all_elements, menu_locations

def simple_safe_scroll_down(driver):
    """Safe scroll down that avoids pull-to-refresh - SAME AS analyze_proper_flow.py"""
    try:
        size = driver.get_window_size()
        start_x = size['width'] // 2
        start_y = int(size['height'] * 0.6)  # Start from 60% down
        end_y = int(size['height'] * 0.3)    # End at 30% down
        
        driver.swipe(start_x, start_y, start_x, end_y, 1000)
        
    except Exception as e:
        print(f"[SCROLL] Error in safe scroll: {e}")

def is_menu_item(text: str, desc: str) -> bool:
    """Check if element is a menu item - YOUR REQUIREMENT"""
    content = (text + ' ' + desc).lower()

    # Main menu items
    main_menus = [
        'ruang gtk', 'ruang murid', 'ruang sekolah', 'ruang bahasa',
        'ruang pemerintah', 'ruang mitra', 'ruang publik', 'ruang orang tua',
        'sumber belajar', 'pusat perbukuan', 'pengelolaan kinerja'
    ]

    return any(menu in content for menu in main_menus)

def get_current_page_position(driver) -> int:
    """Get current scroll position by analyzing visible elements - YOUR REQUIREMENT"""
    try:
        print(f"[POSITION_CHECK] 🔍 Analyzing current page position...")

        # Get all visible elements
        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")

        # Look for specific indicators to determine position
        position_indicators = {
            0: ["jelajahi beragam", "temukan ruang pendidikan", "ruang gtk", "ruang murid"],  # Top of page
            1: ["ruang gtk", "ruang murid", "ruang sekolah", "ruang bahasa"],  # Main menus visible
            2: ["ruang pemerintah", "ruang mitra", "ruang publik", "ruang orang tua"],  # Secondary menus
            3: ["sumber belajar", "portal pembelajaran", "layanan paling"],  # Services section
            4: ["pusat perbukuan", "portal buku", "siswa, guru"],  # Books section
            5: ["pengelolaan kinerja", "dokumen rujukan", "butuh bantuan"]  # Performance section
        }

        # Check which indicators are visible
        visible_texts = []
        element_positions = []

        for element in elements:
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                location = element.location

                if text and text != 'null':
                    visible_texts.append(text.lower())
                    element_positions.append((text.lower(), location['y']))
                if desc and desc != 'null':
                    visible_texts.append(desc.lower())
                    element_positions.append((desc.lower(), location['y']))
            except:
                continue

        print(f"[POSITION_CHECK] Found {len(visible_texts)} text elements")

        # Determine position based on visible indicators with priority
        position_scores = {}

        for position, indicators in position_indicators.items():
            score = 0
            found_indicators = []

            for indicator in indicators:
                for text in visible_texts:
                    if indicator in text:
                        score += 1
                        found_indicators.append(indicator)
                        break

            if score > 0:
                position_scores[position] = (score, found_indicators)
                print(f"[POSITION_CHECK] Position {position}: score={score}, found={found_indicators}")

        if position_scores:
            # Return position with highest score
            best_position = max(position_scores.keys(), key=lambda p: position_scores[p][0])
            score, indicators = position_scores[best_position]
            print(f"[POSITION_CHECK] ✅ Current position: {best_position} (score={score}, indicators={indicators})")
            return best_position

        # Fallback: analyze Y positions of elements
        if element_positions:
            avg_y = sum(y for _, y in element_positions) / len(element_positions)
            print(f"[POSITION_CHECK] Average Y position: {avg_y}")

            # Estimate position based on Y coordinate
            if avg_y < 800:
                estimated_pos = 0
            elif avg_y < 1200:
                estimated_pos = 1
            elif avg_y < 1600:
                estimated_pos = 2
            elif avg_y < 2000:
                estimated_pos = 3
            else:
                estimated_pos = 4

            print(f"[POSITION_CHECK] ✅ Estimated position from Y coords: {estimated_pos}")
            return estimated_pos

        print(f"[POSITION_CHECK] ⚠️ Could not determine position, assuming 0")
        return 0

    except Exception as e:
        print(f"[POSITION_CHECK] Error checking position: {e}")
        return 0

def dynamic_scroll_to_menu(driver, menu_name: str, menu_locations: dict) -> bool:
    """DYNAMIC scrolling to menu position - YOUR EXACT REQUIREMENT"""
    try:
        print(f"\n[DYNAMIC_SCROLL] 🎯 DYNAMIC SCROLL TO MENU: {menu_name}")
        print(f"[DYNAMIC_SCROLL] Your requirement: 'scroll must be dynamic depend what code needed to click'")

        if menu_name not in menu_locations:
            print(f"[DYNAMIC_SCROLL] ❌ No location data for menu: {menu_name}")
            return False

        menu_location = menu_locations[menu_name]
        target_position = menu_location['scroll_position']

        print(f"[DYNAMIC_SCROLL] 📍 Menu '{menu_name}' should be at scroll position: {target_position}")

        # Step 1: Check current position - YOUR REQUIREMENT
        current_position = get_current_page_position(driver)
        print(f"[DYNAMIC_SCROLL] 📍 Current page position: {current_position}")
        print(f"[DYNAMIC_SCROLL] 📍 Target position: {target_position}")

        # Step 2: Calculate if we need to scroll and in which direction - YOUR REQUIREMENT
        if current_position == target_position:
            print(f"[DYNAMIC_SCROLL] ✅ Already at target position {target_position}")
            return True

        if current_position < target_position:
            # Need to scroll DOWN to reach target
            scroll_direction = "down"
            scroll_steps = target_position - current_position
            print(f"[DYNAMIC_SCROLL] 📍 Need to scroll DOWN {scroll_steps} positions")
        else:
            # Need to scroll UP to reach target
            scroll_direction = "up"
            scroll_steps = current_position - target_position
            print(f"[DYNAMIC_SCROLL] 📍 Need to scroll UP {scroll_steps} positions")

        # Step 3: Perform dynamic scrolling - YOUR REQUIREMENT
        print(f"[DYNAMIC_SCROLL] 🔄 Starting DYNAMIC {scroll_direction.upper()} scroll ({scroll_steps} steps)")

        size = driver.get_window_size()

        for step in range(scroll_steps):
            print(f"[DYNAMIC_SCROLL] Step {step+1}/{scroll_steps}: Scrolling {scroll_direction}...")

            if scroll_direction == "down":
                # Scroll down to reveal lower content
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.6)  # Start from 60%
                end_y = int(size['height'] * 0.4)    # End at 40% (downward swipe)
                driver.swipe(start_x, start_y, start_x, end_y, 800)
            else:
                # Scroll up to reveal upper content
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.4)  # Start from 40%
                end_y = int(size['height'] * 0.6)    # End at 60% (upward swipe)
                driver.swipe(start_x, start_y, start_x, end_y, 800)

            time.sleep(1.5)  # Wait for scroll to complete

            # Check if we've reached the target position
            new_position = get_current_page_position(driver)
            print(f"[DYNAMIC_SCROLL] After step {step+1}: Position {current_position} → {new_position}")

            if new_position == target_position:
                print(f"[DYNAMIC_SCROLL] ✅ Reached target position {target_position} after {step+1} steps!")
                return True

            current_position = new_position

        print(f"[DYNAMIC_SCROLL] ✅ Completed {scroll_steps} dynamic scroll steps")

        # Step 4: Verify menu is now visible - YOUR REQUIREMENT
        try:
            menu_elements = driver.find_elements("xpath", f"//*[@text='{menu_name}' or @content-desc='{menu_name}']")
            if menu_elements:
                print(f"[DYNAMIC_SCROLL] ✅ Menu '{menu_name}' is now visible!")
                return True
            else:
                print(f"[DYNAMIC_SCROLL] ⚠️ Menu '{menu_name}' still not visible, but position reached")
                return True
        except Exception as e:
            print(f"[DYNAMIC_SCROLL] Could not verify menu visibility: {e}")
            return True

    except Exception as e:
        print(f"[DYNAMIC_SCROLL] Error in dynamic scroll: {e}")
        return False

def is_at_top_of_page(driver) -> bool:
    """Check if we're at the top of the page - YOUR REQUIREMENT"""
    try:
        print(f"[TOP_CHECK] 🔍 Checking if at top of page...")

        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
        top_indicators = ["jelajahi beragam", "temukan ruang pendidikan"]

        for element in elements:
            try:
                text = (element.get_attribute('text') or '').lower()
                desc = (element.get_attribute('content-desc') or '').lower()

                if any(indicator in text or indicator in desc for indicator in top_indicators):
                    location = element.location
                    if location['y'] < 600:  # Top indicator is near top of screen
                        print(f"[TOP_CHECK] ✅ At top - found '{text or desc}' at y={location['y']}")
                        return True
            except:
                continue

        print(f"[TOP_CHECK] ❌ Not at top - no top indicators found near top")
        return False

    except Exception as e:
        print(f"[TOP_CHECK] Error checking top: {e}")
        return False

def scroll_to_top_smart(driver) -> bool:
    """Smart scroll to top - PREVENTS PULL-TO-REFRESH - YOUR REQUIREMENT"""
    try:
        print(f"[SMART_TOP] 📍 Smart scroll to top...")

        # Step 1: Check if already at top - YOUR REQUIREMENT
        if is_at_top_of_page(driver):
            print(f"[SMART_TOP] ✅ Already at top - no scroll needed (prevents pull-to-refresh)")
            return True

        print(f"[SMART_TOP] Not at top, need to scroll up...")
        size = driver.get_window_size()

        # Step 2: Scroll to top only if needed - YOUR REQUIREMENT
        for attempt in range(5):  # Up to 5 attempts
            print(f"[SMART_TOP] Scroll to top attempt {attempt+1}/5")

            try:
                # Gentle upward swipe - avoid pull-to-refresh zone
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.6)  # Start lower to avoid pull-to-refresh
                end_y = int(size['height'] * 0.9)    # Large upward movement
                driver.swipe(start_x, start_y, start_x, end_y, 1000)
                time.sleep(2)

                # Check if we've reached top
                if is_at_top_of_page(driver):
                    print(f"[SMART_TOP] ✅ Reached top after {attempt+1} attempts")
                    return True

            except Exception as scroll_error:
                error_msg = str(scroll_error)
                print(f"[SMART_TOP] ⚠️ Scroll attempt {attempt+1} failed: {scroll_error}")

                # Check for server crash
                if "instrumentation process is not running" in error_msg:
                    print(f"[SMART_TOP] 🚨 Server crashed during scroll - aborting")
                    return False

                time.sleep(1)

        print(f"[SMART_TOP] ⚠️ Could not reach top after 5 attempts")
        return False

    except Exception as e:
        print(f"[SMART_TOP] Error in smart scroll to top: {e}")
        return False

def scroll_to_position_gentle(driver, target_scroll_position: int) -> bool:
    """Gentle scroll to specific position - PREVENTS CRASHES"""
    try:
        print(f"[GENTLE_SCROLL] 📍 Gently scrolling to position {target_scroll_position}")

        if target_scroll_position == 0:
            return scroll_to_top_smart(driver)

        # For other positions, gentle scroll down step by step
        print(f"[GENTLE_SCROLL] Gentle scroll down {target_scroll_position} positions...")
        try:
            size = driver.get_window_size()

            # First gentle scroll to top
            for i in range(2):  # Reduced scrolls
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.5)
                end_y = int(size['height'] * 0.8)
                driver.swipe(start_x, start_y, start_x, end_y, 800)
                time.sleep(2)

            # Then gentle scroll down to target position
            for i in range(target_scroll_position):
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.6)  # Less aggressive
                end_y = int(size['height'] * 0.4)    # Smaller scroll distance
                driver.swipe(start_x, start_y, start_x, end_y, 800)  # Slower
                time.sleep(2)  # Longer wait between scrolls
                print(f"[GENTLE_SCROLL] Gentle scroll down {i+1}/{target_scroll_position}")

            print(f"[GENTLE_SCROLL] ✅ Reached scroll position {target_scroll_position}")
            return True

        except Exception as e:
            print(f"[GENTLE_SCROLL] Error in gentle position scroll: {e}")
            return False

    except Exception as e:
        print(f"[GENTLE_SCROLL] Error in gentle scroll: {e}")
        return False

def check_server_health(driver) -> bool:
    """Check if UiAutomator2 server is healthy - PREVENTS CRASHES"""
    try:
        # Simple health check
        driver.find_elements("xpath", "//*")
        return True
    except Exception as e:
        error_msg = str(e)
        print(f"[HEALTH] ❌ Server unhealthy: {e}")

        # Check for specific UiAutomator2 crash
        if "instrumentation process is not running" in error_msg or "probably crashed" in error_msg:
            print(f"[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart")
            print(f"[HEALTH] 💡 Recommendation: Restart the automation script")
            return False

        return False

def restart_appium_session(package: str):
    """Restart Appium session after crash - CRASH RECOVERY"""
    try:
        print(f"[RESTART] 🔄 Attempting to restart Appium session...")

        # Wait a bit for cleanup
        time.sleep(5)

        # Start new session
        new_driver = start_simple_appium_session(package)
        if new_driver:
            print(f"[RESTART] ✅ Successfully restarted Appium session")
            return new_driver
        else:
            print(f"[RESTART] ❌ Failed to restart Appium session")
            return None

    except Exception as e:
        print(f"[RESTART] ❌ Error restarting session: {e}")
        return None

def detect_context_and_app(driver) -> tuple:
    """Detect current context and app - PREVENTS CHROME OPENING"""
    try:
        # Check if we're in Chrome or external browser
        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")

        chrome_indicators = ["welcome to chrome", "sign in to browse", "add account to device", "use without an account"]
        browser_indicators = ["browser", "chrome", "firefox", "safari"]

        for element in elements:
            try:
                text = (element.get_attribute('text') or '').lower()
                desc = (element.get_attribute('content-desc') or '').lower()

                if any(indicator in text or indicator in desc for indicator in chrome_indicators):
                    print(f"[CONTEXT] ❌ Detected Chrome browser - external navigation")
                    return ("CHROME", "com.android.chrome")

                if any(indicator in text or indicator in desc for indicator in browser_indicators):
                    print(f"[CONTEXT] ❌ Detected external browser")
                    return ("BROWSER", "external")

            except:
                continue

        # Check current package
        try:
            current_activity = driver.current_activity
            if "chrome" in current_activity.lower():
                print(f"[CONTEXT] ❌ In Chrome activity: {current_activity}")
                return ("CHROME", "com.android.chrome")
        except:
            pass

        print(f"[CONTEXT] ✅ In main app context")
        return ("APP", "com.kemendikdasmen.rumahpendidikan")

    except Exception as e:
        print(f"[CONTEXT] Error detecting context: {e}")
        return ("UNKNOWN", "unknown")

def simple_smart_click_menu(driver, menu_name, menu_locations=None):
    """Simple smart click menu with crash prevention - YOUR REQUIREMENT"""
    print(f"\n[CLICK] 🎯 Attempting to click menu: {menu_name}")

    # Step 0: Health check - PREVENT CRASHES
    if not check_server_health(driver):
        print(f"[CLICK] ❌ Server unhealthy, skipping menu: {menu_name}")
        return False

    # Step 1: Scroll to menu position if location is known - YOUR REQUIREMENT
    if menu_locations and menu_name in menu_locations:
        menu_location = menu_locations[menu_name]
        scroll_position = menu_location.get('scroll_position', 0)

        print(f"[CLICK] 📍 Using known location for {menu_name}")
        print(f"[CLICK] Menu found at scroll position {scroll_position}, y={menu_location['y']}")

        # Method 1: Smart scroll to top if needed - YOUR REQUIREMENT
        if scroll_position == 0:
            print(f"[CLICK] 🔄 Smart scrolling to top for position 0...")
            if not scroll_to_top_smart(driver):
                print(f"[CLICK] ⚠️ Could not scroll to top")
            time.sleep(2)
        else:
            # Method 2: DYNAMIC scroll to menu position - YOUR EXACT REQUIREMENT
            print(f"[CLICK] 🔄 Using DYNAMIC scrolling to reach menu...")
            try:
                if dynamic_scroll_to_menu(driver, menu_name, menu_locations):
                    print(f"[CLICK] ✅ Dynamic scroll successful - menu should be visible")
                    time.sleep(2)  # Wait for scroll to settle
                else:
                    print(f"[CLICK] ⚠️ Dynamic scroll failed, trying gentle fallback...")
                    # Fallback to gentle scrolling
                    if scroll_to_position_gentle(driver, scroll_position):
                        print(f"[CLICK] ✅ Gentle fallback successful")
                        time.sleep(2)
                    else:
                        print(f"[CLICK] ⚠️ All scroll methods failed, trying direct click...")
            except Exception as e:
                print(f"[CLICK] ⚠️ Scroll failed: {e}, trying direct click...")
    else:
        print(f"[CLICK] ⚠️ No location data for {menu_name}, trying direct click...")
    
    # Try multiple patterns and methods
    patterns = [
        f"//*[@text='{menu_name}' or @content-desc='{menu_name}']",
        f"//*[contains(@text, '{menu_name}') or contains(@content-desc, '{menu_name}')]",
        f"//android.widget.ImageView[@content-desc='{menu_name}']",
        f"//android.widget.TextView[@text='{menu_name}']",
        f"//*[@clickable='true' and (@text='{menu_name}' or @content-desc='{menu_name}')]"
    ]
    
    for i, pattern in enumerate(patterns, 1):
        try:
            print(f"[CLICK] Trying pattern {i}: {pattern[:60]}...")
            elements = driver.find_elements("xpath", pattern)
            
            if elements:
                element = elements[0]
                print(f"[CLICK] Found element, attempting click...")
                
                # Method 1: Regular click
                try:
                    element.click()
                    time.sleep(2)  # Wait for navigation
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (regular click)")
                    return True
                except:
                    pass
                
                # Method 2: Tap using coordinates
                try:
                    location = element.location
                    size = element.size
                    x = location['x'] + size['width'] // 2
                    y = location['y'] + size['height'] // 2
                    
                    driver.tap([(x, y)])
                    time.sleep(2)
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (tap)")
                    return True
                except:
                    pass
                
        except Exception as e:
            print(f"[CLICK] Pattern {i} failed: {e}")
            continue
    
    print(f"[CLICK] ❌ Could not click {menu_name}")
    return False

def simple_go_back(driver):
    """Go back to previous page - SAME AS analyze_proper_flow.py"""
    try:
        print("\n[BACK] 🔙 Going back to previous page...")
        
        # Method 1: Back button
        try:
            driver.back()
            time.sleep(2)
            print("[BACK] ✅ Used back button")
            return True
        except:
            pass
        
        # Method 2: Home navigation
        try:
            # Look for home/main indicators
            home_patterns = [
                "//*[@content-desc='Home' or @text='Home']",
                "//*[contains(@content-desc, 'Beranda') or contains(@text, 'Beranda')]",
                "//*[@content-desc='Navigate up' or @text='Navigate up']"
            ]
            
            for pattern in home_patterns:
                elements = driver.find_elements("xpath", pattern)
                if elements:
                    elements[0].click()
                    time.sleep(2)
                    print("[BACK] ✅ Used home navigation")
                    return True
        except:
            pass
        
        print("[BACK] ⚠️ Could not navigate back")
        return False
        
    except Exception as e:
        print(f"[BACK] Error going back: {e}")
        return False

def find_submenu_items_simple(driver):
    """Find potential submenu items on current page - YOUR REQUIREMENT"""
    print(f"\n[SUBMENU] 🔍 Finding submenu items...")

    submenu_items = []

    try:
        # Get all clickable elements
        clickable_elements = driver.find_elements("xpath", "//*[@clickable='true']")

        for element in clickable_elements:
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                class_name = element.get_attribute('class') or ''

                # Filter out navigation elements and main menus
                if is_potential_submenu_simple(text, desc, class_name):
                    submenu_items.append({
                        'element': element,
                        'text': text,
                        'content_desc': desc,
                        'class': class_name
                    })

            except Exception as e:
                continue

        print(f"[SUBMENU] Found {len(submenu_items)} potential submenu items")
        return submenu_items[:10]  # Limit to first 10 to avoid too many

    except Exception as e:
        print(f"[SUBMENU] Error finding submenu items: {e}")
        return []

def is_potential_submenu_simple(text: str, desc: str, class_name: str) -> bool:
    """Check if element is a potential submenu item - YOUR REQUIREMENT"""
    # Skip empty elements
    if not text.strip() and not desc.strip():
        return False

    # Skip null elements - YOUR REQUIREMENT
    if text == 'null' or desc == 'null':
        return False

    # Skip navigation elements
    nav_keywords = ['beranda', 'home', 'back', 'kembali', 'tab', 'navigation']
    content = (text + ' ' + desc).lower()
    if any(keyword in content for keyword in nav_keywords):
        return False

    # Skip main menu items (they should be on main page)
    main_menus = ['ruang gtk', 'ruang murid', 'ruang sekolah', 'ruang bahasa']
    if any(menu in content for menu in main_menus):
        return False

    # Include if has meaningful content
    if len(text.strip()) > 2 or len(desc.strip()) > 2:
        return True

    return False

def crawl_submenus_hierarchical(driver, progress_data, progress_filename, parent_menu, submenu_locations, depth):
    """Crawl submenus with true hierarchical structure - YOUR EXACT REQUIREMENT"""
    try:
        print(f"\n[HIERARCHY] 📂 Starting hierarchical submenu crawl for {parent_menu} at depth {depth}")

        # Get submenu items
        submenu_items = []
        try:
            submenu_items = driver.find_elements("xpath", "//*[@clickable='true' and @text!='' and @text!=' ']")
            print(f"[HIERARCHY] Found {len(submenu_items)} potential submenu items")
        except Exception as e:
            print(f"[HIERARCHY] ⚠️ Error finding submenu items: {e}")
            return

        # Process each submenu
        for i, submenu_item in enumerate(submenu_items[:5]):  # Limit to first 5 for safety
            try:
                submenu_text = submenu_item.get_attribute("text") or f"Submenu_{i+1}"
                submenu_hierarchy_path = f"{parent_menu}>{submenu_text}"

                print(f"\n[HIERARCHY] 🎯 Processing submenu {i+1}/{min(len(submenu_items), 5)}: {submenu_text}")
                print(f"[HIERARCHY] 📍 Hierarchy path: {submenu_hierarchy_path}")

                # Click submenu
                try:
                    submenu_item.click()
                    print(f"[HIERARCHY] ✅ Successfully clicked submenu: {submenu_text}")

                    # Wait for page load
                    simple_wait_for_page_load(driver, timeout=5)

                    # Handle popup
                    handle_login_popup(driver)

                    # Check context
                    context, app = detect_context_and_app(driver)
                    if context == "CHROME" or context == "BROWSER":
                        print(f"[HIERARCHY] ❌ Submenu opened browser, going back...")
                        simple_go_back(driver)
                        continue

                    # 🎯 IMMEDIATE HIERARCHICAL VISIT MARKING FOR SUBMENU
                    update_hierarchical_progress(progress_data, progress_filename, submenu_hierarchy_path, visited=True)
                    print(f"[HIERARCHY] ✅ IMMEDIATELY marked submenu '{submenu_hierarchy_path}' as VISITED")

                    # Collect elements at submenu level
                    try:
                        submenu_elements, sub_submenu_locations = collect_all_elements_simple(driver, f"Submenu: {submenu_hierarchy_path}")

                        # Update hierarchical progress with collected elements
                        update_hierarchical_progress(progress_data, progress_filename, submenu_hierarchy_path, submenu_elements, visited=True)
                        print(f"[HIERARCHY] ✅ Updated submenu '{submenu_hierarchy_path}' with {len(submenu_elements)} elements")

                        # Process sub-submenus if any (depth 3)
                        if sub_submenu_locations and depth < 3:
                            print(f"[HIERARCHY] 🔍 Found sub-submenus, going deeper...")
                            crawl_subsubmenus_hierarchical(driver, progress_data, progress_filename, submenu_hierarchy_path, sub_submenu_locations)

                    except Exception as collection_error:
                        print(f"[HIERARCHY] ❌ Error collecting submenu elements: {collection_error}")

                    # Go back to parent menu
                    print(f"[HIERARCHY] 🔙 Going back from submenu: {submenu_text}")
                    simple_go_back(driver)
                    simple_wait_for_page_load(driver, timeout=3)

                except Exception as click_error:
                    print(f"[HIERARCHY] ❌ Failed to click submenu {submenu_text}: {click_error}")
                    continue

            except Exception as submenu_error:
                print(f"[HIERARCHY] ❌ Error processing submenu {i+1}: {submenu_error}")
                continue

        print(f"[HIERARCHY] ✅ Completed hierarchical submenu crawl for {parent_menu}")

    except Exception as e:
        print(f"[HIERARCHY] ❌ Error in hierarchical submenu crawl: {e}")

def crawl_subsubmenus_hierarchical(driver, progress_data, progress_filename, parent_submenu_path, subsubmenu_locations):
    """Crawl sub-submenus with hierarchical structure - YOUR EXACT REQUIREMENT"""
    try:
        print(f"\n[HIERARCHY] 📂 Starting sub-submenu crawl for {parent_submenu_path}")

        # Get sub-submenu items
        subsubmenu_items = []
        try:
            subsubmenu_items = driver.find_elements("xpath", "//*[@clickable='true' and @text!='' and @text!=' ']")
            print(f"[HIERARCHY] Found {len(subsubmenu_items)} potential sub-submenu items")
        except Exception as e:
            print(f"[HIERARCHY] ⚠️ Error finding sub-submenu items: {e}")
            return

        # Process each sub-submenu
        for i, subsubmenu_item in enumerate(subsubmenu_items[:3]):  # Limit to first 3 for safety
            try:
                subsubmenu_text = subsubmenu_item.get_attribute("text") or f"SubSubmenu_{i+1}"
                subsubmenu_hierarchy_path = f"{parent_submenu_path}>{subsubmenu_text}"

                print(f"\n[HIERARCHY] 🎯 Processing sub-submenu {i+1}/{min(len(subsubmenu_items), 3)}: {subsubmenu_text}")
                print(f"[HIERARCHY] 📍 Hierarchy path: {subsubmenu_hierarchy_path}")

                # Click sub-submenu
                try:
                    subsubmenu_item.click()
                    print(f"[HIERARCHY] ✅ Successfully clicked sub-submenu: {subsubmenu_text}")

                    # Wait for page load
                    simple_wait_for_page_load(driver, timeout=5)

                    # Handle popup
                    handle_login_popup(driver)

                    # 🎯 IMMEDIATE HIERARCHICAL VISIT MARKING FOR SUB-SUBMENU
                    update_hierarchical_progress(progress_data, progress_filename, subsubmenu_hierarchy_path, visited=True)
                    print(f"[HIERARCHY] ✅ IMMEDIATELY marked sub-submenu '{subsubmenu_hierarchy_path}' as VISITED")

                    # Collect elements at sub-submenu level (FINAL COLLECTION)
                    try:
                        subsubmenu_elements, _ = collect_all_elements_simple(driver, f"SubSubmenu: {subsubmenu_hierarchy_path}")

                        # Update hierarchical progress with collected elements
                        update_hierarchical_progress(progress_data, progress_filename, subsubmenu_hierarchy_path, subsubmenu_elements, visited=True)
                        print(f"[HIERARCHY] ✅ FINAL COLLECTION: Updated sub-submenu '{subsubmenu_hierarchy_path}' with {len(subsubmenu_elements)} elements")

                    except Exception as collection_error:
                        print(f"[HIERARCHY] ❌ Error collecting sub-submenu elements: {collection_error}")

                    # Go back to parent submenu
                    print(f"[HIERARCHY] 🔙 Going back from sub-submenu: {subsubmenu_text}")
                    simple_go_back(driver)
                    simple_wait_for_page_load(driver, timeout=3)

                except Exception as click_error:
                    print(f"[HIERARCHY] ❌ Failed to click sub-submenu {subsubmenu_text}: {click_error}")
                    continue

            except Exception as subsubmenu_error:
                print(f"[HIERARCHY] ❌ Error processing sub-submenu {i+1}: {subsubmenu_error}")
                continue

        print(f"[HIERARCHY] ✅ Completed sub-submenu crawl for {parent_submenu_path}")

    except Exception as e:
        print(f"[HIERARCHY] ❌ Error in sub-submenu crawl: {e}")

def crawl_submenus_simple(driver, parent_menu: str, submenu_locations=None, depth: int = 1, already_collected: bool = False, visited_menus: list = None):
    """Crawl through submenus hierarchically with location awareness - YOUR REQUIREMENT (Legacy)"""
    try:
        print(f"\n[SUBMENU_CRAWL] 📂 Starting submenu crawl for {parent_menu} at depth {depth}")

        # Initialize visited menus tracker if not provided
        if visited_menus is None:
            visited_menus = []

        # YOUR REQUIREMENT 1: At depth 3+ (sub-submenus), ONLY COLLECT - NO CLICKING
        if depth >= 3:
            print(f"[SUBMENU_CRAWL] 📋 At sub-submenu level (depth {depth}) - COLLECT ONLY, NO CLICKING")
            # Just collect elements, don't click any menus
            elements, locations = collect_all_elements_simple(driver, f"{parent_menu}_FINAL_COLLECTION")
            print(f"[SUBMENU_CRAWL] ✅ Final collection: {len(elements)} elements from {parent_menu}")
            return

        if depth > 3:  # Limit depth to prevent infinite recursion
            print(f"[SUBMENU_CRAWL] Max depth reached for {parent_menu}")
            return

        # YOUR FIX: Only collect elements if not already collected
        if not already_collected:
            print(f"[SUBMENU_CRAWL] 📋 First time in {parent_menu} - collecting elements...")
            # Collect elements in submenu ONCE
            submenu_elements, current_submenu_locations = collect_all_elements_simple(driver, f"{parent_menu}_SUBMENU_COLLECTION")
            print(f"[SUBMENU_CRAWL] ✅ Collected {len(submenu_elements)} elements from {parent_menu}")
            # Use the locations from collection if submenu_locations not provided
            if not submenu_locations:
                submenu_locations = current_submenu_locations
        else:
            print(f"[SUBMENU_CRAWL] ✅ Already collected elements for {parent_menu} - skipping collection")

        # YOUR REQUIREMENT 2: Scroll back to top before processing submenus
        print(f"[SUBMENU_CRAWL] 🔄 Ensuring at top before processing submenus...")
        scroll_to_top_smart(driver)

        # Find all potential submenu items
        submenus = find_submenu_items_simple(driver)

        # If we have location data, prioritize submenus with known locations - YOUR REQUIREMENT
        if submenu_locations:
            print(f"[SUBMENU_CRAWL] 📍 Using location data for {len(submenu_locations)} potential submenus")
            for submenu_name, location in submenu_locations.items():
                print(f"[SUBMENU_CRAWL]   - {submenu_name}: y={location['y']}")

        if not submenus:
            print(f"[SUBMENU_CRAWL] No submenus found in {parent_menu}")
            return

        print(f"[SUBMENU_CRAWL] Found {len(submenus)} potential submenus in {parent_menu}")

        # YOUR FIX: Show visited menus status
        if visited_menus:
            print(f"[SUBMENU_CRAWL] 📋 Already visited menus: {visited_menus}")

        # Navigate through each submenu
        for i, submenu_info in enumerate(submenus, 1):
            try:
                # Extract clean submenu name - FIXES EMPTY NAMES
                submenu_name = submenu_info.get('text', submenu_info.get('content_desc', ''))
                if not submenu_name or submenu_name.strip() == "":
                    submenu_name = f'Submenu_{i}'

                # Truncate long names
                if len(submenu_name) > 50:
                    submenu_name = submenu_name[:47] + "..."

                # YOUR FIX: Skip already visited menus (popup recovery)
                if submenu_name in visited_menus:
                    print(f"[SUBMENU_CRAWL] ✅ Skipping already visited menu: {submenu_name}")
                    continue

                print(f"\n[SUBMENU_CRAWL] Clicking submenu {i}/{len(submenus)}: {submenu_name}")

                # Click the submenu
                if click_submenu_item_simple(driver, submenu_info):
                    # Wait for page to load
                    simple_wait_for_page_load(driver, timeout=5)

                    # Check context after navigation - PREVENT CHROME COLLECTION
                    context, app = detect_context_and_app(driver)
                    if context == "CHROME" or context == "BROWSER":
                        print(f"[SUBMENU_CRAWL] ❌ In external browser, skipping collection and going back...")
                        simple_go_back(driver)
                        simple_wait_for_page_load(driver, timeout=5)
                        # YOUR FIX: Mark as visited even if it opened external browser
                        visited_menus.append(submenu_name)
                        continue

                    # YOUR FIX: Mark menu as visited BEFORE processing (in case of popup)
                    visited_menus.append(submenu_name)
                    print(f"[SUBMENU_CRAWL] ✅ Marked {submenu_name} as visited")

                    # YOUR FIX: Check if this is the same page (popup scenario)
                    # If popup was closed and we're still on same page, DON'T collect again
                    is_same_page = False
                    try:
                        # Check if we're still on the submenu page by looking for GTK indicator
                        gtk_elements = driver.find_elements("xpath", "//*[contains(@text, 'Ruang Guru dan Tenaga Kependidikan')]")
                        if gtk_elements:
                            print(f"[SUBMENU_CRAWL] 🎯 Still on same submenu page after popup - skipping sub-crawl")
                            print(f"[SUBMENU_CRAWL] 🔄 Continuing to next menu instead...")
                            is_same_page = True
                        else:
                            print(f"[SUBMENU_CRAWL] 📂 Navigated to different page - doing sub-crawl...")
                    except Exception as e:
                        print(f"[SUBMENU_CRAWL] ⚠️ Could not check current page: {e}")
                        # If we can't check, assume it's a different page and proceed with caution
                        if not check_server_health(driver):
                            print(f"[SUBMENU_CRAWL] ❌ Server unhealthy, skipping sub-crawl")
                            is_same_page = True

                    # YOUR FIX: Only do recursive crawl if we actually navigated to a different page
                    if not is_same_page:
                        try:
                            # Recursively crawl sub-submenus - YOUR REQUIREMENT
                            # Note: This will collect elements in the sub-submenu page
                            crawl_submenus_simple(driver, f"{parent_menu} > {submenu_name}", None, depth + 1, False, [])

                            # Go back to parent menu
                            simple_go_back(driver)
                            simple_wait_for_page_load(driver, timeout=5)
                        except Exception as sub_crawl_error:
                            print(f"[SUBMENU_CRAWL] ⚠️ Error in sub-crawl: {sub_crawl_error}")
                            # Try to recover by going back
                            try:
                                simple_go_back(driver)
                                simple_wait_for_page_load(driver, timeout=3)
                            except:
                                print(f"[SUBMENU_CRAWL] ⚠️ Could not recover from sub-crawl error")

                    # YOUR FIX: When we come back, we've already collected, so set flag to True
                    already_collected = True

                    # YOUR NEW REQUIREMENT: After back action, automatically continue to next menu
                    print(f"[SUBMENU_CRAWL] 🔄 Back action completed - preparing to continue to next menu...")

                    # Scroll to make sure next menus are visible if needed
                    try:
                        scroll_to_top_smart(driver)
                        print(f"[SUBMENU_CRAWL] 📜 Scrolled to ensure next menus are visible")
                    except Exception as scroll_error:
                        print(f"[SUBMENU_CRAWL] ⚠️ Scroll after back failed: {scroll_error}")

                    # Continue to next iteration (next menu will be processed automatically)

                else:
                    print(f"[SUBMENU_CRAWL] Could not click submenu: {submenu_name}")
                    # YOUR FIX: Mark as visited even if click failed
                    visited_menus.append(submenu_name)

            except Exception as e:
                print(f"[SUBMENU_CRAWL] Error processing submenu {i}: {e}")
                # YOUR FIX: Mark as visited even if error occurred
                if submenu_name not in visited_menus:
                    visited_menus.append(submenu_name)
                # Try to recover by going back
                simple_go_back(driver)
                time.sleep(1)
                continue

        print(f"[SUBMENU_CRAWL] ✅ Completed submenu crawl for {parent_menu}")

    except Exception as e:
        print(f"[SUBMENU_CRAWL] Error in submenu crawl for {parent_menu}: {e}")

def click_submenu_item_simple(driver, submenu_info: dict) -> bool:
    """Click a submenu item with context detection - YOUR REQUIREMENT"""
    try:
        element = submenu_info['element']
        name = submenu_info.get('text', submenu_info.get('content_desc', 'Unknown'))

        # Fix empty names
        if not name or name.strip() == "" or name == "Unknown":
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                name = text if text and text != 'null' else desc
                if not name or name == 'null':
                    name = f"Element_{element.location['x']}_{element.location['y']}"
            except:
                name = "UnknownElement"

        print(f"[CLICK_SUBMENU] Attempting to click: {name}")

        # Method 1: Regular click
        try:
            element.click()
            time.sleep(3)  # Longer wait for page load

            # YOUR REQUIREMENT: Handle popup after click
            handle_login_popup(driver)

            # Check context after click - PREVENT CHROME OPENING
            context, app = detect_context_and_app(driver)
            if context == "CHROME" or context == "BROWSER":
                print(f"[CLICK_SUBMENU] ❌ Opened external browser, going back...")
                simple_go_back(driver)
                time.sleep(2)
                return False

            print(f"[CLICK_SUBMENU] ✅ Successfully clicked submenu: {name}")
            return True
        except:
            pass

        # Method 2: Tap using coordinates
        try:
            location = element.location
            size = element.size
            x = location['x'] + size['width'] // 2
            y = location['y'] + size['height'] // 2

            driver.tap([(x, y)])
            time.sleep(3)  # Longer wait for page load

            # Check context after tap - PREVENT CHROME OPENING
            context, app = detect_context_and_app(driver)
            if context == "CHROME" or context == "BROWSER":
                print(f"[CLICK_SUBMENU] ❌ Opened external browser, going back...")
                simple_go_back(driver)
                time.sleep(2)
                return False

            print(f"[CLICK_SUBMENU] ✅ Successfully clicked submenu: {name} (tap)")
            return True
        except:
            pass

        print(f"[CLICK_SUBMENU] ❌ Failed to click submenu: {name}")
        return False

    except Exception as e:
        print(f"[CLICK_SUBMENU] Error clicking submenu: {e}")
        return False

def go_back_to_main_simple(driver):
    """Navigate back to main page from any submenu - YOUR REQUIREMENT"""
    try:
        print("\n[NAVIGATE_MAIN] 🏠 Navigating back to main page...")

        max_attempts = 3
        for attempt in range(max_attempts):
            # Try to go back
            if simple_go_back(driver):
                time.sleep(2)

                # Check if we're on main page by looking for main menu items
                main_indicators = [
                    "//*[@text='Ruang GTK']",
                    "//*[@text='Ruang Murid']",
                    "//*[@text='Ruang Sekolah']"
                ]

                main_elements_found = 0
                for indicator in main_indicators:
                    try:
                        elements = driver.find_elements("xpath", indicator)
                        if elements:
                            main_elements_found += 1
                    except:
                        continue

                if main_elements_found >= 2:
                    print(f"[NAVIGATE_MAIN] ✅ Successfully reached main page (attempt {attempt + 1})")

                    # YOUR REQUIREMENT: Go back to top after completing menu
                    print(f"[NAVIGATE_MAIN] 🔄 Ensuring we're at top for next menu...")
                    scroll_to_top_smart(driver)

                    return True
                else:
                    print(f"[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt {attempt + 1})")
            else:
                print(f"[NAVIGATE_MAIN] Back navigation failed (attempt {attempt + 1})")

        print("[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts")
        return False

    except Exception as e:
        print(f"[NAVIGATE_MAIN] Error navigating to main: {e}")
        return False

def run_hierarchical_crawl_with_progress(driver, progress_data, progress_filename):
    """Run the complete hierarchical crawl with real-time progress tracking - YOUR EXACT REQUIREMENT"""
    print("\n" + "="*80)
    print("🎯 STARTING HIERARCHICAL CRAWL WITH PROGRESS TRACKING - YOUR EXACT FLOW")
    print("="*80)
    print("Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu")
    print("Real-time JSON sync for visited status tracking")
    print("="*80)

    all_collected_data = {}

    try:
        # Check for popup on startup
        handle_login_popup(driver)

        # Step 1: Collect main page elements and store in progress
        print("\n📋 STEP 1: Collecting all elements on MAIN PAGE")
        main_elements, main_menu_locations = collect_all_elements_simple(driver, "Main Page")
        all_collected_data["Main Page"] = main_elements

        # Update progress with main page elements using hierarchical structure
        update_hierarchical_progress(progress_data, progress_filename, "Main Page", main_elements, visited=True, level_type="main_page")

        # Step 2: Extract main menus and initialize in progress file
        main_menus = [name for name in main_menu_locations.keys()
                     if name in ['Ruang GTK', 'Ruang Murid', 'Ruang Sekolah', 'Ruang Bahasa',
                                'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']]

        print(f"\n🎯 STEP 2: Found {len(main_menus)} main menus to crawl")

        # Initialize all menus in hierarchical progress file
        for menu_name in main_menus:
            update_hierarchical_progress(progress_data, progress_filename, menu_name, visited=False)

        # Step 3: Process each menu with progress tracking - SMART SELECTION FROM JSON
        for menu_name in main_menus:
            try:
                # 🎯 SMART MENU SELECTION FROM HIERARCHICAL JSON - YOUR REQUIREMENT
                # Check if menu already visited in hierarchical structure
                menu_data = progress_data.get("hierarchy", {}).get(menu_name, {})
                if menu_data.get("visited", False):
                    print(f"\n✅ MENU {menu_name} already visited in hierarchical JSON - skipping")
                    print(f"[HIERARCHY] 📅 Visit timestamp: {menu_data.get('visit_timestamp', 'Unknown')}")
                    print(f"[HIERARCHY] 📊 Elements collected: {len(menu_data.get('elements', []))}")
                    print(f"[HIERARCHY] 📊 Submenus: {len(menu_data.get('submenus', {}))}")
                    continue

                print(f"\n{'='*60}")
                print(f"🎯 PROCESSING MENU: {menu_name}")
                print(f"📊 PROGRESS: Checking real-time status from JSON...")

                # Get current progress stats
                stats = progress_data.get("statistics", {})
                print(f"📊 Current: {stats.get('visited_menus', 0)}/{stats.get('total_menus', 0)} menus, {stats.get('visited_elements', 0)}/{stats.get('total_elements', 0)} elements")

                # Show remaining unvisited menus
                summary = get_progress_summary(progress_data)
                if summary.get("unvisited_menus"):
                    print(f"📋 Remaining unvisited: {summary['unvisited_menus']}")
                print(f"{'='*60}")

                # Click menu using smart location-based approach
                if simple_smart_click_menu(driver, menu_name, main_menu_locations):
                    # Wait for page to load
                    simple_wait_for_page_load(driver, timeout=10)

                    # Check for popup after clicking menu
                    popup_result = handle_login_popup(driver)

                    # Check context to ensure we're in the right place
                    context, app = detect_context_and_app(driver)
                    if context == "CHROME" or context == "BROWSER":
                        print(f"❌ Menu {menu_name} opened external browser, skipping...")
                        simple_go_back(driver)
                        continue

                    print(f"✅ Successfully navigated to {menu_name}")

                    # 🎯 IMMEDIATE HIERARCHICAL VISIT MARKING - YOUR REQUIREMENT
                    # Mark menu as visited immediately upon successful navigation
                    update_hierarchical_progress(progress_data, progress_filename, menu_name, visited=True)
                    print(f"[HIERARCHY] ✅ IMMEDIATELY marked '{menu_name}' as VISITED upon navigation")

                    # Collect elements and update progress
                    try:
                        menu_elements, submenu_locations = collect_all_elements_simple(driver, f"Menu: {menu_name}")
                        all_collected_data[f"Menu: {menu_name}"] = menu_elements

                        # Update hierarchical progress with collected elements (keeping visited=True)
                        update_hierarchical_progress(progress_data, progress_filename, menu_name, menu_elements, visited=True)

                        print(f"[HIERARCHY] ✅ Updated '{menu_name}' with {len(menu_elements)} collected elements")

                        # Process submenus if any using hierarchical structure
                        if submenu_locations:
                            print(f"\n🔍 Starting hierarchical submenu crawl for {menu_name}...")
                            crawl_submenus_hierarchical(driver, progress_data, progress_filename, menu_name, submenu_locations, 1)

                    except Exception as collection_error:
                        print(f"❌ Error collecting elements from {menu_name}: {collection_error}")

                    # Go back to main page
                    print(f"\n🔙 Going back to main page from {menu_name}...")
                    go_back_to_main_simple(driver)

                    print(f"✅ Completed crawling menu: {menu_name}")

                    # 🎯 CHECK NEXT UNVISITED MENU FROM JSON - YOUR REQUIREMENT
                    next_unvisited = get_next_unvisited_menu(progress_data)
                    if next_unvisited:
                        print(f"[PROGRESS] 🎯 Next unvisited menu from JSON: {next_unvisited}")
                        print(f"[PROGRESS] 📋 Preparing to navigate to: {next_unvisited}")
                    else:
                        print(f"[PROGRESS] ✅ All menus have been visited according to JSON!")

                else:
                    print(f"❌ Could not click menu: {menu_name}")

            except Exception as menu_error:
                print(f"❌ Error processing menu {menu_name}: {menu_error}")

        # Final progress summary
        print(f"\n📊 FINAL PROGRESS SUMMARY:")
        stats = progress_data.get("statistics", {})
        print(f"  - Total menus: {stats.get('total_menus', 0)}")
        print(f"  - Visited menus: {stats.get('visited_menus', 0)}")
        print(f"  - Total elements: {stats.get('total_elements', 0)}")
        print(f"  - Visited elements: {stats.get('visited_elements', 0)}")

        # Mark session as complete
        progress_data["session_info"]["status"] = "completed"
        save_progress_file(progress_data, progress_filename)

        # Save final collected data
        save_collected_data_simple(all_collected_data)

        print("\n" + "="*80)
        print("🎉 HIERARCHICAL CRAWL WITH PROGRESS TRACKING COMPLETE!")
        print("="*80)
        print(f"✅ Crawled {len(main_menus)} main menus")
        print(f"✅ Collected data from {len(all_collected_data)} pages")
        print(f"✅ Real-time progress saved to: {progress_filename}")
        print("="*80)

        return all_collected_data

    except Exception as e:
        print(f"❌ Error in hierarchical crawl with progress: {e}")
        # Mark session as failed
        progress_data["session_info"]["status"] = "failed"
        save_progress_file(progress_data, progress_filename)
        return all_collected_data

def run_hierarchical_crawl_simple(driver):
    """Run the complete hierarchical crawl with visited status tracking - YOUR EXACT REQUIREMENT"""
    print("\n" + "="*80)
    print("🎯 STARTING HIERARCHICAL CRAWL - YOUR EXACT FLOW")
    print("="*80)
    print("Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu")
    print("="*80)

    all_collected_data = {}
    visited_status = {}  # Track visited pages and elements

    try:
        # Step 1: Collect all elements on main page first
        print("\n📋 STEP 1: Collecting all elements on MAIN PAGE")
        main_page_elements, main_menu_locations = collect_all_elements_simple(driver, "Main Page")
        all_collected_data["Main Page"] = main_page_elements

        # Mark main page as visited - YOUR REQUIREMENT
        visited_status["Main Page"] = True
        print(f"✅ Main Page marked as VISITED")

        # Mark each main page element as visited - YOUR REQUIREMENT
        for i, element in enumerate(main_page_elements):
            element_key = f"Main Page_element_{i}"
            visited_status[element_key] = True

        # Store menu locations for smart clicking - YOUR REQUIREMENT
        print(f"[LOCATION] 📍 Stored locations for {len(main_menu_locations)} menus")
        for menu_name, location in main_menu_locations.items():
            print(f"[LOCATION]   - {menu_name}: y={location['y']}, center=({location['center_x']}, {location['center_y']})")

        # Step 2: Find all main menus
        main_menus = [
            "Ruang GTK", "Ruang Murid", "Ruang Sekolah", "Ruang Bahasa",
            "Ruang Pemerintah", "Ruang Mitra", "Ruang Publik", "Ruang Orang Tua"
        ]

        print(f"\n🎯 STEP 2: Found {len(main_menus)} main menus to crawl")

        # YOUR REQUIREMENT 3: Menu progress tracking system
        menu_progress = {
            'total_menus': len(main_menus),
            'completed_menus': [],
            'failed_menus': [],
            'current_menu': None,
            'remaining_menus': main_menus.copy()
        }

        print(f"\n📊 MENU PROGRESS TRACKING INITIALIZED:")
        print(f"  - Total menus to process: {menu_progress['total_menus']}")
        print(f"  - Remaining menus: {menu_progress['remaining_menus']}")

        # Step 3: Click each menu and follow your exact flow - WITH PROGRESS TRACKING
        for menu_index, menu_name in enumerate(main_menus, 1):
            try:
                # YOUR REQUIREMENT 3: Update progress tracking
                menu_progress['current_menu'] = menu_name
                print(f"\n" + "="*60)
                print(f"🎯 MENU {menu_index}/{len(main_menus)}: {menu_name}")
                print(f"📊 PROGRESS: Completed={len(menu_progress['completed_menus'])}, Failed={len(menu_progress['failed_menus'])}, Remaining={len(menu_progress['remaining_menus'])}")
                print("="*60)

                # Health check before each menu - PREVENT CRASHES
                if not check_server_health(driver):
                    print(f"❌ Server unhealthy before menu {menu_name}, skipping...")
                    menu_progress['failed_menus'].append(menu_name)
                    menu_progress['remaining_menus'].remove(menu_name)
                    continue

                # YOUR REQUIREMENT: "when click menu 1 in main page will directly into another page or submenu"
                # Use menu location for smart clicking - YOUR REQUIREMENT
                click_success = simple_smart_click_menu(driver, menu_name, main_menu_locations)

                if click_success:
                    # Wait for page to load
                    simple_wait_for_page_load(driver, timeout=10)

                    # Check context after main menu click - PREVENT CHROME
                    context, app = detect_context_and_app(driver)
                    if context == "CHROME" or context == "BROWSER":
                        print(f"❌ Main menu {menu_name} opened external browser, going back...")
                        simple_go_back(driver)
                        menu_progress['failed_menus'].append(menu_name)
                        menu_progress['remaining_menus'].remove(menu_name)
                        continue

                    # Health check after navigation
                    if not check_server_health(driver):
                        print(f"❌ Server unhealthy after clicking {menu_name}, going back...")
                        simple_go_back(driver)
                        menu_progress['failed_menus'].append(menu_name)
                        menu_progress['remaining_menus'].remove(menu_name)
                        continue

                    # YOUR REQUIREMENT: "after that collect all element locators in the other page"
                    # YOUR REQUIREMENT: "and also you need to scroll down step by step to make element visible"
                    try:
                        menu_elements, submenu_locations = collect_all_elements_simple(driver, f"Menu: {menu_name}")
                        all_collected_data[f"Menu: {menu_name}"] = menu_elements

                        # Store submenu locations for hierarchical navigation - YOUR REQUIREMENT
                        print(f"[LOCATION] 📍 Found {len(submenu_locations)} submenu locations in {menu_name}")

                        # YOUR REQUIREMENT: "when all element locators for submenu already collected"
                        # YOUR REQUIREMENT: "the code need to scroll to top and start click the submenu 1, 2, 3, n"
                        print(f"\n🔍 Starting submenu crawl for {menu_name}...")
                        # YOUR FIX: Start with already_collected=True since we just collected elements above
                        # Initialize empty visited_menus list for popup recovery
                        crawl_submenus_simple(driver, menu_name, submenu_locations, 1, True, [])

                    except Exception as collection_error:
                        print(f"❌ Error collecting elements from {menu_name}: {collection_error}")

                    # YOUR REQUIREMENT: "after this you need to go back to main page"
                    print(f"\n🔙 Going back to main page from {menu_name}...")
                    go_back_to_main_simple(driver)

                    # Health check after going back
                    if not check_server_health(driver):
                        print(f"❌ Server unhealthy after going back from {menu_name}")
                        break  # Stop processing if server is unstable

                    # YOUR REQUIREMENT 3: Mark menu as completed
                    menu_progress['completed_menus'].append(menu_name)
                    menu_progress['remaining_menus'].remove(menu_name)
                    print(f"✅ Completed crawling menu: {menu_name}")
                    print(f"📊 UPDATED PROGRESS: {len(menu_progress['completed_menus'])}/{menu_progress['total_menus']} menus completed")

                    # YOUR NEW REQUIREMENT: After back action, automatically prepare for next menu
                    if menu_progress['remaining_menus']:  # Only if there are more menus to process
                        next_menu = menu_progress['remaining_menus'][0]
                        print(f"[NEXT_MENU] 🎯 Preparing for next menu: {next_menu}")

                        # Scroll to make next menu visible if needed
                        try:
                            scroll_to_top_smart(driver)
                            print(f"[NEXT_MENU] 📜 Scrolled to prepare for next menu: {next_menu}")
                        except Exception as scroll_error:
                            print(f"[NEXT_MENU] ⚠️ Scroll preparation failed: {scroll_error}")
                    else:
                        print(f"[NEXT_MENU] 🏁 No more menus to process - automation complete")

                else:
                    print(f"❌ Could not click menu: {menu_name}")
                    menu_progress['failed_menus'].append(menu_name)
                    menu_progress['remaining_menus'].remove(menu_name)

            except Exception as e:
                print(f"❌ Error processing menu {menu_name}: {e}")
                menu_progress['failed_menus'].append(menu_name)
                if menu_name in menu_progress['remaining_menus']:
                    menu_progress['remaining_menus'].remove(menu_name)
                # Try to recover by going back to main
                try:
                    go_back_to_main_simple(driver)
                except:
                    print(f"❌ Could not recover from error in {menu_name}")
                    break  # Stop if we can't recover
                continue

        # YOUR REQUIREMENT 3: Final progress summary
        print(f"\n📊 FINAL MENU PROGRESS SUMMARY:")
        print(f"  - Total menus: {menu_progress['total_menus']}")
        print(f"  - Successfully completed: {len(menu_progress['completed_menus'])}")
        print(f"  - Failed: {len(menu_progress['failed_menus'])}")
        print(f"  - Remaining: {len(menu_progress['remaining_menus'])}")
        print(f"  - Completed menus: {menu_progress['completed_menus']}")
        print(f"  - Failed menus: {menu_progress['failed_menus']}")
        print(f"  - Remaining menus: {menu_progress['remaining_menus']}")

        # Step 4: Save all collected data
        save_collected_data_simple(all_collected_data)

        print("\n" + "="*80)
        print("🎉 HIERARCHICAL CRAWL COMPLETE!")
        print("="*80)
        print(f"✅ Crawled {len(main_menus)} main menus")
        print(f"✅ Collected data from {len(all_collected_data)} pages")
        print(f"✅ Followed your exact flow: Main → Menu → Submenu → Sub-submenu → Back")
        print("="*80)

        return all_collected_data

    except Exception as e:
        print(f"❌ Error in hierarchical crawl: {e}")
        return all_collected_data

def load_progress_file():
    """Load existing progress file or create new one - YOUR REQUIREMENT"""
    try:
        from datetime import datetime
        import os

        # Look for existing progress file
        progress_files = [f for f in os.listdir('.') if f.startswith('automation_progress_') and f.endswith('.json')]

        if progress_files:
            # Use the most recent progress file
            latest_file = max(progress_files, key=lambda x: os.path.getctime(x))
            print(f"[PROGRESS] 📂 Loading existing progress file: {latest_file}")

            with open(latest_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            # Ensure hierarchy key exists for backward compatibility
            if "hierarchy" not in progress_data:
                progress_data["hierarchy"] = {}
                print(f"[PROGRESS] 🔧 Added hierarchy structure to existing progress file")

            print(f"[PROGRESS] ✅ Loaded progress with {len(progress_data.get('menus', {}))} menus")
            return progress_data, latest_file
        else:
            # Create new progress file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"automation_progress_{timestamp}.json"

            progress_data = {
                "session_info": {
                    "created": timestamp,
                    "last_updated": timestamp,
                    "status": "in_progress",
                    "app_package": "com.kemendikdasmen.rumahpendidikan"
                },
                "hierarchy": {},  # NEW: Hierarchical structure
                "menus": {},      # LEGACY: Keep for compatibility
                "statistics": {
                    "total_menus": 0,
                    "visited_menus": 0,
                    "total_elements": 0,
                    "visited_elements": 0,
                    "total_submenus": 0,
                    "visited_submenus": 0,
                    "total_subsubmenus": 0,
                    "visited_subsubmenus": 0
                }
            }

            print(f"[PROGRESS] 📝 Created new progress file: {filename}")
            return progress_data, filename

    except Exception as e:
        print(f"[PROGRESS] ❌ Error loading progress file: {e}")
        return {}, "automation_progress_fallback.json"

def save_progress_file(progress_data, filename):
    """Save progress file in real-time - YOUR REQUIREMENT"""
    try:
        from datetime import datetime

        # Update timestamp
        progress_data["session_info"]["last_updated"] = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Update statistics from hierarchical structure
        def count_hierarchy_stats(level_data, level_type="menu"):
            stats = {
                "total": 0, "visited": 0, "total_elements": 0, "visited_elements": 0,
                "submenus": {"total": 0, "visited": 0, "total_elements": 0, "visited_elements": 0},
                "subsubmenus": {"total": 0, "visited": 0, "total_elements": 0, "visited_elements": 0}
            }

            for name, data in level_data.items():
                stats["total"] += 1
                if data.get("visited", False):
                    stats["visited"] += 1

                # Count elements at this level
                elements = data.get("elements", [])
                stats["total_elements"] += len(elements)
                stats["visited_elements"] += sum(1 for elem in elements if elem.get("visited", False))

                # Count submenus recursively
                if "submenus" in data and data["submenus"]:
                    sub_stats = count_hierarchy_stats(data["submenus"], "submenu")
                    stats["submenus"]["total"] += sub_stats["total"]
                    stats["submenus"]["visited"] += sub_stats["visited"]
                    stats["submenus"]["total_elements"] += sub_stats["total_elements"]
                    stats["submenus"]["visited_elements"] += sub_stats["visited_elements"]

                    # Add sub-submenus
                    stats["subsubmenus"]["total"] += sub_stats["submenus"]["total"]
                    stats["subsubmenus"]["visited"] += sub_stats["submenus"]["visited"]
                    stats["subsubmenus"]["total_elements"] += sub_stats["submenus"]["total_elements"]
                    stats["subsubmenus"]["visited_elements"] += sub_stats["submenus"]["visited_elements"]

            return stats

        hierarchy_stats = count_hierarchy_stats(progress_data.get("hierarchy", {}))

        progress_data["statistics"] = {
            "total_menus": hierarchy_stats["total"],
            "visited_menus": hierarchy_stats["visited"],
            "total_elements": hierarchy_stats["total_elements"],
            "visited_elements": hierarchy_stats["visited_elements"],
            "total_submenus": hierarchy_stats["submenus"]["total"],
            "visited_submenus": hierarchy_stats["submenus"]["visited"],
            "total_subsubmenus": hierarchy_stats["subsubmenus"]["total"],
            "visited_subsubmenus": hierarchy_stats["subsubmenus"]["visited"]
        }

        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)

        print(f"[PROGRESS] 💾 Progress saved: {visited_menus}/{total_menus} menus, {visited_elements}/{total_elements} elements")

    except Exception as e:
        print(f"[PROGRESS] ❌ Error saving progress: {e}")

def update_hierarchical_progress(progress_data, filename, hierarchy_path, elements=None, visited=False, level_type="menu"):
    """Update hierarchical progress with proper nesting - YOUR REQUIREMENT"""
    try:
        from datetime import datetime

        # Parse hierarchy path (e.g., "Menu1", "Menu1>Submenu1", "Menu1>Submenu1>SubSubmenu1")
        path_parts = hierarchy_path.split(">")

        # Navigate to the correct nested location
        current_level = progress_data["hierarchy"]

        for i, part in enumerate(path_parts):
            part = part.strip()

            # Create the structure if it doesn't exist
            if part not in current_level:
                current_level[part] = {
                    "visited": False,
                    "elements": [],
                    "submenus": {},
                    "visit_timestamp": None,
                    "level": i + 1,
                    "type": "main_menu" if i == 0 else "submenu" if i == 1 else "subsubmenu"
                }

            # If this is the target level, update it
            if i == len(path_parts) - 1:
                target_data = current_level[part]

                # Update elements if provided
                if elements:
                    target_data["elements"] = []
                    for j, elem in enumerate(elements):
                        element_data = {
                            "index": j + 1,
                            "text": elem.get("text", ""),
                            "type": elem.get("type", ""),
                            "clickable": elem.get("clickable", False),
                            "visited": False,
                            "location": elem.get("location", {}),
                            "hierarchy_path": hierarchy_path
                        }
                        target_data["elements"].append(element_data)

                # Update visited status
                if visited:
                    target_data["visited"] = True
                    target_data["visit_timestamp"] = datetime.now().strftime("%Y%m%d_%H%M%S")

                print(f"[HIERARCHY] ✅ Updated '{hierarchy_path}' - Level: {target_data['level']}, Visited: {target_data['visited']}")

            else:
                # Move to next level
                if "submenus" not in current_level[part]:
                    current_level[part]["submenus"] = {}
                current_level = current_level[part]["submenus"]

        # Save immediately
        save_progress_file(progress_data, filename)

    except Exception as e:
        print(f"[HIERARCHY] ❌ Error updating hierarchical progress: {e}")

def get_next_unvisited_in_hierarchy(progress_data, parent_path=""):
    """Get next unvisited item in hierarchy - YOUR REQUIREMENT"""
    try:
        def search_level(level_data, current_path=""):
            for name, data in level_data.items():
                full_path = f"{current_path}>{name}" if current_path else name

                # If this level is not visited, return it
                if not data.get("visited", False):
                    return full_path, data.get("level", 1)

                # If this level is visited but has unvisited submenus, search deeper
                if "submenus" in data and data["submenus"]:
                    result = search_level(data["submenus"], full_path)
                    if result:
                        return result

            return None

        # Start search from hierarchy root or specific parent
        if parent_path:
            # Navigate to parent path first
            path_parts = parent_path.split(">")
            current_level = progress_data["hierarchy"]

            for part in path_parts:
                part = part.strip()
                if part in current_level and "submenus" in current_level[part]:
                    current_level = current_level[part]["submenus"]
                else:
                    return None

            return search_level(current_level, parent_path)
        else:
            return search_level(progress_data.get("hierarchy", {}))

    except Exception as e:
        print(f"[HIERARCHY] ❌ Error getting next unvisited: {e}")
        return None

def update_menu_progress(progress_data, filename, menu_name, elements=None, visited=False, submenus=None):
    """Update menu progress in real-time - YOUR REQUIREMENT (Legacy compatibility)"""
    try:
        # Convert to hierarchical format
        update_hierarchical_progress(progress_data, filename, menu_name, elements, visited)

    except Exception as e:
        print(f"[PROGRESS] ❌ Error updating menu progress: {e}")

def get_next_unvisited_menu(progress_data):
    """Get next unvisited menu - YOUR REQUIREMENT"""
    try:
        for menu_name, menu_data in progress_data.get("menus", {}).items():
            if not menu_data.get("visited", False):
                return menu_name
        return None
    except Exception as e:
        print(f"[PROGRESS] ❌ Error getting next menu: {e}")
        return None

def mark_element_visited(progress_data, filename, menu_name, element_index):
    """Mark specific element as visited - YOUR REQUIREMENT"""
    try:
        if menu_name in progress_data["menus"]:
            elements = progress_data["menus"][menu_name].get("elements", [])
            if 0 <= element_index < len(elements):
                from datetime import datetime
                elements[element_index]["visited"] = True
                elements[element_index]["visit_timestamp"] = datetime.now().strftime("%Y%m%d_%H%M%S")

                # Save immediately
                save_progress_file(progress_data, filename)

                print(f"[PROGRESS] ✅ Marked element {element_index + 1} in '{menu_name}' as visited")
                return True
        return False
    except Exception as e:
        print(f"[PROGRESS] ❌ Error marking element visited: {e}")
        return False

def get_next_unvisited_element(progress_data, menu_name):
    """Get next unvisited element in a menu - YOUR REQUIREMENT"""
    try:
        if menu_name in progress_data["menus"]:
            elements = progress_data["menus"][menu_name].get("elements", [])
            for i, elem in enumerate(elements):
                if not elem.get("visited", False) and elem.get("clickable", False):
                    return i, elem
        return None, None
    except Exception as e:
        print(f"[PROGRESS] ❌ Error getting next element: {e}")
        return None, None

def get_progress_summary(progress_data):
    """Get comprehensive progress summary - YOUR REQUIREMENT"""
    try:
        stats = progress_data.get("statistics", {})
        menus = progress_data.get("menus", {})

        summary = {
            "total_menus": len(menus),
            "visited_menus": sum(1 for menu in menus.values() if menu.get("visited", False)),
            "unvisited_menus": [name for name, menu in menus.items() if not menu.get("visited", False)],
            "total_elements": sum(len(menu.get("elements", [])) for menu in menus.values()),
            "visited_elements": sum(
                sum(1 for elem in menu.get("elements", []) if elem.get("visited", False))
                for menu in menus.values()
            ),
            "session_status": progress_data.get("session_info", {}).get("status", "unknown")
        }

        return summary
    except Exception as e:
        print(f"[PROGRESS] ❌ Error getting progress summary: {e}")
        return {}

def save_collected_data_simple(all_data, visited_status=None):
    """Save collected data to JSON file with visited status tracking - YOUR REQUIREMENT"""
    try:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"locators/simplified_hierarchical_collection_{timestamp}.json"

        # Create locators directory if it doesn't exist
        os.makedirs("locators", exist_ok=True)

        # Prepare data for saving with visited status
        save_data = {
            "timestamp": timestamp,
            "collection_type": "hierarchical_simplified_with_visited_tracking",
            "total_pages": len(all_data),
            "visited_status": visited_status or {},
            "pages": {},
            "features": [
                "Menu location tracking",
                "Smart scroll to menu position",
                "Hierarchical navigation",
                "Null element filtering",
                "Step-by-step scrolling",
                "Visited status tracking"
            ]
        }

        total_elements = 0
        visited_pages = 0

        for page_name, elements in all_data.items():
            # Check if this page/menu was visited
            page_visited = visited_status.get(page_name, False) if visited_status else False
            if page_visited:
                visited_pages += 1

            save_data["pages"][page_name] = {
                "element_count": len(elements),
                "visited": page_visited,
                "visit_timestamp": datetime.now().isoformat() if page_visited else None,
                "status": "✅ DONE" if page_visited else "⏳ PENDING",
                "elements": []
            }

            # Process each element with visited status
            for i, element in enumerate(elements):
                element_key = f"{page_name}_element_{i}"
                element_visited = visited_status.get(element_key, False) if visited_status else False

                # Create element with visited status
                if isinstance(element, dict):
                    element_with_status = element.copy()
                else:
                    element_with_status = {
                        "text": str(element),
                        "type": "text"
                    }

                element_with_status.update({
                    "element_id": element_key,
                    "visited": element_visited,
                    "visit_timestamp": datetime.now().isoformat() if element_visited else None,
                    "status": "✅ DONE" if element_visited else "⏳ PENDING"
                })

                save_data["pages"][page_name]["elements"].append(element_with_status)

            total_elements += len(elements)

        # Add summary statistics
        save_data["total_elements"] = total_elements
        save_data["visited_summary"] = {
            "total_pages": len(all_data),
            "visited_pages": visited_pages,
            "pending_pages": len(all_data) - visited_pages,
            "completion_percentage": round((visited_pages / len(all_data) * 100), 2) if all_data else 0,
            "status_overview": f"{visited_pages}/{len(all_data)} pages completed"
        }

        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Data saved to: {filename}")
        print(f"📊 Total elements collected: {total_elements}")
        print(f"✅ Visited pages: {visited_pages}/{len(all_data)}")
        print(f"📈 Completion: {save_data['visited_summary']['completion_percentage']}%")
        print(f"📋 Status: {save_data['visited_summary']['status_overview']}")

        return filename

    except Exception as e:
        print(f"❌ Error saving data: {e}")
        return None

def setup_logging():
    """Setup comprehensive logging to file - YOUR REQUIREMENT"""
    import sys
    from datetime import datetime

    # Create logs directory if it doesn't exist
    import os
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/analyze_{timestamp}.txt"

    # Create a custom logger that writes to both console and file
    class TeeLogger:
        def __init__(self, filename):
            self.terminal = sys.stdout
            self.log = open(filename, "w", encoding='utf-8')

        def write(self, message):
            self.terminal.write(message)
            self.log.write(message)
            self.log.flush()  # Ensure immediate write

        def flush(self):
            self.terminal.flush()
            self.log.flush()

    # Redirect stdout to our logger
    sys.stdout = TeeLogger(log_filename)
    print(f"📝 Logging enabled - saving to: {log_filename}")
    return log_filename

def main():
    """Main function - SIMPLIFIED VERSION WITH CRASH PREVENTION"""
    # Setup logging first
    log_file = setup_logging()

    print("🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT")
    print("Based on analyze_proper_flow.py pattern")
    print("Implements your exact hierarchical navigation flow")
    print("Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown 4) Server crashes")
    print("="*70)

    driver = None
    max_restart_attempts = 3
    restart_count = 0

    # Load or create progress file - YOUR REQUIREMENT
    progress_data, progress_filename = load_progress_file()

    try:
        # Load config
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"

        print("\n📱 Starting Appium session...")
        # Start simple Appium session
        driver = start_simple_appium_session(package)

        print("⏳ Waiting for app to load...")
        # Wait for app to load
        simple_wait_for_page_load(driver, timeout=15)

        print("🎯 Starting hierarchical crawl...")

        # Enhanced hierarchical crawl with crash recovery and progress tracking
        collected_data = {}
        while restart_count <= max_restart_attempts:
            try:
                # Run hierarchical crawl with your exact flow and progress tracking
                collected_data = run_hierarchical_crawl_with_progress(driver, progress_data, progress_filename)
                break  # Success, exit retry loop

            except Exception as crawl_error:
                error_msg = str(crawl_error)
                print(f"❌ Error during crawl: {crawl_error}")

                # Check if it's a server crash
                if "instrumentation process is not running" in error_msg:
                    print(f"[CRASH_RECOVERY] 🚨 Server crashed during crawl")
                    if restart_count < max_restart_attempts:
                        print(f"[CRASH_RECOVERY] 🔄 Attempting restart {restart_count + 1}/{max_restart_attempts}")
                        new_driver = restart_appium_session(package)
                        if new_driver:
                            driver = new_driver
                            restart_count += 1
                            print(f"[CRASH_RECOVERY] ✅ Session restarted, retrying crawl")
                            # Wait for app to load after restart
                            simple_wait_for_page_load(driver, timeout=15)
                            continue  # Retry the crawl
                        else:
                            print(f"[CRASH_RECOVERY] ❌ Failed to restart session")
                            break
                    else:
                        print(f"[CRASH_RECOVERY] ❌ Max restart attempts reached")
                        break
                else:
                    # Non-crash error, don't retry
                    break

        print("\n🎉 SIMPLIFIED ANALYSIS COMPLETE!")
        print(f"✅ Successfully collected data from {len(collected_data)} pages")
        print(f"🔄 Session restarts performed: {restart_count}")

        # Save final results
        save_collected_data_simple(collected_data)

        print("\n✅ ALL PROCESSING COMPLETE - NO CRASHES!")
        print("Press Enter to close session...")
        input()

    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"❌ Error in main: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Always try to close driver safely
        if driver:
            try:
                print("\n🔄 Closing Appium session...")
                driver.quit()
                print("✅ Session closed successfully")
            except Exception as close_error:
                print(f"⚠️ Error closing session: {close_error}")

        print("\n🎯 ANALYSIS FINISHED")
        print("Check the logs for any issues that need attention")

if __name__ == "__main__":
    main()
