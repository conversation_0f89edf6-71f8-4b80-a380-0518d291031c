import subprocess
import time
import json
import yaml
import requests
import websocket
from appium import webdriver
from appium.options.android import <PERSON>iA<PERSON>mator2Options
import re
import xml.etree.ElementTree as ET
import hashlib
from appium.webdriver.webelement import WebElement
import datetime
from selenium.common.exceptions import WebD<PERSON>Exception, StaleElementReferenceException, NoSuchElementException
import os
from appium.webdriver.common.appiumby import AppiumBy
from collections import defaultdict
import re
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.common.exceptions import NoSuchElementException
from difflib import SequenceMatcher
from selenium.webdriver.common.by import By
import difflib
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import pickle
import threading
import queue
from selenium import webdriver as web_driver
from selenium.webdriver.chrome.options import Options as ChromeOptions
# _time import removed - using standard time module

CONFIG_PATH = os.path.join("config", "config.yaml")

# Enhanced State Management Classes
class PageContext(Enum):
    NATIVE = "native"
    WEBVIEW = "webview"
    CHROME_NATIVE = "chrome_native"
    UNKNOWN = "unknown"

class NavigationState(Enum):
    MAIN_PAGE = "main_page"
    MENU_LEVEL_1 = "menu_level_1"
    MENU_LEVEL_2 = "menu_level_2"
    MENU_LEVEL_3 = "menu_level_3"
    UNKNOWN_LEVEL = "unknown_level"

@dataclass
class ElementInfo:
    xpath: str
    text: str
    content_desc: str
    resource_id: str
    class_name: str
    clickable: bool
    visible: bool
    bounds: Dict[str, int]
    page_context: PageContext
    navigation_level: NavigationState
    collected_at: str

@dataclass
class PageState:
    page_name: str
    context: PageContext
    navigation_level: NavigationState
    elements: List[ElementInfo]
    fully_explored: bool
    scroll_position: int
    page_source_hash: str
    timestamp: str

@dataclass
class NavigationPath:
    path: List[str]
    current_depth: int
    current_page: str
    context_history: List[PageContext]

@dataclass
class CrawlState:
    navigation_path: NavigationPath
    visited_pages: Set[str]
    collected_elements: List[ElementInfo]
    page_states: Dict[str, PageState]
    current_menu_index: int
    current_submenu_index: int
    recovery_checkpoint: Optional[str]
    last_successful_action: str
    crash_count: int

class StateManager:
    def __init__(self, package_name: str, state_file: str = None):
        self.package_name = package_name
        self.state_file = state_file or f"state_{package_name}.pkl"
        self.state_lock = threading.Lock()
        self.crawl_state = CrawlState(
            navigation_path=NavigationPath([], 0, "Main Page", []),
            visited_pages=set(),
            collected_elements=[],
            page_states={},
            current_menu_index=0,
            current_submenu_index=0,
            recovery_checkpoint=None,
            last_successful_action="",
            crash_count=0
        )

    def save_state(self):
        """Save current state to disk for crash recovery"""
        with self.state_lock:
            try:
                with open(self.state_file, 'wb') as f:
                    pickle.dump(self.crawl_state, f)
                print(f"[STATE] Saved state to {self.state_file}")
            except Exception as e:
                print(f"[STATE] Error saving state: {e}")

    def load_state(self) -> bool:
        """Load state from disk if available"""
        if not os.path.exists(self.state_file):
            return False

        try:
            with open(self.state_file, 'rb') as f:
                self.crawl_state = pickle.load(f)
            print(f"[STATE] Loaded state from {self.state_file}")
            return True
        except Exception as e:
            print(f"[STATE] Error loading state: {e}")
            return False

    def update_navigation_path(self, page_name: str, context: PageContext):
        """Update current navigation path"""
        with self.state_lock:
            self.crawl_state.navigation_path.path.append(page_name)
            self.crawl_state.navigation_path.current_page = page_name
            self.crawl_state.navigation_path.context_history.append(context)
            self.crawl_state.navigation_path.current_depth = len(self.crawl_state.navigation_path.path) - 1

    def add_page_state(self, page_state: PageState):
        """Add or update page state"""
        with self.state_lock:
            self.crawl_state.page_states[page_state.page_name] = page_state
            self.crawl_state.visited_pages.add(page_state.page_name)

    def add_elements(self, elements: List[ElementInfo]):
        """Add collected elements to state"""
        with self.state_lock:
            self.crawl_state.collected_elements.extend(elements)

    def set_recovery_checkpoint(self, checkpoint: str):
        """Set recovery checkpoint for crash recovery"""
        with self.state_lock:
            self.crawl_state.recovery_checkpoint = checkpoint
            self.crawl_state.last_successful_action = checkpoint

    def increment_crash_count(self):
        """Increment crash counter"""
        with self.state_lock:
            self.crawl_state.crash_count += 1

    def get_current_state(self) -> CrawlState:
        """Get current crawl state"""
        with self.state_lock:
            return self.crawl_state

class PageTracker:
    """Track page navigation and scroll state for smart positioning"""
    def __init__(self):
        self.has_scrolled_down = False
        self.current_page = "MAIN"
        self.page_just_opened = True
        self.menu_locations = {}  # Store menu locations for smart clicking

    def mark_page_opened(self, page_name):
        """Mark that a new page was just opened"""
        self.current_page = page_name
        self.page_just_opened = True
        self.has_scrolled_down = False
        print(f"[TRACKER] New page opened: {page_name} - At top by default")

    def mark_scrolled_down(self):
        """Mark that user has scrolled down"""
        self.has_scrolled_down = True
        self.page_just_opened = False
        print(f"[TRACKER] Scrolled down on {self.current_page}")

    def is_at_top(self):
        """Determine if we're at top based on navigation logic"""
        if self.page_just_opened:
            print(f"[TRACKER] ✅ At top - {self.current_page} just opened")
            return True
        elif not self.has_scrolled_down:
            print(f"[TRACKER] ✅ At top - No scrolling done on {self.current_page}")
            return True
        else:
            print(f"[TRACKER] ⚠️ Not at top - Has scrolled down on {self.current_page}")
            return False

    def store_menu_location(self, menu_name, location_info):
        """Store menu location information for smart clicking"""
        self.menu_locations[menu_name] = location_info
        print(f"[TRACKER] Stored location for menu: {menu_name}")

    def get_menu_location(self, menu_name):
        """Get stored menu location"""
        return self.menu_locations.get(menu_name)

    def detect_page_position(self, driver):
        """Intelligently detect if we're at the top of the page using navigation logic"""
        try:
            print("[POSITION] Analyzing current page position...")

            # Use navigation-based logic (your insight!)
            if self.is_at_top():
                return "TOP"

            # Fallback: Check if we can see top elements
            top_indicators = [
                "//*[contains(@text, 'Jelajahi') or contains(@content-desc, 'Jelajahi')]",
                "//*[contains(@text, 'Temukan') or contains(@content-desc, 'Temukan')]",
                "//*[@text='Ruang GTK' or @content-desc='Ruang GTK']",
                "//*[contains(@text, 'Beranda') or contains(@content-desc, 'Beranda')]"
            ]

            top_elements_found = 0
            for indicator in top_indicators:
                try:
                    elements = driver.find_elements("xpath", indicator)
                    if elements:
                        element = elements[0]
                        location = element.location
                        if location['y'] < 400:
                            top_elements_found += 1
                            print(f"[POSITION] Top indicator found at y={location['y']}")
                except:
                    continue

            if top_elements_found >= 2:
                print(f"[POSITION] ✅ At top - found {top_elements_found} top indicators")
                return "TOP"
            else:
                print("[POSITION] ⚠️ Not at top - need to scroll up")
                return "NOT_TOP"

        except Exception as e:
            print(f"[POSITION] Error detecting position: {e}")
            return "TOP"  # Safe default

    def smart_scroll_to_top(self, driver):
        """Smart scroll that only scrolls when necessary and safe"""
        try:
            print("[SMART_SCROLL] Checking if scroll to top is needed...")

            # Use navigation-based logic
            position = self.detect_page_position(driver)

            if position == "TOP":
                print("[SMART_SCROLL] ✅ Already at top - no scroll needed")
                return True

            print("[SMART_SCROLL] Not at top, performing safe scroll...")

            # Perform safe scroll to top
            size = driver.get_window_size()
            start_x = size['width'] // 2

            max_scrolls = 3
            for i in range(max_scrolls):
                # Safe scroll up (avoid pull-to-refresh)
                start_y = int(size['height'] * 0.6)  # Start from middle
                end_y = int(size['height'] * 0.8)    # End at bottom (scroll up)

                print(f"[SMART_SCROLL] Safe scroll up {i+1}/{max_scrolls}...")
                driver.swipe(start_x, start_y, start_x, end_y, 1000)
                time.sleep(1.5)

                # Check if we've reached top
                current_position = self.detect_page_position(driver)
                if current_position == "TOP":
                    print(f"[SMART_SCROLL] ✅ Reached top after {i+1} scrolls")
                    # Reset tracker since we're back at top
                    self.has_scrolled_down = False
                    self.page_just_opened = False
                    return True

            print("[SMART_SCROLL] ✅ Scroll complete")
            # Reset tracker
            self.has_scrolled_down = False
            self.page_just_opened = False
            return True

        except Exception as e:
            print(f"[SMART_SCROLL] Error in smart scroll: {e}")
            return True

class SmartNavigationEngine:
    def __init__(self, driver, state_manager: StateManager, config: dict):
        self.driver = driver
        self.state_manager = state_manager
        self.config = config
        self.page_tracker = PageTracker()  # Add page tracker
        self.main_menus = [
            'Ruang GTK', 'Ruang Murid', 'Ruang Sekolah', 'Ruang Bahasa',
            'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua',
            'Sumber Belajar', 'Pusat Perbukuan', 'Pengelolaan Kinerja', 'Lihat Semua',
            'Butuh Bantuan?', 'Ruang', 'Pemberitahuan', 'Akun'
        ]

    def detect_page_context(self) -> PageContext:
        """Enhanced context detection"""
        try:
            package = self.driver.current_package
            contexts = self.driver.contexts
            curr_context = getattr(self.driver, 'current_context', None)

            if package == 'com.android.chrome':
                for ctx in contexts:
                    if 'WEBVIEW' in ctx or 'CHROMIUM' in ctx:
                        return PageContext.WEBVIEW
                return PageContext.CHROME_NATIVE
            elif curr_context and (curr_context.startswith('WEBVIEW') or curr_context.startswith('CHROMIUM')):
                return PageContext.WEBVIEW
            else:
                return PageContext.NATIVE
        except Exception as e:
            print(f"[CONTEXT] Error detecting context: {e}")
            return PageContext.UNKNOWN

    def determine_navigation_level(self, page_name: str) -> NavigationState:
        """Determine current navigation level based on page name and path"""
        if page_name == "Main Page" or page_name in self.main_menus:
            return NavigationState.MAIN_PAGE

        depth = self.state_manager.crawl_state.navigation_path.current_depth
        if depth == 1:
            return NavigationState.MENU_LEVEL_1
        elif depth == 2:
            return NavigationState.MENU_LEVEL_2
        elif depth == 3:
            return NavigationState.MENU_LEVEL_3
        else:
            return NavigationState.UNKNOWN_LEVEL

    def smart_back_navigation(self, target_context: PageContext):
        """Smart back navigation based on context"""
        try:
            current_context = self.detect_page_context()
            main_package = self.state_manager.package_name

            if current_context == PageContext.NATIVE:
                print("[NAV] Using native back navigation")
                self.driver.back()
            elif current_context in [PageContext.WEBVIEW, PageContext.CHROME_NATIVE]:
                print("[NAV] Switching back to main app from webview/chrome")
                self.driver.activate_app(main_package)
            else:
                print("[NAV] Unknown context, using default back")
                self.driver.back()

            time.sleep(1)
            return True
        except Exception as e:
            print(f"[NAV] Error in back navigation: {e}")
            return False

    def navigate_to_main_page(self) -> bool:
        """Navigate back to main page"""
        try:
            main_package = self.state_manager.package_name
            self.driver.activate_app(main_package)
            time.sleep(2)

            # Update state
            self.state_manager.crawl_state.navigation_path = NavigationPath(
                ["Main Page"], 0, "Main Page", [self.detect_page_context()]
            )
            return True
        except Exception as e:
            print(f"[NAV] Error navigating to main page: {e}")
            return False

class ComprehensiveElementCollector:
    def __init__(self, driver, state_manager: StateManager, config: dict):
        self.driver = driver
        self.state_manager = state_manager
        self.config = config
        # Remove hardcoded labels - collect ALL elements dynamically
        self.min_text_length = config.get('min_element_text_length', 2)
        self.collect_all_elements = config.get('collect_all_elements', True)
        self.main_package = state_manager.package_name
        self.allowed_external_packages = set()  # Track external apps opened from main app
        self.target_package = state_manager.package_name
        self.allowed_external_packages = [
            'com.android.chrome',
            'com.android.browser',
            'com.google.android.webview',
            'com.android.webview'
        ]

    def scroll_to_bottom_step_by_step(self, max_scrolls: int = 8) -> List[ElementInfo]:
        """Scroll through page step by step collecting all elements"""
        collected_elements = []
        seen_elements = set()
        scroll_count = 0
        last_page_source = None

        print(f"[COLLECT] Starting step-by-step scroll collection (max {max_scrolls} scrolls)")

        while scroll_count < max_scrolls:
            try:
                # Collect elements at current scroll position
                current_elements = self._collect_visible_elements()

                # Add new elements
                for element in current_elements:
                    element_key = f"{element.xpath}_{element.text}_{element.content_desc}"
                    if element_key not in seen_elements:
                        collected_elements.append(element)
                        seen_elements.add(element_key)

                # Check if we've reached the bottom
                current_page_source = self.driver.page_source
                if last_page_source and self._is_same_content(current_page_source, last_page_source):
                    print(f"[COLLECT] Reached bottom of page after {scroll_count} scrolls")
                    break

                last_page_source = current_page_source

                # Perform scroll
                if not self._perform_smart_scroll():
                    print("[COLLECT] Could not scroll further")
                    break

                # Mark that we've scrolled down (if page tracker is available)
                if hasattr(self.state_manager, 'page_tracker'):
                    self.state_manager.page_tracker.mark_scrolled_down()

                scroll_count += 1
                time.sleep(1.5)  # Wait for content to load

            except Exception as e:
                print(f"[COLLECT] Error during scroll collection: {e}")
                break

        print(f"[COLLECT] Collected {len(collected_elements)} elements after {scroll_count} scrolls")
        return collected_elements

    def _collect_visible_elements(self) -> List[ElementInfo]:
        """Collect all visible elements on current screen with package filtering"""
        elements = []
        try:
            # Check current package and filter appropriately
            if not self._should_collect_from_current_package():
                print(f"[COLLECT] Skipping element collection - not in allowed package")
                return elements

            xml = self.driver.page_source
            tree = ET.ElementTree(ET.fromstring(xml))
            root = tree.getroot()

            current_context = self._get_current_context()
            current_level = self._get_current_navigation_level()
            timestamp = datetime.datetime.now().isoformat()

            def recurse_elements(node, path):
                parent = node.getparent() if hasattr(node, 'getparent') else None
                if parent is not None:
                    same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                    idx = same_tag_siblings.index(node) + 1
                else:
                    idx = 1

                this_xpath = f"{path}/{node.tag}[{idx}]"
                attrs = node.attrib.copy()

                text = attrs.get('text', node.text or "")
                content_desc = attrs.get('content-desc', "")
                resource_id = attrs.get('resource-id', "")
                class_name = attrs.get('class', node.tag)
                clickable = attrs.get('clickable', 'false') == 'true'

                # Check if element has meaningful content
                if (text and text.strip()) or (content_desc and content_desc.strip()) or clickable:
                    try:
                        # Try to find element and check visibility
                        element = self.driver.find_element('xpath', this_xpath)
                        visible = element.is_displayed()
                        bounds = element.rect

                        element_info = ElementInfo(
                            xpath=this_xpath,
                            text=text,
                            content_desc=content_desc,
                            resource_id=resource_id,
                            class_name=class_name,
                            clickable=clickable,
                            visible=visible,
                            bounds=bounds,
                            page_context=current_context,
                            navigation_level=current_level,
                            collected_at=timestamp
                        )
                        elements.append(element_info)

                    except Exception:
                        # Element not found or not accessible, create info anyway
                        element_info = ElementInfo(
                            xpath=this_xpath,
                            text=text,
                            content_desc=content_desc,
                            resource_id=resource_id,
                            class_name=class_name,
                            clickable=clickable,
                            visible=False,
                            bounds={},
                            page_context=current_context,
                            navigation_level=current_level,
                            collected_at=timestamp
                        )
                        elements.append(element_info)

                # Recurse into children
                for child in list(node):
                    recurse_elements(child, this_xpath)

            recurse_elements(root, "")

        except Exception as e:
            print(f"[COLLECT] Error collecting elements: {e}")

        return elements

    def _get_current_context(self) -> PageContext:
        """Get current page context"""
        try:
            package = self.driver.current_package
            contexts = self.driver.contexts

            if package == 'com.android.chrome':
                return PageContext.CHROME_NATIVE
            elif any('WEBVIEW' in ctx or 'CHROMIUM' in ctx for ctx in contexts):
                return PageContext.WEBVIEW
            else:
                return PageContext.NATIVE
        except:
            return PageContext.UNKNOWN

    def _get_current_navigation_level(self) -> NavigationState:
        """Get current navigation level"""
        depth = self.state_manager.crawl_state.navigation_path.current_depth
        if depth == 0:
            return NavigationState.MAIN_PAGE
        elif depth == 1:
            return NavigationState.MENU_LEVEL_1
        elif depth == 2:
            return NavigationState.MENU_LEVEL_2
        elif depth == 3:
            return NavigationState.MENU_LEVEL_3
        else:
            return NavigationState.UNKNOWN_LEVEL

    def _perform_smart_scroll(self) -> bool:
        """Perform smart scrolling"""
        try:
            # Try to find scrollable container
            scrollable = None
            for class_name in ['ScrollView', 'RecyclerView', 'android.widget.ScrollView', 'androidx.recyclerview.widget.RecyclerView']:
                try:
                    scrollable = self.driver.find_element('xpath', f"//*[contains(@class, '{class_name}')]")
                    break
                except:
                    continue

            if scrollable:
                loc = scrollable.location
                size = scrollable.size
                start_x = loc['x'] + size['width'] // 2
                start_y = loc['y'] + int(size['height'] * 0.8)
                end_y = loc['y'] + int(size['height'] * 0.2)

                self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                return True
            else:
                # Fallback to screen swipe
                size = self.driver.get_window_size()
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.8)
                end_y = int(size['height'] * 0.2)

                self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                return True

        except Exception as e:
            print(f"[SCROLL] Error performing scroll: {e}")
            return False

    def _is_same_content(self, source1: str, source2: str) -> bool:
        """Check if two page sources represent the same content"""
        try:
            # Simple hash comparison
            hash1 = hashlib.md5(source1.encode()).hexdigest()
            hash2 = hashlib.md5(source2.encode()).hexdigest()
            return hash1 == hash2
        except:
            return False

    def _should_collect_from_current_package(self) -> bool:
        """Check if we should collect elements from current package"""
        try:
            current_package = self.driver.current_package

            # Always collect from main package
            if current_package == self.main_package:
                return True

            # Check if this is an external app opened directly from main app
            if current_package in self.allowed_external_packages:
                return True

            # Check if this is a system package that might be part of the flow
            system_packages = [
                'com.android.chrome',
                'com.android.browser',
                'com.google.android.webview',
                'com.android.webview'
            ]

            if current_package in system_packages:
                # Add to allowed external packages for future reference
                self.allowed_external_packages.add(current_package)
                print(f"[COLLECT] Added external package to allowed list: {current_package}")
                return True

            print(f"[COLLECT] Skipping package {current_package} - not main app or allowed external")
            return False

        except Exception as e:
            print(f"[COLLECT] Error checking package: {e}")
            return True  # Default to collecting if we can't determine package

    def track_external_app_opened(self, package_name: str):
        """Track when an external app is opened from main app"""
        if package_name != self.main_package:
            self.allowed_external_packages.add(package_name)
            print(f"[COLLECT] Tracking external app opened from main app: {package_name}")

    def collect_all_meaningful_elements(self) -> List[ElementInfo]:
        """Collect ALL meaningful elements without hardcoded filtering"""
        meaningful_elements = []

        try:
            all_elements = self._collect_visible_elements()

            for element in all_elements:
                if self._is_meaningful_element(element):
                    meaningful_elements.append(element)

        except Exception as e:
            print(f"[COLLECT] Error collecting meaningful elements: {e}")

        return meaningful_elements

    def _is_meaningful_element(self, element: ElementInfo) -> bool:
        """Check if element is meaningful (has content or is interactive)"""
        # Collect elements that have meaningful content or are interactive
        element_text = element.text.strip()
        element_desc = element.content_desc.strip()

        # Filter out null/empty elements
        if element_text == 'null' or element_desc == 'null':
            return False

        if element_text == '' and element_desc == '':
            return False

        # Include if element has text content
        if element_text and len(element_text) >= self.min_text_length:
            return True

        # Include if element has content description
        if element_desc and len(element_desc) >= self.min_text_length:
            return True

        # Include if element is clickable (interactive)
        if element.clickable:
            return True

        # Include if element has resource ID (likely important)
        if element.resource_id and element.resource_id.strip():
            return True

        # Include specific UI element types that are typically important
        important_classes = [
            'Button', 'ImageButton', 'TextView', 'EditText', 'CheckBox',
            'RadioButton', 'Switch', 'ToggleButton', 'Spinner', 'ListView',
            'RecyclerView', 'ViewPager', 'TabLayout', 'NavigationView'
        ]

        if any(cls in element.class_name for cls in important_classes):
            return True

        return False

class CrashRecoverySystem:
    def __init__(self, state_manager: StateManager, config: dict):
        self.state_manager = state_manager
        self.config = config
        self.max_retries = 3

    def restart_appium_session(self, package_name: str) -> Optional[webdriver.Remote]:
        """Restart Appium session after crash"""
        try:
            print("[RECOVERY] Restarting Appium session...")

            # Increment crash count
            self.state_manager.increment_crash_count()

            # Create new driver
            driver = start_appium_session(package_name, self.config['wait_timeout'])

            if driver:
                print("[RECOVERY] Appium session restarted successfully")
                return driver
            else:
                print("[RECOVERY] Failed to restart Appium session")
                return None

        except Exception as e:
            print(f"[RECOVERY] Error restarting Appium session: {e}")
            return None

    def replay_navigation_path(self, driver, navigation_path: List[str]) -> bool:
        """Replay navigation path to restore state"""
        try:
            print(f"[RECOVERY] Replaying navigation path: {navigation_path}")

            # Start from main page
            main_package = self.state_manager.package_name
            driver.activate_app(main_package)
            time.sleep(2)

            # Navigate through each step in the path (skip main page)
            for step_name in navigation_path[1:]:
                print(f"[RECOVERY] Navigating to: {step_name}")

                # Try to find element by content-desc first
                element = None
                try:
                    element = driver.find_element("xpath", f"//*[@content-desc='{step_name}']")
                except:
                    pass

                # Try by text if content-desc failed
                if not element:
                    try:
                        element = driver.find_element("xpath", f"//*[@text='{step_name}']")
                    except:
                        pass

                # Try partial matching
                if not element:
                    try:
                        element = driver.find_element("xpath", f"//*[contains(@content-desc, '{step_name}')]")
                    except:
                        pass

                if not element:
                    try:
                        element = driver.find_element("xpath", f"//*[contains(@text, '{step_name}')]")
                    except:
                        pass

                if element:
                    element.click()
                    time.sleep(2)
                    print(f"[RECOVERY] Successfully navigated to: {step_name}")
                else:
                    print(f"[RECOVERY] Could not find element for: {step_name}")
                    return False

            print("[RECOVERY] Navigation path replay completed successfully")
            return True

        except Exception as e:
            print(f"[RECOVERY] Error replaying navigation path: {e}")
            return False

    def attempt_recovery(self, package_name: str) -> Optional[webdriver.Remote]:
        """Attempt full crash recovery"""
        try:
            crash_count = self.state_manager.crawl_state.crash_count

            if crash_count >= self.max_retries:
                print(f"[RECOVERY] Max retries ({self.max_retries}) reached. Giving up.")
                return None

            print(f"[RECOVERY] Attempting recovery (attempt {crash_count + 1}/{self.max_retries})")

            # Restart Appium session
            driver = self.restart_appium_session(package_name)
            if not driver:
                return None

            # Replay navigation path
            navigation_path = self.state_manager.crawl_state.navigation_path.path
            if len(navigation_path) > 1:  # More than just main page
                success = self.replay_navigation_path(driver, navigation_path)
                if not success:
                    print("[RECOVERY] Failed to replay navigation path")
                    driver.quit()
                    return None

            # Update state with successful recovery
            self.state_manager.set_recovery_checkpoint("Recovery completed successfully")
            self.state_manager.save_state()

            return driver

        except Exception as e:
            print(f"[RECOVERY] Error during recovery attempt: {e}")
            return None

class SmartMenuDetector:
    def __init__(self, driver, state_manager: StateManager):
        self.driver = driver
        self.state_manager = state_manager

    def detect_remaining_menus(self, current_level: NavigationState) -> List[str]:
        """Enhanced detection of remaining unvisited menus at current level"""
        try:
            print(f"[MENU_DETECT] Detecting menus for level: {current_level}")

            menu_candidates = []

            # Strategy 1: Find explicitly clickable elements
            clickable_elements = self.driver.find_elements("xpath", "//*[@clickable='true']")
            print(f"[MENU_DETECT] Found {len(clickable_elements)} explicitly clickable elements")

            for element in clickable_elements:
                candidate = self._analyze_menu_candidate(element, current_level)
                if candidate:
                    menu_candidates.append(candidate)

            # Strategy 2: Find potentially clickable elements (even if not marked clickable)
            potentially_clickable = self._find_potentially_clickable_elements()
            print(f"[MENU_DETECT] Found {len(potentially_clickable)} potentially clickable elements")

            for element in potentially_clickable:
                candidate = self._analyze_menu_candidate(element, current_level)
                if candidate:
                    menu_candidates.append(candidate)

            # Strategy 3: Find elements with meaningful text/content-desc
            text_elements = self.driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"[MENU_DETECT] Found {len(text_elements)} elements with text/content-desc")

            for element in text_elements:
                if self._could_be_menu_item(element):
                    candidate = self._analyze_menu_candidate(element, current_level)
                    if candidate:
                        menu_candidates.append(candidate)

            # Remove duplicates and filter
            unique_candidates = self._deduplicate_menu_candidates(menu_candidates)

            print(f"[MENU_DETECT] Found {len(unique_candidates)} unique menu candidates")
            for i, candidate in enumerate(unique_candidates[:10]):  # Show first 10
                print(f"[MENU_DETECT] {i+1}. {candidate}")

            return unique_candidates

        except Exception as e:
            print(f"[MENU_DETECT] Error detecting menus: {e}")
            return []

    def _analyze_menu_candidate(self, element, current_level: NavigationState) -> str:
        """Analyze if element is a valid menu candidate"""
        try:
            text = element.get_attribute('text') or ''
            content_desc = element.get_attribute('content-desc') or ''
            resource_id = element.get_attribute('resource-id') or ''
            class_name = element.get_attribute('class') or ''

            # Get the best identifier for this element
            element_name = text or content_desc
            if not element_name:
                # Try to get text from child elements
                try:
                    children = element.find_elements("xpath", ".//*[@text!='' or @content-desc!='']")
                    for child in children[:3]:  # Check first 3 children
                        child_text = child.get_attribute('text') or child.get_attribute('content-desc') or ''
                        if child_text and len(child_text.strip()) > 2:
                            element_name = child_text.strip()
                            break
                except:
                    pass

            if not element_name or len(element_name.strip()) < 2:
                return None

            element_name = element_name.strip()

            # Skip if already visited
            if element_name in self.state_manager.crawl_state.visited_pages:
                return None

            # Check if it looks like a menu item
            if not self._is_menu_like(element_name, current_level, class_name, resource_id):
                return None

            return element_name

        except Exception as e:
            return None

    def _find_potentially_clickable_elements(self) -> list:
        """Find elements that might be clickable even if not marked as such"""
        potentially_clickable = []

        try:
            # Common clickable element patterns
            patterns = [
                "//android.widget.TextView[string-length(@text) > 2]",
                "//android.widget.ImageView[@content-desc!='']",
                "//android.view.ViewGroup[*[@text!='' or @content-desc!='']]",
                "//androidx.cardview.widget.CardView",
                "//*[contains(@resource-id, 'item')]",
                "//*[contains(@resource-id, 'menu')]",
                "//*[contains(@resource-id, 'card')]",
                "//*[contains(@resource-id, 'button')]"
            ]

            for pattern in patterns:
                try:
                    elements = self.driver.find_elements("xpath", pattern)
                    for element in elements:
                        if self._has_reasonable_size(element):
                            potentially_clickable.append(element)
                except:
                    continue

        except Exception as e:
            print(f"[MENU_DETECT] Error finding potentially clickable elements: {e}")

        return potentially_clickable

    def _could_be_menu_item(self, element) -> bool:
        """Check if element could be a menu item based on various criteria"""
        try:
            # Check if element has reasonable size for a menu item
            if not self._has_reasonable_size(element):
                return False

            # Check if element has meaningful content
            text = element.get_attribute('text') or ''
            content_desc = element.get_attribute('content-desc') or ''

            if not text and not content_desc:
                return False

            # Check if content is meaningful (not just numbers or single characters)
            meaningful_content = text or content_desc
            if len(meaningful_content.strip()) < 2:
                return False

            # Skip common non-menu elements
            skip_patterns = ['back', 'close', 'cancel', 'ok', 'yes', 'no', '×', '✓']
            if any(pattern in meaningful_content.lower() for pattern in skip_patterns):
                return False

            return True

        except:
            return False

    def _has_reasonable_size(self, element) -> bool:
        """Check if element has reasonable size for interaction"""
        try:
            rect = element.rect
            width = rect.get('width', 0)
            height = rect.get('height', 0)

            # Menu items should be at least 40x20 pixels and not too large
            return (width >= 40 and height >= 20 and
                   width <= 800 and height <= 200)
        except:
            return True  # Default to true if we can't get size

    def _deduplicate_menu_candidates(self, candidates: list) -> list:
        """Remove duplicate menu candidates"""
        seen = set()
        unique = []

        for candidate in candidates:
            if candidate and candidate not in seen:
                seen.add(candidate)
                unique.append(candidate)

        return unique

    def _is_menu_like(self, element_name: str, level: NavigationState, class_name: str = '', resource_id: str = '') -> bool:
        """Enhanced check if element looks like a menu item"""
        # Basic validation
        if not element_name or len(element_name.strip()) < 2:
            return False

        element_name_lower = element_name.lower().strip()

        # Skip common UI elements that are definitely not menus
        skip_terms = [
            'back', 'close', 'cancel', 'ok', 'yes', 'no', 'search', 'filter',
            'loading', 'please wait', 'error', 'retry', 'refresh', 'home',
            'settings', 'help', 'about', 'version', 'copyright', '©', '®',
            'next', 'previous', 'skip', 'done', 'finish', 'submit'
        ]

        if any(term in element_name_lower for term in skip_terms):
            return False

        # Skip pure numbers or very short text
        if element_name.isdigit() or len(element_name.strip()) < 3:
            return False

        # For main page, be more inclusive but still filter known main menus
        if level == NavigationState.MAIN_PAGE:
            main_menu_keywords = [
                'ruang', 'gtk', 'murid', 'sekolah', 'bahasa', 'pemerintah',
                'mitra', 'publik', 'orang tua', 'sumber', 'belajar', 'pusat',
                'perbukuan', 'pengelolaan', 'kinerja', 'lihat', 'semua',
                'bantuan', 'pemberitahuan', 'akun'
            ]

            # If it contains main menu keywords, it's likely a main menu
            if any(keyword in element_name_lower for keyword in main_menu_keywords):
                return True

        # For submenus, be very inclusive - most text elements could be menus
        else:
            # Check if it has characteristics of a menu item
            menu_indicators = [
                # Class name indicators
                'TextView' in class_name,
                'Button' in class_name,
                'ImageView' in class_name,
                'CardView' in class_name,

                # Resource ID indicators
                'menu' in resource_id.lower(),
                'item' in resource_id.lower(),
                'card' in resource_id.lower(),
                'button' in resource_id.lower(),

                # Text content indicators
                len(element_name.strip()) >= 3,
                not element_name.isdigit(),
                ' ' in element_name or len(element_name) > 5,  # Multi-word or longer single word
            ]

            # If any menu indicators are present, consider it a potential menu
            if any(menu_indicators):
                return True

        # Additional heuristics for any level
        # If text looks like a title or navigation item
        title_patterns = [
            element_name.istitle(),  # Title Case
            element_name.isupper() and len(element_name) > 3,  # UPPERCASE
            len(element_name.split()) > 1,  # Multiple words
            any(char.isalpha() for char in element_name),  # Contains letters
        ]

        if any(title_patterns):
            return True

        # Default to false for very short or unclear text
        return len(element_name.strip()) > 4

    def is_page_fully_explored(self, page_name: str) -> bool:
        """Check if current page has been fully explored"""
        try:
            # Check if page state exists and is marked as fully explored
            page_state = self.state_manager.crawl_state.page_states.get(page_name)
            if page_state:
                return page_state.fully_explored

            # If no state exists, consider it not explored
            return False

        except Exception as e:
            print(f"[MENU_DETECT] Error checking exploration status: {e}")
            return False

# Old EnhancedAppCrawler class removed - now defined after SmartActionClicker
        """Enhanced crawl through all main menus with intelligent detection"""
        try:
            main_menus = self.navigator.main_menus

            print(f"[ENHANCED_CRAWL] Starting to crawl {len(main_menus)} main menus")

            for menu_index, menu_name in enumerate(main_menus):
                try:
                    print(f"\n[ENHANCED_CRAWL] ===== Processing main menu {menu_index + 1}/{len(main_menus)}: {menu_name} =====")

                    # Update state
                    self.state_manager.crawl_state.current_menu_index = menu_index
                    self.state_manager.set_recovery_checkpoint(f"Processing main menu: {menu_name}")

                    # Navigate to main page first and ensure we're at the top
                    if not self._prepare_main_page_for_menu_click():
                        print(f"[ENHANCED_CRAWL] Could not prepare main page for menu: {menu_name}")
                        continue

                    # Try to find and click the menu with enhanced detection
                    if self._find_and_click_main_menu(menu_name):
                        print(f"[ENHANCED_CRAWL] Successfully clicked menu: {menu_name}")

                        # Wait for page to load
                        time.sleep(3)

                        # Collect elements on the menu page
                        menu_elements = self._collect_complete_page(menu_name)
                        print(f"[ENHANCED_CRAWL] Collected {len(menu_elements)} elements from {menu_name}")

                        # Crawl submenus recursively
                        self._crawl_submenus(menu_name, 1)

                        print(f"[ENHANCED_CRAWL] Completed crawling menu: {menu_name}")

                    else:
                        print(f"[ENHANCED_CRAWL] Could not find or click menu: {menu_name}")
                        # Show debug information to help understand what's available
                        self.debug_page_elements(20)
                        # Try to find similar menus or alternatives
                        self._try_alternative_menu_detection(menu_name)

                except Exception as e:
                    print(f"[ENHANCED_CRAWL] Error processing menu {menu_name}: {e}")
                    # Try recovery
                    recovered_driver = self.recovery_system.attempt_recovery(self.package_name)
                    if recovered_driver:
                        self.driver = recovered_driver
                        print(f"[ENHANCED_CRAWL] Recovered from error, continuing with next menu")
                        continue
                    else:
                        print(f"[ENHANCED_CRAWL] Could not recover from error in menu {menu_name}")
                        break

            print(f"\n[ENHANCED_CRAWL] Completed crawling all main menus")

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in main menu crawling: {e}")

    def _prepare_main_page_for_menu_click(self) -> bool:
        """Prepare main page for menu clicking"""
        try:
            # Navigate to main page
            if not self.navigator.navigate_to_main_page():
                return False

            # Scroll to top to ensure we can see main menus
            self._scroll_to_top()
            time.sleep(2)

            # Sometimes menus are below the fold, scroll down a bit to find them
            for scroll_attempt in range(3):
                # Check if we can find any main menu
                found_any_menu = False
                for menu_name in self.navigator.main_menus[:5]:  # Check first 5 menus
                    candidates = self._find_menu_candidates(menu_name)
                    if candidates:
                        found_any_menu = True
                        break

                if found_any_menu:
                    print(f"[ENHANCED_CRAWL] Found main menus after {scroll_attempt} scroll attempts")
                    return True

                # Scroll down a bit to find menus
                try:
                    size = self.driver.get_window_size()
                    start_x = size['width'] // 2
                    start_y = int(size['height'] * 0.4)
                    end_y = int(size['height'] * 0.6)
                    self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                    time.sleep(1)
                except:
                    pass

            return True  # Return true even if we don't find menus, let individual clicks handle it

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error preparing main page: {e}")
            return False

    def _find_and_click_main_menu(self, menu_name: str) -> bool:
        """Enhanced menu finding and clicking with scrolling"""
        try:
            # First try without scrolling
            if self._click_menu_item(menu_name):
                return True

            # If not found, try scrolling to find the menu
            print(f"[ENHANCED_CRAWL] Menu {menu_name} not found, trying with scrolling...")

            max_scroll_attempts = 5
            for scroll_attempt in range(max_scroll_attempts):
                # Scroll down to reveal more menus
                try:
                    size = self.driver.get_window_size()
                    start_x = size['width'] // 2
                    start_y = int(size['height'] * 0.7)
                    end_y = int(size['height'] * 0.3)
                    self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                    time.sleep(2)
                except:
                    pass

                # Try to click menu after scrolling
                if self._click_menu_item(menu_name):
                    print(f"[ENHANCED_CRAWL] Found and clicked {menu_name} after scrolling")
                    return True

            print(f"[ENHANCED_CRAWL] Could not find {menu_name} even after scrolling")
            return False

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in enhanced menu finding for {menu_name}: {e}")
            return False

    def _try_alternative_menu_detection(self, menu_name: str):
        """Try to find alternative ways to detect the menu"""
        try:
            print(f"[ENHANCED_CRAWL] Trying alternative detection for: {menu_name}")

            # Try to find any clickable elements that might be the menu
            all_clickable = self.driver.find_elements("xpath", "//*[@clickable='true']")

            print(f"[ENHANCED_CRAWL] Found {len(all_clickable)} clickable elements to analyze")

            for element in all_clickable[:20]:  # Limit to first 20 to avoid too much output
                try:
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    class_name = element.get_attribute('class') or ''

                    if text or desc:
                        print(f"[ENHANCED_CRAWL] Found clickable: text='{text}', desc='{desc}', class='{class_name}'")

                except:
                    continue

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in alternative detection: {e}")

# Orphaned methods removed - they are now part of the proper EnhancedAppCrawler class
        """Enhanced menu clicking with intelligent detection"""
        try:
            print(f"[MENU_CLICK] Attempting to click menu: {menu_name}")

            # First, try to find all possible candidates
            candidates = self._find_menu_candidates(menu_name)

            if not candidates:
                print(f"[MENU_CLICK] No candidates found for: {menu_name}")
                return False

            # Try each candidate with smart clicking
            for i, candidate in enumerate(candidates):
                try:
                    print(f"[MENU_CLICK] Trying candidate {i+1}/{len(candidates)}: {candidate['method']}")

                    # Ensure element is visible
                    if not self._ensure_element_visible(candidate['element']):
                        print(f"[MENU_CLICK] Candidate {i+1} not visible, skipping")
                        continue

                    # Use smart clicker to try multiple click methods
                    if self.smart_clicker.smart_click_element(candidate['element'], menu_name):
                        # Verify navigation occurred
                        if self._verify_navigation_occurred():
                            print(f"[MENU_CLICK] Successfully clicked {menu_name} using {candidate['method']}")
                            # Track package change if external app opened
                            self._track_package_change()
                            return True
                        else:
                            print(f"[MENU_CLICK] Click registered but no navigation detected")
                    else:
                        print(f"[MENU_CLICK] Smart click failed for candidate {i+1}")

                except Exception as e:
                    print(f"[MENU_CLICK] Error with candidate {i+1}: {e}")
                    continue

            print(f"[MENU_CLICK] All candidates failed for: {menu_name}")
            return False

        except Exception as e:
            print(f"[MENU_CLICK] Error in enhanced menu clicking for {menu_name}: {e}")
            return False

    def _track_package_change(self):
        """Track if we've moved to an external package"""
        try:
            current_package = self.driver.current_package
            if current_package != self.package_name:
                print(f"[PACKAGE_TRACK] Moved to external package: {current_package}")
                self.collector.track_external_app_opened(current_package)
        except Exception as e:
            print(f"[PACKAGE_TRACK] Error tracking package change: {e}")

    def _find_menu_candidates(self, menu_name: str) -> list:
        """Find all possible menu candidates using multiple strategies"""
        candidates = []

        try:
            # Strategy 1: Exact content-desc match
            try:
                elements = self.driver.find_elements("xpath", f"//*[@content-desc='{menu_name}']")
                for element in elements:
                    if self._is_likely_clickable(element):
                        candidates.append({'element': element, 'method': 'exact_content_desc'})
            except:
                pass

            # Strategy 2: Exact text match
            try:
                elements = self.driver.find_elements("xpath", f"//*[@text='{menu_name}']")
                for element in elements:
                    if self._is_likely_clickable(element):
                        candidates.append({'element': element, 'method': 'exact_text'})
            except:
                pass

            # Strategy 3: Partial content-desc match
            try:
                elements = self.driver.find_elements("xpath", f"//*[contains(@content-desc, '{menu_name}')]")
                for element in elements:
                    if self._is_likely_clickable(element):
                        candidates.append({'element': element, 'method': 'partial_content_desc'})
            except:
                pass

            # Strategy 4: Partial text match
            try:
                elements = self.driver.find_elements("xpath", f"//*[contains(@text, '{menu_name}')]")
                for element in elements:
                    if self._is_likely_clickable(element):
                        candidates.append({'element': element, 'method': 'partial_text'})
            except:
                pass

            # Strategy 5: Find clickable elements with nested text
            try:
                elements = self.driver.find_elements("xpath", f"//*[@clickable='true']")
                for element in elements:
                    if self._element_contains_text(element, menu_name):
                        candidates.append({'element': element, 'method': 'nested_text'})
            except:
                pass

            # Strategy 6: Find by resource-id patterns
            try:
                # Common resource-id patterns for menu items
                resource_patterns = [
                    f"//*[contains(@resource-id, 'menu')]",
                    f"//*[contains(@resource-id, 'item')]",
                    f"//*[contains(@resource-id, 'card')]",
                    f"//*[contains(@resource-id, 'button')]"
                ]

                for pattern in resource_patterns:
                    elements = self.driver.find_elements("xpath", pattern)
                    for element in elements:
                        if self._element_contains_text(element, menu_name) and self._is_likely_clickable(element):
                            candidates.append({'element': element, 'method': f'resource_pattern'})
            except:
                pass

            # Strategy 7: Find by class name patterns (common UI components)
            try:
                class_patterns = [
                    "//android.widget.TextView",
                    "//android.widget.Button",
                    "//android.widget.ImageView",
                    "//android.view.ViewGroup",
                    "//androidx.cardview.widget.CardView"
                ]

                for pattern in class_patterns:
                    elements = self.driver.find_elements("xpath", pattern)
                    for element in elements:
                        if self._element_contains_text(element, menu_name) and self._is_likely_clickable(element):
                            candidates.append({'element': element, 'method': f'class_pattern'})
            except:
                pass

            # Strategy 8: Fuzzy text matching for variations
            try:
                # Handle common variations in menu names
                menu_variations = self._generate_menu_variations(menu_name)

                for variation in menu_variations:
                    elements = self.driver.find_elements("xpath", f"//*[contains(translate(@text, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{variation.lower()}')]")
                    for element in elements:
                        if self._is_likely_clickable(element):
                            candidates.append({'element': element, 'method': f'fuzzy_text_{variation}'})

                    elements = self.driver.find_elements("xpath", f"//*[contains(translate(@content-desc, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{variation.lower()}')]")
                    for element in elements:
                        if self._is_likely_clickable(element):
                            candidates.append({'element': element, 'method': f'fuzzy_desc_{variation}'})
            except:
                pass

            # Remove duplicates and sort by priority
            candidates = self._deduplicate_and_prioritize_candidates(candidates)

            print(f"[MENU_CLICK] Found {len(candidates)} candidates for {menu_name}")
            return candidates

        except Exception as e:
            print(f"[MENU_CLICK] Error finding candidates for {menu_name}: {e}")
            return []

    def _is_likely_clickable(self, element) -> bool:
        """Check if element is likely to be clickable"""
        try:
            # Check if explicitly clickable
            if element.get_attribute('clickable') == 'true':
                return True

            # Check if focusable
            if element.get_attribute('focusable') == 'true':
                return True

            # Check if it's a common clickable class
            class_name = element.get_attribute('class') or ''
            clickable_classes = [
                'Button', 'ImageButton', 'TextView', 'ImageView',
                'CardView', 'LinearLayout', 'RelativeLayout', 'FrameLayout'
            ]

            if any(cls in class_name for cls in clickable_classes):
                return True

            # Check if it has meaningful bounds (not too small)
            try:
                rect = element.rect
                if rect['width'] > 50 and rect['height'] > 30:  # Reasonable tap target
                    return True
            except:
                pass

            return False

        except Exception:
            return False

    def _element_contains_text(self, element, target_text: str) -> bool:
        """Check if element or its children contain the target text"""
        try:
            target_lower = target_text.lower()

            # Check element's own text
            element_text = element.get_attribute('text') or ''
            if target_lower in element_text.lower():
                return True

            # Check element's content-desc
            content_desc = element.get_attribute('content-desc') or ''
            if target_lower in content_desc.lower():
                return True

            # Check child elements
            try:
                children = element.find_elements("xpath", ".//*")
                for child in children:
                    child_text = child.get_attribute('text') or ''
                    child_desc = child.get_attribute('content-desc') or ''

                    if target_lower in child_text.lower() or target_lower in child_desc.lower():
                        return True
            except:
                pass

            return False

        except Exception:
            return False

    def _ensure_element_visible(self, element) -> bool:
        """Ensure element is visible on screen"""
        try:
            # Check if element is displayed
            if not element.is_displayed():
                return False

            # Get element location and screen size
            location = element.location
            size = element.size
            screen_size = self.driver.get_window_size()

            # Check if element is within screen bounds
            if (location['y'] < 0 or
                location['y'] + size['height'] > screen_size['height'] or
                location['x'] < 0 or
                location['x'] + size['width'] > screen_size['width']):

                # Try to scroll to element
                try:
                    self.driver.execute_script("mobile: scrollToElement", {"element": element.id})
                    time.sleep(1)
                    return element.is_displayed()
                except:
                    return False

            return True

        except Exception:
            return False

    def _verify_navigation_occurred(self) -> bool:
        """Verify that navigation to a new page occurred"""
        try:
            # Wait a moment for navigation
            time.sleep(2)

            # Check if activity changed
            try:
                current_activity = self.driver.current_activity
                # If we can get activity, assume navigation might have occurred
                return True
            except:
                pass

            # Check if page source changed significantly
            try:
                current_source = self.driver.page_source
                # Simple check - if we can get page source, assume we're on a page
                return len(current_source) > 100
            except:
                pass

            return True  # Assume success if we can't verify

        except Exception:
            return True  # Assume success if verification fails

    def _deduplicate_and_prioritize_candidates(self, candidates: list) -> list:
        """Remove duplicate candidates and sort by priority"""
        try:
            # Remove duplicates based on element location
            unique_candidates = []
            seen_locations = set()

            for candidate in candidates:
                try:
                    element = candidate['element']
                    location = element.location
                    size = element.size

                    # Create unique key based on location and size
                    key = (location['x'], location['y'], size['width'], size['height'])

                    if key not in seen_locations:
                        seen_locations.add(key)
                        unique_candidates.append(candidate)

                except:
                    # If we can't get location, still include it
                    unique_candidates.append(candidate)

            # Sort by priority (exact matches first)
            priority_order = [
                'exact_content_desc', 'exact_text',
                'partial_content_desc', 'partial_text',
                'nested_text', 'resource_pattern', 'class_pattern'
            ]

            def get_priority(candidate):
                method = candidate['method']
                try:
                    return priority_order.index(method)
                except ValueError:
                    return len(priority_order)  # Put unknown methods at the end

            unique_candidates.sort(key=get_priority)

            return unique_candidates

        except Exception as e:
            print(f"[MENU_CLICK] Error deduplicating candidates: {e}")
            return candidates

    def _generate_menu_variations(self, menu_name: str) -> list:
        """Generate variations of menu name to handle different text formats"""
        variations = [menu_name]

        try:
            # Add lowercase version
            variations.append(menu_name.lower())

            # Add uppercase version
            variations.append(menu_name.upper())

            # Add version without spaces
            variations.append(menu_name.replace(' ', ''))

            # Add version with different separators
            variations.append(menu_name.replace(' ', '_'))
            variations.append(menu_name.replace(' ', '-'))

            # For compound words, try individual words
            words = menu_name.split()
            if len(words) > 1:
                for word in words:
                    if len(word) > 3:  # Only add meaningful words
                        variations.append(word)

            # Handle specific menu name patterns
            if 'Ruang' in menu_name:
                # Try without 'Ruang' prefix
                without_ruang = menu_name.replace('Ruang ', '').strip()
                if without_ruang:
                    variations.append(without_ruang)

            # Remove duplicates while preserving order
            unique_variations = []
            for var in variations:
                if var not in unique_variations and var.strip():
                    unique_variations.append(var.strip())

            return unique_variations

        except Exception as e:
            print(f"[MENU_CLICK] Error generating variations for {menu_name}: {e}")
            return [menu_name]

class SmartActionClicker:
    """Smart action clicking system that can handle any type of click action"""

    def __init__(self, driver, state_manager: StateManager):
        self.driver = driver
        self.state_manager = state_manager

    def smart_click(self, element, element_name: str = "Unknown") -> bool:
        """Alias for smart_click_element for compatibility"""
        return self.smart_click_element(element, element_name)

    def find_menu_location(self, menu_name: str) -> dict:
        """Find and store menu location information"""
        try:
            print(f"[LOCATION] Searching for menu location: {menu_name}")

            # Try multiple patterns to find the menu
            patterns = [
                f"//*[@text='{menu_name}' or @content-desc='{menu_name}']",
                f"//*[contains(@text, '{menu_name}') or contains(@content-desc, '{menu_name}')]",
                f"//android.widget.ImageView[@content-desc='{menu_name}']",
                f"//android.widget.TextView[@text='{menu_name}']",
                f"//*[@clickable='true' and (@text='{menu_name}' or @content-desc='{menu_name}')]"
            ]

            for pattern in patterns:
                try:
                    elements = self.driver.find_elements("xpath", pattern)
                    if elements:
                        element = elements[0]
                        location = element.location
                        size = element.size

                        location_info = {
                            'x': location['x'],
                            'y': location['y'],
                            'width': size['width'],
                            'height': size['height'],
                            'center_x': location['x'] + size['width'] // 2,
                            'center_y': location['y'] + size['height'] // 2,
                            'pattern': pattern,
                            'visible': element.is_displayed()
                        }

                        print(f"[LOCATION] Found {menu_name} at y={location['y']}, center=({location_info['center_x']}, {location_info['center_y']})")
                        return location_info

                except Exception as e:
                    continue

            print(f"[LOCATION] ❌ Could not find location for: {menu_name}")
            return None

        except Exception as e:
            print(f"[LOCATION] Error finding menu location: {e}")
            return None

    def scroll_to_menu_location(self, menu_name: str, target_location: dict) -> bool:
        """Scroll to bring menu into view based on its known location"""
        try:
            print(f"[SCROLL_TO_MENU] Scrolling to bring {menu_name} into view...")

            # Get current screen dimensions
            size = self.driver.get_window_size()
            screen_height = size['height']

            # Define visible area (excluding status bar and navigation)
            visible_top = 200  # Account for status bar
            visible_bottom = screen_height - 200  # Account for navigation bar

            target_y = target_location['center_y']

            # Check if menu is already in visible area
            if visible_top <= target_y <= visible_bottom:
                print(f"[SCROLL_TO_MENU] ✅ {menu_name} already in visible area (y={target_y})")
                return True

            # Calculate scroll direction and amount
            if target_y < visible_top:
                # Menu is above visible area - scroll up
                print(f"[SCROLL_TO_MENU] Menu above visible area, scrolling up...")
                scroll_direction = "up"
                scroll_amount = (visible_top - target_y) // 100 + 1
            else:
                # Menu is below visible area - scroll down
                print(f"[SCROLL_TO_MENU] Menu below visible area, scrolling down...")
                scroll_direction = "down"
                scroll_amount = (target_y - visible_bottom) // 100 + 1

            # Perform scrolling
            max_scrolls = min(scroll_amount, 5)  # Limit to 5 scrolls
            for i in range(max_scrolls):
                if scroll_direction == "up":
                    # Safe scroll up
                    start_x = size['width'] // 2
                    start_y = int(size['height'] * 0.6)
                    end_y = int(size['height'] * 0.8)
                    self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                else:
                    # Safe scroll down
                    start_x = size['width'] // 2
                    start_y = int(size['height'] * 0.7)
                    end_y = int(size['height'] * 0.3)
                    self.driver.swipe(start_x, start_y, start_x, end_y, 1000)

                time.sleep(1)
                print(f"[SCROLL_TO_MENU] Scroll {i+1}/{max_scrolls} completed")

                # Check if menu is now visible
                current_location = self.find_menu_location(menu_name)
                if current_location and current_location['visible']:
                    current_y = current_location['center_y']
                    if visible_top <= current_y <= visible_bottom:
                        print(f"[SCROLL_TO_MENU] ✅ {menu_name} now visible at y={current_y}")
                        return True

            print(f"[SCROLL_TO_MENU] ⚠️ Could not bring {menu_name} into view after {max_scrolls} scrolls")
            return False

        except Exception as e:
            print(f"[SCROLL_TO_MENU] Error scrolling to menu: {e}")
            return False

    def smart_click_menu_with_location(self, menu_name: str) -> bool:
        """Smart click menu with location-based positioning"""
        try:
            print(f"\n[SMART_CLICK] 🎯 Attempting to click menu with location: {menu_name}")

            # Step 1: Find menu location
            location_info = self.find_menu_location(menu_name)
            if not location_info:
                print(f"[SMART_CLICK] ❌ Could not find location for: {menu_name}")
                return False

            # Step 2: Check if menu is visible, if not scroll to it
            if not location_info['visible']:
                print(f"[SMART_CLICK] Menu not visible, scrolling to location...")
                if not self.scroll_to_menu_location(menu_name, location_info):
                    print(f"[SMART_CLICK] ❌ Could not scroll to menu: {menu_name}")
                    return False

                # Re-find location after scrolling
                location_info = self.find_menu_location(menu_name)
                if not location_info:
                    print(f"[SMART_CLICK] ❌ Lost menu after scrolling: {menu_name}")
                    return False

            # Step 3: Click the menu
            print(f"[SMART_CLICK] Clicking menu at location ({location_info['center_x']}, {location_info['center_y']})")

            # Try multiple click methods
            # Method 1: Find element and click
            try:
                elements = self.driver.find_elements("xpath", location_info['pattern'])
                if elements:
                    elements[0].click()
                    time.sleep(3)
                    print(f"[SMART_CLICK] ✅ Successfully clicked {menu_name} (element click)")
                    return True
            except:
                pass

            # Method 2: Tap using coordinates
            try:
                self.driver.tap([(location_info['center_x'], location_info['center_y'])])
                time.sleep(3)
                print(f"[SMART_CLICK] ✅ Successfully clicked {menu_name} (coordinate tap)")
                return True
            except:
                pass

            print(f"[SMART_CLICK] ❌ All click methods failed for: {menu_name}")
            return False

        except Exception as e:
            print(f"[SMART_CLICK] Error in smart click with location: {e}")
            return False

    def smart_click_element(self, element, element_name: str = "Unknown") -> bool:
        """Perform smart click action on element using multiple methods"""
        try:
            print(f"[SMART_CLICK] Attempting to click: {element_name}")

            # Method 1: Standard click
            if self._try_standard_click(element, element_name):
                return True

            # Method 2: Tap using coordinates
            if self._try_coordinate_tap(element, element_name):
                return True

            # Method 3: JavaScript click (for webviews)
            if self._try_javascript_click(element, element_name):
                return True

            # Method 4: Action chains
            if self._try_action_chains_click(element, element_name):
                return True

            # Method 5: Touch action
            if self._try_touch_action(element, element_name):
                return True

            # Method 6: Send ENTER key
            if self._try_send_enter(element, element_name):
                return True

            # Method 7: Parent element click
            if self._try_parent_click(element, element_name):
                return True

            print(f"[SMART_CLICK] All click methods failed for: {element_name}")
            return False

        except Exception as e:
            print(f"[SMART_CLICK] Error in smart click for {element_name}: {e}")
            return False

    def _try_standard_click(self, element, element_name: str) -> bool:
        """Try standard element click"""
        try:
            element.click()
            time.sleep(2)
            print(f"[SMART_CLICK] Standard click successful for: {element_name}")
            return True
        except Exception as e:
            print(f"[SMART_CLICK] Standard click failed for {element_name}: {e}")
            return False

    def _try_coordinate_tap(self, element, element_name: str) -> bool:
        """Try clicking using coordinates"""
        try:
            location = element.location
            size = element.size

            # Calculate center point
            center_x = location['x'] + size['width'] // 2
            center_y = location['y'] + size['height'] // 2

            # Use Appium's tap method
            self.driver.tap([(center_x, center_y)])
            time.sleep(2)
            print(f"[SMART_CLICK] Coordinate tap successful for: {element_name}")
            return True
        except Exception as e:
            print(f"[SMART_CLICK] Coordinate tap failed for {element_name}: {e}")
            return False

    def _try_javascript_click(self, element, element_name: str) -> bool:
        """Try JavaScript click (useful for webviews)"""
        try:
            self.driver.execute_script("arguments[0].click();", element)
            time.sleep(2)
            print(f"[SMART_CLICK] JavaScript click successful for: {element_name}")
            return True
        except Exception as e:
            print(f"[SMART_CLICK] JavaScript click failed for {element_name}: {e}")
            return False

    def _try_action_chains_click(self, element, element_name: str) -> bool:
        """Try using action chains"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)
            actions.click(element).perform()
            time.sleep(2)
            print(f"[SMART_CLICK] Action chains click successful for: {element_name}")
            return True
        except Exception as e:
            print(f"[SMART_CLICK] Action chains click failed for {element_name}: {e}")
            return False

    def _try_touch_action(self, element, element_name: str) -> bool:
        """Try using touch action"""
        try:
            from appium.webdriver.common.touch_action import TouchAction

            location = element.location
            size = element.size
            center_x = location['x'] + size['width'] // 2
            center_y = location['y'] + size['height'] // 2

            touch = TouchAction(self.driver)
            touch.tap(x=center_x, y=center_y).perform()
            time.sleep(2)
            print(f"[SMART_CLICK] Touch action successful for: {element_name}")
            return True
        except Exception as e:
            print(f"[SMART_CLICK] Touch action failed for {element_name}: {e}")
            return False

    def _try_send_enter(self, element, element_name: str) -> bool:
        """Try sending ENTER key to element"""
        try:
            from selenium.webdriver.common.keys import Keys
            element.send_keys(Keys.ENTER)
            time.sleep(2)
            print(f"[SMART_CLICK] Send ENTER successful for: {element_name}")
            return True
        except Exception as e:
            print(f"[SMART_CLICK] Send ENTER failed for {element_name}: {e}")
            return False

    def _try_parent_click(self, element, element_name: str) -> bool:
        """Try clicking parent element if direct click fails"""
        try:
            parent = element.find_element("xpath", "..")
            if parent:
                parent.click()
                time.sleep(2)
                print(f"[SMART_CLICK] Parent click successful for: {element_name}")
                return True
        except Exception as e:
            print(f"[SMART_CLICK] Parent click failed for {element_name}: {e}")
            return False

class EnhancedAppCrawler:
    def __init__(self, driver, package_name: str, config: dict = None):
        self.driver = driver
        self.package_name = package_name
        self.config = config or {}
        self.state_manager = StateManager(package_name)
        self.navigator = SmartNavigationEngine(driver, self.state_manager, config)
        self.collector = ComprehensiveElementCollector(driver, self.state_manager, config)
        self.recovery_system = CrashRecoverySystem(self.state_manager, config)
        self.menu_detector = SmartMenuDetector(driver, self.state_manager)
        self.smart_clicker = SmartActionClicker(driver, self.state_manager)

    def _click_menu_item(self, menu_name: str) -> bool:
        """Click a menu item by name with enhanced error handling"""
        try:
            print(f"[CLICK_MENU] Attempting to click menu: {menu_name}")

            # Step 1: Use smart scroll to ensure we're at the right position
            if hasattr(self.navigator, 'page_tracker'):
                self.navigator.page_tracker.smart_scroll_to_top(self.driver)

            # Step 2: Try smart location-based clicking first
            if self.smart_clicker.smart_click_menu_with_location(menu_name):
                print(f"[CLICK_MENU] ✅ Successfully clicked {menu_name} with location-based method")
                # Mark new page as opened
                if hasattr(self.navigator, 'page_tracker'):
                    self.navigator.page_tracker.mark_page_opened(f"Menu: {menu_name}")
                return True

            # Step 3: Fallback to traditional patterns
            print(f"[CLICK_MENU] Location-based method failed, trying traditional patterns...")
            patterns = [
                f"//*[@text='{menu_name}' or @content-desc='{menu_name}']",
                f"//*[contains(@text, '{menu_name}') or contains(@content-desc, '{menu_name}')]",
                f"//android.widget.ImageView[@content-desc='{menu_name}']",
                f"//android.widget.TextView[@text='{menu_name}']"
            ]

            for pattern in patterns:
                try:
                    elements = self.driver.find_elements("xpath", pattern)
                    if elements:
                        element = elements[0]
                        print(f"[CLICK_MENU] Found {menu_name} using pattern: {pattern[:50]}...")

                        # Use smart clicker for reliable clicking
                        if self.smart_clicker.smart_click(element, f"Menu: {menu_name}"):
                            print(f"[CLICK_MENU] ✅ Successfully clicked {menu_name}")
                            # Mark new page as opened
                            if hasattr(self.navigator, 'page_tracker'):
                                self.navigator.page_tracker.mark_page_opened(f"Menu: {menu_name}")
                            time.sleep(2)  # Wait for navigation
                            return True
                        else:
                            print(f"[CLICK_MENU] ⚠️ Smart click failed for {menu_name}")

                except Exception as e:
                    print(f"[CLICK_MENU] Error with pattern {pattern[:30]}...: {e}")
                    continue

            print(f"[CLICK_MENU] ❌ Could not find or click {menu_name}")
            return False

        except Exception as e:
            print(f"[CLICK_MENU] Error clicking menu {menu_name}: {e}")

            # Check if this is a crash
            if "instrumentation process is not running" in str(e):
                print(f"[CLICK_MENU] 🚨 App crash detected while clicking {menu_name}")
                if attempt_app_recovery(self.driver):
                    print(f"[CLICK_MENU] ✅ Recovery successful, retrying {menu_name}")
                    return self._click_menu_item(menu_name)  # Retry once
                else:
                    print(f"[CLICK_MENU] ❌ Recovery failed for {menu_name}")

            return False

    def start_comprehensive_crawl(self) -> Dict[str, Any]:
        """Start the comprehensive crawling process"""
        try:
            print("[ENHANCED_CRAWL] Starting comprehensive app crawling...")

            # Try to load previous state
            if self.state_manager.load_state():
                print("[ENHANCED_CRAWL] Resuming from previous state...")
                return self._resume_crawl()
            else:
                print("[ENHANCED_CRAWL] Starting fresh crawl...")
                return self._start_fresh_crawl()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in comprehensive crawl: {e}")
            return self._handle_crawl_error(e)

    def _start_fresh_crawl(self) -> Dict[str, Any]:
        """Start crawling from the beginning"""
        try:
            # Initialize state
            self.state_manager.update_navigation_path("Main Page", PageContext.NATIVE)
            self.state_manager.set_recovery_checkpoint("Starting fresh crawl")

            # Initialize page tracker
            if hasattr(self.navigator, 'page_tracker'):
                self.navigator.page_tracker.mark_page_opened("Main Page")

            # Step 1: Collect all elements on main page
            print("[ENHANCED_CRAWL] Step 1: Collecting all elements on main page...")
            main_page_elements = self._collect_complete_page("Main Page")

            # Step 2: Navigate through all main menus
            print("[ENHANCED_CRAWL] Step 2: Navigating through main menus...")
            self._crawl_all_main_menus()

            # Step 3: Generate final results
            return self._generate_final_results()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in fresh crawl: {e}")
            return self._attempt_recovery_and_continue(e)

    def _crawl_submenus(self, parent_menu: str, depth: int = 1):
        """Crawl through submenus hierarchically"""
        try:
            print(f"[SUBMENU_CRAWL] Starting submenu crawl for {parent_menu} at depth {depth}")

            if depth > 3:  # Limit depth to prevent infinite recursion
                print(f"[SUBMENU_CRAWL] Max depth reached for {parent_menu}")
                return

            # Scroll to top to see all submenus
            self._scroll_to_top()
            time.sleep(0.5)  # Reduced from 1 to 0.5

            # Collect all clickable elements that could be submenus
            submenus = self._find_submenu_items()

            if not submenus:
                print(f"[SUBMENU_CRAWL] No submenus found in {parent_menu}")
                return

            print(f"[SUBMENU_CRAWL] Found {len(submenus)} potential submenus in {parent_menu}")

            # Navigate through each submenu
            for i, submenu_info in enumerate(submenus, 1):
                try:
                    submenu_name = submenu_info.get('text', submenu_info.get('content_desc', f'Submenu_{i}'))
                    print(f"[SUBMENU_CRAWL] Clicking submenu {i}/{len(submenus)}: {submenu_name}")

                    # Scroll to top before clicking
                    self._scroll_to_top()
                    time.sleep(0.5)  # Reduced from 1 to 0.5

                    # Click the submenu
                    if self._click_submenu_item(submenu_info):
                        # Mark new page opened
                        if hasattr(self.navigator, 'page_tracker'):
                            self.navigator.page_tracker.mark_page_opened(f"Submenu: {submenu_name}")

                        # Wait for page to load
                        time.sleep(1)  # Reduced from 2 to 1

                        # Collect elements in submenu
                        submenu_elements = self._collect_complete_page(f"{parent_menu} > {submenu_name}")
                        print(f"[SUBMENU_CRAWL] Collected {len(submenu_elements)} elements from {submenu_name}")

                        # Recursively crawl sub-submenus
                        self._crawl_submenus(f"{parent_menu} > {submenu_name}", depth + 1)

                        # Go back to parent menu
                        self._navigate_back()
                        time.sleep(1)  # Reduced from 2 to 1

                        # Mark back to parent
                        if hasattr(self.navigator, 'page_tracker'):
                            self.navigator.page_tracker.mark_page_opened(f"Menu: {parent_menu}")

                    else:
                        print(f"[SUBMENU_CRAWL] Could not click submenu: {submenu_name}")

                except Exception as e:
                    print(f"[SUBMENU_CRAWL] Error processing submenu {i}: {e}")
                    # Try to recover by going back
                    self._navigate_back()
                    time.sleep(1)
                    continue

            print(f"[SUBMENU_CRAWL] Completed submenu crawl for {parent_menu}")

        except Exception as e:
            print(f"[SUBMENU_CRAWL] Error in submenu crawl for {parent_menu}: {e}")

    def _find_submenu_items(self) -> list:
        """Find potential submenu items on current page"""
        try:
            submenu_items = []

            # Get all clickable elements
            clickable_elements = self.driver.find_elements("xpath", "//*[@clickable='true']")

            for element in clickable_elements:
                try:
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    class_name = element.get_attribute('class') or ''

                    # Filter out navigation elements and main menus
                    if self._is_potential_submenu(text, desc, class_name):
                        submenu_items.append({
                            'element': element,
                            'text': text,
                            'content_desc': desc,
                            'class': class_name
                        })

                except Exception as e:
                    continue

            print(f"[FIND_SUBMENU] Found {len(submenu_items)} potential submenu items")
            return submenu_items[:10]  # Limit to first 10 to avoid too many

        except Exception as e:
            print(f"[FIND_SUBMENU] Error finding submenu items: {e}")
            return []

    def _is_potential_submenu(self, text: str, desc: str, class_name: str) -> bool:
        """Check if element is a potential submenu item"""
        # Skip empty elements
        if not text.strip() and not desc.strip():
            return False

        # Skip null elements
        if text == 'null' or desc == 'null':
            return False

        # Skip navigation elements
        nav_keywords = ['beranda', 'home', 'back', 'kembali', 'tab', 'navigation']
        content = (text + ' ' + desc).lower()
        if any(keyword in content for keyword in nav_keywords):
            return False

        # Skip main menu items (they should be on main page)
        main_menus = ['ruang gtk', 'ruang murid', 'ruang sekolah', 'ruang bahasa']
        if any(menu in content for menu in main_menus):
            return False

        # Include if has meaningful content
        if len(text.strip()) > 2 or len(desc.strip()) > 2:
            return True

        return False

    def _click_submenu_item(self, submenu_info: dict) -> bool:
        """Click a submenu item"""
        try:
            element = submenu_info['element']
            name = submenu_info.get('text', submenu_info.get('content_desc', 'Unknown'))

            # Use smart clicker
            if self.smart_clicker.smart_click(element, f"Submenu: {name}"):
                print(f"[CLICK_SUBMENU] ✅ Successfully clicked submenu: {name}")
                return True
            else:
                print(f"[CLICK_SUBMENU] ❌ Failed to click submenu: {name}")
                return False

        except Exception as e:
            print(f"[CLICK_SUBMENU] Error clicking submenu: {e}")
            return False

    def _navigate_back(self):
        """Navigate back to previous page"""
        try:
            print("[NAVIGATE_BACK] Going back to previous page...")

            # Method 1: Use back button
            try:
                self.driver.back()
                print("[NAVIGATE_BACK] ✅ Used back button")
                return True
            except:
                pass

            # Method 2: Look for back/close buttons
            back_patterns = [
                "//*[@content-desc='Navigate up']",
                "//*[@content-desc='Back']",
                "//*[@text='Back']",
                "//*[contains(@content-desc, 'back')]",
                "//*[contains(@text, 'Kembali')]"
            ]

            for pattern in back_patterns:
                try:
                    elements = self.driver.find_elements("xpath", pattern)
                    if elements:
                        elements[0].click()
                        print("[NAVIGATE_BACK] ✅ Used back button element")
                        return True
                except:
                    continue

            print("[NAVIGATE_BACK] ⚠️ Could not find back button")
            return False

        except Exception as e:
            print(f"[NAVIGATE_BACK] Error navigating back: {e}")
            return False

    def _navigate_back_to_main(self):
        """Navigate back to main page from any submenu"""
        try:
            print("[NAVIGATE_MAIN] Navigating back to main page...")

            max_attempts = 3
            for attempt in range(max_attempts):
                # Try to go back
                if self._navigate_back():
                    time.sleep(2)

                    # Check if we're on main page by looking for main menu items
                    main_indicators = [
                        "//*[@text='Ruang GTK']",
                        "//*[@text='Ruang Murid']",
                        "//*[@text='Ruang Sekolah']"
                    ]

                    main_elements_found = 0
                    for indicator in main_indicators:
                        try:
                            elements = self.driver.find_elements("xpath", indicator)
                            if elements:
                                main_elements_found += 1
                        except:
                            continue

                    if main_elements_found >= 2:
                        print(f"[NAVIGATE_MAIN] ✅ Successfully reached main page (attempt {attempt + 1})")
                        # Mark main page as opened
                        if hasattr(self.navigator, 'page_tracker'):
                            self.navigator.page_tracker.mark_page_opened("Main Page")
                        return True
                    else:
                        print(f"[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt {attempt + 1})")
                else:
                    print(f"[NAVIGATE_MAIN] Back navigation failed (attempt {attempt + 1})")

            print("[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts")
            return False

        except Exception as e:
            print(f"[NAVIGATE_MAIN] Error navigating to main: {e}")
            return False

    def _resume_crawl(self) -> Dict[str, Any]:
        """Resume crawling from saved state"""
        try:
            print("[ENHANCED_CRAWL] Resuming crawl from saved state...")

            # Attempt to restore session and navigation state
            current_path = self.state_manager.crawl_state.navigation_path.path

            if len(current_path) > 1:
                # Try to navigate back to where we were
                success = self.recovery_system.replay_navigation_path(self.driver, current_path)
                if not success:
                    print("[ENHANCED_CRAWL] Could not restore navigation state, starting fresh...")
                    return self._start_fresh_crawl()

            # Continue from where we left off
            return self._continue_crawl_from_current_state()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error resuming crawl: {e}")
            return self._attempt_recovery_and_continue(e)

    def _is_at_top_of_page(self):
        """Check if we're already at the top of the page to prevent pull-to-refresh"""
        try:
            # Method 1: Check for typical top-of-page indicators
            top_indicators = [
                "//*[contains(@text, 'Jelajahi') or contains(@content-desc, 'Jelajahi')]",  # Main header
                "//*[contains(@text, 'Temukan') or contains(@content-desc, 'Temukan')]",   # Subtitle
                "//*[@text='Ruang GTK' or @content-desc='Ruang GTK']",  # First menu item
            ]

            for indicator in top_indicators:
                try:
                    elements = self.driver.find_elements("xpath", indicator)
                    if elements:
                        # Check if element is visible in upper portion of screen
                        element = elements[0]
                        location = element.location
                        if location['y'] < 300:  # Element is in top 300 pixels
                            print(f"[SCROLL_CHECK] ✅ Already at top - found top indicator at y={location['y']}")
                            return True
                except:
                    continue

            # Method 2: Try a small scroll and see if content changes
            try:
                # Get current page source
                initial_source = self.driver.page_source
                initial_hash = hash(initial_source[:1000])  # Hash first 1000 chars

                # Try a very small scroll down and back up
                size = self.driver.get_window_size()
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.3)
                end_y = int(size['height'] * 0.4)

                # Small scroll down
                self.driver.swipe(start_x, start_y, start_x, end_y, 200)
                time.sleep(0.5)

                # Small scroll back up
                self.driver.swipe(start_x, end_y, start_x, start_y, 200)
                time.sleep(0.5)

                # Check if content changed (indicating refresh)
                final_source = self.driver.page_source
                final_hash = hash(final_source[:1000])

                if initial_hash == final_hash:
                    print("[SCROLL_CHECK] ✅ Content unchanged - likely at top")
                    return True
                else:
                    print("[SCROLL_CHECK] ⚠️ Content changed - may have triggered refresh")
                    return True  # Assume at top to prevent further scrolling

            except:
                pass

            return False

        except Exception as e:
            print(f"[SCROLL_CHECK] Error checking top position: {e}")
            return True  # Assume at top to be safe

    def _scroll_to_top(self):
        """Scroll to top of the page with pull-to-refresh prevention"""
        try:
            print("[SCROLL_TO_TOP] Checking if scroll is needed...")

            # First check if we're already at the top
            if self._is_at_top_of_page():
                print("[SCROLL_TO_TOP] ✅ Already at top of page - skipping scroll to prevent refresh")
                return

            print("[SCROLL_TO_TOP] Not at top, performing careful scroll...")

            # Perform very conservative scrolling to avoid pull-to-refresh
            size = self.driver.get_window_size()
            start_x = size['width'] // 2

            # Start from middle of screen (not top) to avoid pull-to-refresh zone
            start_y = int(size['height'] * 0.5)  # Start from middle
            end_y = int(size['height'] * 0.8)    # Scroll to bottom area

            # Do maximum 3 gentle scrolls (not 5)
            for i in range(3):
                print(f"[SCROLL_TO_TOP] Gentle scroll {i+1}/3...")

                # Check if we're at top before each scroll
                if self._is_at_top_of_page():
                    print(f"[SCROLL_TO_TOP] ✅ Reached top after {i} scrolls")
                    break

                # Gentle scroll with longer duration to be less aggressive
                self.driver.swipe(start_x, start_y, start_x, end_y, 1500)  # Slower swipe
                time.sleep(1)  # Longer pause between scrolls

            print("[SCROLL_TO_TOP] ✅ Scroll to top completed")

        except Exception as e:
            error_msg = str(e)
            print(f"[SCROLL_TO_TOP] Error scrolling to top: {e}")

            # Check if this is a crash
            if "instrumentation process is not running" in error_msg or "crashed" in error_msg.lower():
                print("[SCROLL_TO_TOP] 🚨 App crash detected during scrolling!")
                if attempt_app_recovery(self.driver):
                    print("[SCROLL_TO_TOP] ✅ Recovery successful after scroll error")
                else:
                    print("[SCROLL_TO_TOP] ❌ Recovery failed after scroll error")
            else:
                print("[SCROLL_TO_TOP] ⚠️ Non-crash scroll error, continuing...")

    def _deduplicate_elements(self, elements: List[ElementInfo]) -> List[ElementInfo]:
        """Remove duplicate elements"""
        seen = set()
        unique_elements = []

        for element in elements:
            # Create unique key based on xpath, text, and content_desc
            key = f"{element.xpath}_{element.text}_{element.content_desc}"
            if key not in seen:
                seen.add(key)
                unique_elements.append(element)

        return unique_elements

    def _continue_crawl_from_current_state(self) -> Dict[str, Any]:
        """Continue crawling from current state"""
        try:
            # Get current state
            current_state = self.state_manager.get_current_state()

            # Continue from current menu index
            main_menus = self.navigator.main_menus
            start_index = current_state.current_menu_index

            # Continue crawling remaining menus
            for menu_index in range(start_index, len(main_menus)):
                menu_name = main_menus[menu_index]
                print(f"[ENHANCED_CRAWL] Continuing with menu {menu_index + 1}/{len(main_menus)}: {menu_name}")

                # Process menu (similar to _crawl_all_main_menus)
                self.state_manager.crawl_state.current_menu_index = menu_index
                self.state_manager.set_recovery_checkpoint(f"Continuing with menu: {menu_name}")

                if not self.navigator.navigate_to_main_page():
                    continue

                if self._click_menu_item(menu_name):
                    menu_elements = self._collect_complete_page(menu_name)
                    self._crawl_submenus(menu_name, 1)

            return self._generate_final_results()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error continuing crawl: {e}")
            return self._attempt_recovery_and_continue(e)

    def _attempt_recovery_and_continue(self, error: Exception) -> Dict[str, Any]:
        """Attempt recovery after error and continue"""
        try:
            print(f"[ENHANCED_CRAWL] Attempting recovery after error: {error}")

            # Try to recover
            recovered_driver = self.recovery_system.attempt_recovery(self.package_name)

            if recovered_driver:
                self.driver = recovered_driver
                print("[ENHANCED_CRAWL] Recovery successful, continuing crawl...")
                return self._continue_crawl_from_current_state()
            else:
                print("[ENHANCED_CRAWL] Recovery failed, generating results with collected data...")
                return self._generate_final_results()

        except Exception as recovery_error:
            print(f"[ENHANCED_CRAWL] Recovery attempt failed: {recovery_error}")
            return self._generate_final_results()

    def _continue_crawl_from_current_state(self) -> Dict[str, Any]:
        """Continue crawling from current state"""
        try:
            # Get current state
            current_state = self.state_manager.get_current_state()

            # Continue from current menu index
            main_menus = self.navigator.main_menus
            start_index = current_state.current_menu_index

            print(f"[ENHANCED_CRAWL] Continuing from menu index {start_index}")

            # Continue crawling remaining menus
            for menu_index in range(start_index, len(main_menus)):
                menu_name = main_menus[menu_index]
                print(f"[ENHANCED_CRAWL] Continuing with menu {menu_index + 1}/{len(main_menus)}: {menu_name}")

                # Process menu (similar to _crawl_all_main_menus)
                self.state_manager.crawl_state.current_menu_index = menu_index
                self.state_manager.set_recovery_checkpoint(f"Continuing with menu: {menu_name}")

                if not self._prepare_main_page_for_menu_click():
                    continue

                if self._find_and_click_main_menu(menu_name):
                    menu_elements = self._collect_complete_page(menu_name)
                    self._crawl_submenus(menu_name, 1)

            return self._generate_final_results()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error continuing crawl: {e}")
            return self._attempt_recovery_and_continue(e)

    def _handle_crawl_error(self, error: Exception) -> Dict[str, Any]:
        """Handle crawl error and return results"""
        print(f"[ENHANCED_CRAWL] Handling crawl error: {error}")
        return self._generate_final_results()

    def _generate_final_results(self) -> Dict[str, Any]:
        """Generate final crawl results"""
        try:
            current_state = self.state_manager.get_current_state()

            # Convert ElementInfo objects to dictionaries for JSON serialization
            elements_dict = []
            for element in current_state.collected_elements:
                element_dict = asdict(element)
                # Convert enums to strings
                element_dict['page_context'] = element.page_context.value
                element_dict['navigation_level'] = element.navigation_level.value
                elements_dict.append(element_dict)

            # Convert PageState objects to dictionaries
            page_states_dict = {}
            for page_name, page_state in current_state.page_states.items():
                page_dict = asdict(page_state)
                page_dict['context'] = page_state.context.value
                page_dict['navigation_level'] = page_state.navigation_level.value
                # Convert elements in page state
                page_dict['elements'] = []
                for element in page_state.elements:
                    elem_dict = asdict(element)
                    elem_dict['page_context'] = element.page_context.value
                    elem_dict['navigation_level'] = element.navigation_level.value
                    page_dict['elements'].append(elem_dict)
                page_states_dict[page_name] = page_dict

            results = {
                "crawl_summary": {
                    "total_elements_collected": len(current_state.collected_elements),
                    "total_pages_visited": len(current_state.visited_pages),
                    "crash_count": current_state.crash_count,
                    "last_successful_action": current_state.last_successful_action,
                    "crawl_completed": True
                },
                "navigation_path": {
                    "final_path": current_state.navigation_path.path,
                    "final_depth": current_state.navigation_path.current_depth,
                    "context_history": [ctx.value for ctx in current_state.navigation_path.context_history]
                },
                "collected_elements": elements_dict,
                "page_states": page_states_dict,
                "visited_pages": list(current_state.visited_pages)
            }

            print(f"[ENHANCED_CRAWL] Generated final results: {results['crawl_summary']}")
            return results

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error generating final results: {e}")
            return {
                "crawl_summary": {
                    "total_elements_collected": 0,
                    "total_pages_visited": 0,
                    "crash_count": 0,
                    "last_successful_action": "Error generating results",
                    "crawl_completed": False
                },
                "error": str(e)
            }

    def _collect_complete_page(self, page_name: str) -> List[ElementInfo]:
        """Collect all elements on a page by scrolling to bottom"""
        try:
            print(f"[ENHANCED_CRAWL] Collecting complete page: {page_name}")

            # Scroll to top first
            self._scroll_to_top()

            # Collect all elements by scrolling step by step
            all_elements = self.collector.scroll_to_bottom_step_by_step()

            # Also collect all meaningful elements specifically
            meaningful_elements = self.collector.collect_all_meaningful_elements()

            # Combine and deduplicate
            combined_elements = self._deduplicate_elements(all_elements + meaningful_elements)

            # Create page state
            page_context = self.navigator.detect_page_context()
            nav_level = self.navigator.determine_navigation_level(page_name)

            page_state = PageState(
                page_name=page_name,
                context=page_context,
                navigation_level=nav_level,
                elements=combined_elements,
                fully_explored=True,
                scroll_position=0,
                page_source_hash=hashlib.md5(self.driver.page_source.encode()).hexdigest(),
                timestamp=datetime.datetime.now().isoformat()
            )

            # Save state
            self.state_manager.add_page_state(page_state)
            self.state_manager.add_elements(combined_elements)
            self.state_manager.save_state()

            print(f"[ENHANCED_CRAWL] Collected {len(combined_elements)} elements from {page_name}")
            return combined_elements

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error collecting page {page_name}: {e}")
            return []

    def _crawl_all_main_menus(self):
        """Enhanced crawl through all main menus with intelligent detection"""
        try:
            main_menus = self.navigator.main_menus

            print(f"[ENHANCED_CRAWL] Starting to crawl {len(main_menus)} main menus")

            for menu_index, menu_name in enumerate(main_menus):
                try:
                    print(f"\n[ENHANCED_CRAWL] ===== Processing main menu {menu_index + 1}/{len(main_menus)}: {menu_name} =====")

                    # Update state
                    self.state_manager.crawl_state.current_menu_index = menu_index
                    self.state_manager.set_recovery_checkpoint(f"Processing main menu: {menu_name}")

                    # Navigate to main page first and ensure we're at the top
                    if not self._prepare_main_page_for_menu_click():
                        print(f"[ENHANCED_CRAWL] Could not prepare main page for menu: {menu_name}")
                        continue

                    # Try to find and click the menu with enhanced detection
                    if self._find_and_click_main_menu(menu_name):
                        print(f"[ENHANCED_CRAWL] Successfully clicked menu: {menu_name}")

                        # Wait for page to load
                        time.sleep(3)

                        # Collect elements on the menu page
                        menu_elements = self._collect_complete_page(menu_name)
                        print(f"[ENHANCED_CRAWL] Collected {len(menu_elements)} elements from {menu_name}")

                        # Crawl submenus recursively
                        self._crawl_submenus(menu_name, 1)

                        print(f"[ENHANCED_CRAWL] Completed crawling menu: {menu_name}")

                    else:
                        print(f"[ENHANCED_CRAWL] Could not find or click menu: {menu_name}")
                        # Show debug information to help understand what's available
                        self.debug_page_elements(20)

                except Exception as e:
                    print(f"[ENHANCED_CRAWL] Error processing menu {menu_name}: {e}")
                    # Try recovery
                    recovered_driver = self.recovery_system.attempt_recovery(self.package_name)
                    if recovered_driver:
                        self.driver = recovered_driver
                        print(f"[ENHANCED_CRAWL] Recovered from error, continuing with next menu")
                        continue
                    else:
                        print(f"[ENHANCED_CRAWL] Could not recover from error in menu {menu_name}")
                        break

            print(f"\n[ENHANCED_CRAWL] Completed crawling all main menus")

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in main menu crawling: {e}")

    def _prepare_main_page_for_menu_click(self) -> bool:
        """Prepare main page for menu clicking"""
        try:
            # Navigate to main page
            if not self.navigator.navigate_to_main_page():
                return False

            # Scroll to top to ensure we can see main menus
            self._scroll_to_top()
            time.sleep(2)
            return True

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error preparing main page: {e}")
            return False

    def _find_and_click_main_menu(self, menu_name: str) -> bool:
        """Enhanced menu finding and clicking with scrolling"""
        try:
            # First try without scrolling
            if self._click_menu_item(menu_name):
                return True

            # If not found, try scrolling to find the menu
            print(f"[ENHANCED_CRAWL] Menu {menu_name} not found, trying with scrolling...")

            max_scroll_attempts = 5
            for scroll_attempt in range(max_scroll_attempts):
                # Scroll down to reveal more menus
                try:
                    size = self.driver.get_window_size()
                    start_x = size['width'] // 2
                    start_y = int(size['height'] * 0.7)
                    end_y = int(size['height'] * 0.3)
                    self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                    time.sleep(2)
                except:
                    pass

                # Try to click menu after scrolling
                if self._click_menu_item(menu_name):
                    print(f"[ENHANCED_CRAWL] Found and clicked {menu_name} after scrolling")
                    return True

            print(f"[ENHANCED_CRAWL] Could not find {menu_name} even after scrolling")
            return False

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in enhanced menu finding for {menu_name}: {e}")
            return False

    def debug_page_elements(self, limit: int = 30):
        """Debug function to show available elements on current page"""
        try:
            print(f"\n[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first {limit}) =====")

            # Get all elements with text or content-desc
            all_elements = self.driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")

            print(f"[DEBUG] Found {len(all_elements)} elements with text/content-desc")

            for i, element in enumerate(all_elements[:limit]):
                try:
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    class_name = element.get_attribute('class') or ''
                    clickable = element.get_attribute('clickable') == 'true'
                    resource_id = element.get_attribute('resource-id') or ''

                    click_indicator = "🔘" if clickable else "⚪"

                    print(f"[DEBUG] {i+1:2d}. {click_indicator} text='{text[:30]}' desc='{desc[:30]}' class='{class_name.split('.')[-1]}' id='{resource_id.split('/')[-1] if '/' in resource_id else resource_id}'")

                except Exception as e:
                    print(f"[DEBUG] {i+1:2d}. Error getting element info: {e}")

            print(f"[DEBUG] ===== END PAGE ELEMENTS =====\n")

        except Exception as e:
            print(f"[DEBUG] Error in debug_page_elements: {e}")

class GherkinGenerationEngine:
    def __init__(self, config: dict):
        self.config = config
        self.ai_model = config.get('ai_model', 'phi3:medium')

    def generate_gherkin_scenarios(self, crawl_results: Dict[str, Any], package_name: str) -> str:
        """Generate comprehensive Gherkin scenarios from crawl results"""
        try:
            print("[GHERKIN] Starting Gherkin scenario generation...")

            # Prepare data for AI processing
            elements_summary = self._prepare_elements_summary(crawl_results)
            navigation_summary = self._prepare_navigation_summary(crawl_results)

            # Generate scenarios using AI
            gherkin_content = self._generate_ai_scenarios(elements_summary, navigation_summary, package_name)

            # Post-process and validate
            final_gherkin = self._post_process_gherkin(gherkin_content, package_name)

            print("[GHERKIN] Gherkin generation completed")
            return final_gherkin

        except Exception as e:
            print(f"[GHERKIN] Error generating Gherkin scenarios: {e}")
            return self._generate_fallback_gherkin(crawl_results, package_name)

    def _prepare_elements_summary(self, crawl_results: Dict[str, Any]) -> str:
        """Prepare a summary of collected elements for AI processing"""
        try:
            elements = crawl_results.get('collected_elements', [])

            # Group elements by page/navigation level
            grouped_elements = {}
            for element in elements:
                nav_level = element.get('navigation_level', 'unknown')
                if nav_level not in grouped_elements:
                    grouped_elements[nav_level] = []

                # Create meaningful element description
                element_desc = {
                    'text': element.get('text', ''),
                    'content_desc': element.get('content_desc', ''),
                    'clickable': element.get('clickable', False),
                    'class_name': element.get('class_name', ''),
                    'page_context': element.get('page_context', 'unknown')
                }
                grouped_elements[nav_level].append(element_desc)

            # Create summary text
            summary_parts = []
            for level, elements_list in grouped_elements.items():
                summary_parts.append(f"\n{level.upper()} ELEMENTS:")

                # Focus on clickable and meaningful elements
                meaningful_elements = [
                    e for e in elements_list
                    if (e['clickable'] or e['text'].strip() or e['content_desc'].strip())
                ]

                for element in meaningful_elements[:20]:  # Limit to avoid token overflow
                    text = element['text'] or element['content_desc'] or 'Unnamed element'
                    context = element['page_context']
                    clickable = "clickable" if element['clickable'] else "non-clickable"
                    summary_parts.append(f"  - {text} ({clickable}, {context})")

            return '\n'.join(summary_parts)

        except Exception as e:
            print(f"[GHERKIN] Error preparing elements summary: {e}")
            return "Error preparing elements summary"

    def _prepare_navigation_summary(self, crawl_results: Dict[str, Any]) -> str:
        """Prepare navigation flow summary"""
        try:
            navigation = crawl_results.get('navigation_path', {})
            page_states = crawl_results.get('page_states', {})

            summary_parts = [
                "NAVIGATION FLOW:",
                f"Final navigation path: {' -> '.join(navigation.get('final_path', []))}",
                f"Maximum depth reached: {navigation.get('final_depth', 0)}",
                f"Total pages visited: {len(crawl_results.get('visited_pages', []))}",
                "\nPAGE DETAILS:"
            ]

            for page_name, page_info in page_states.items():
                elements_count = len(page_info.get('elements', []))
                context = page_info.get('context', 'unknown')
                summary_parts.append(f"  - {page_name}: {elements_count} elements ({context})")

            return '\n'.join(summary_parts)

        except Exception as e:
            print(f"[GHERKIN] Error preparing navigation summary: {e}")
            return "Error preparing navigation summary"

    def _generate_ai_scenarios(self, elements_summary: str, navigation_summary: str, package_name: str) -> str:
        """Use AI to generate human-readable Gherkin scenarios"""
        try:
            prompt = f"""
You are an expert QA engineer creating comprehensive Gherkin test scenarios for an Android application.

APPLICATION: {package_name}

COLLECTED UI ELEMENTS:
{elements_summary}

NAVIGATION STRUCTURE:
{navigation_summary}

TASK: Create comprehensive, human-readable Gherkin scenarios that cover:
1. Main navigation flows through all discovered menus and submenus
2. Element interaction scenarios for clickable components
3. Content verification scenarios for important text/information
4. Cross-platform scenarios (native, webview, chrome contexts)
5. Error handling and edge cases

REQUIREMENTS:
- Use natural, human-readable language in scenario descriptions
- Create realistic user journeys that make business sense
- Include proper Given/When/Then structure
- Add meaningful scenario names and descriptions
- Group related scenarios under appropriate features
- Include data validation where applicable
- Consider different user personas and use cases

EXAMPLE FORMAT:
Feature: Main Navigation
  As a user of the application
  I want to navigate through different sections
  So that I can access all available features

  Scenario: Navigate to Teacher Workspace
    Given I am on the main page
    When I tap on "Ruang GTK" menu
    Then I should see the teacher workspace page
    And I should see performance management options

Generate comprehensive scenarios covering all discovered elements and navigation paths.
Focus on creating meaningful, business-relevant test cases that a human tester would understand and execute.
"""

            # Query AI model
            gherkin_content = query_ollama(prompt, self.ai_model)
            return gherkin_content

        except Exception as e:
            print(f"[GHERKIN] Error generating AI scenarios: {e}")
            return self._generate_basic_scenarios(elements_summary, navigation_summary, package_name)

    def _generate_basic_scenarios(self, elements_summary: str, navigation_summary: str, package_name: str) -> str:
        """Generate basic scenarios without AI"""
        try:
            basic_gherkin = f"""Feature: {package_name} Application Testing
  As a user of the {package_name} application
  I want to interact with all available features
  So that I can verify the application works correctly

  Scenario: Launch Application
    Given the application is installed
    When I launch the application
    Then I should see the main page
    And all main menu items should be visible

  Scenario: Navigate Main Menus
    Given I am on the main page
    When I tap on each main menu item
    Then I should navigate to the corresponding section
    And I should be able to return to the main page

  Scenario: Verify Page Content
    Given I navigate to different pages
    When I scroll through the content
    Then all text and interactive elements should be visible
    And clickable elements should respond to taps

  Scenario: Test Navigation Flow
    Given I am on any page
    When I use back navigation
    Then I should return to the previous page
    And the navigation state should be consistent
"""
            return basic_gherkin

        except Exception as e:
            print(f"[GHERKIN] Error generating basic scenarios: {e}")
            return f"# Error generating scenarios for {package_name}"

    def _post_process_gherkin(self, gherkin_content: str, package_name: str) -> str:
        """Post-process and validate Gherkin content"""
        try:
            # Basic validation and cleanup
            lines = gherkin_content.split('\n')
            processed_lines = []

            for line in lines:
                # Clean up common AI artifacts
                line = line.strip()
                if line and not line.startswith('#'):
                    processed_lines.append(line)

            # Ensure proper Gherkin structure
            if not any(line.startswith('Feature:') for line in processed_lines):
                processed_lines.insert(0, f"Feature: {package_name} Application Testing")

            return '\n'.join(processed_lines)

        except Exception as e:
            print(f"[GHERKIN] Error post-processing Gherkin: {e}")
            return gherkin_content

    def _generate_fallback_gherkin(self, crawl_results: Dict[str, Any], package_name: str) -> str:
        """Generate fallback Gherkin when AI fails"""
        return f"""Feature: {package_name} Basic Testing
  As a tester
  I want to verify basic application functionality
  So that I can ensure the app works correctly

  Scenario: Application Launch
    Given the application is installed
    When I launch the application
    Then the application should start successfully

  Scenario: Basic Navigation
    Given I am on the main page
    When I interact with available elements
    Then the application should respond appropriately
"""

# Load config
def load_config():
    with open(CONFIG_PATH, 'r') as f:
        return yaml.safe_load(f)

def start_emulator(avd_name, headless=True):
    # Check if emulator is already running
    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
    if 'emulator-' in result.stdout:
        print("Emulator already running.")
        return
    headless_flag = ['-no-window'] if headless else []
    subprocess.Popen(['emulator', '-avd', avd_name] + headless_flag)
    print(f"Starting emulator {avd_name} (headless={headless})...")
    # Wait for device
    while True:
        result = subprocess.run(['adb', 'shell', 'getprop', 'sys.boot_completed'], capture_output=True, text=True)
        if '1' in result.stdout:
            print("Emulator booted.")
            break
        print("Waiting for emulator to boot...")
        time.sleep(5)

def get_apk_path(apk_folder):
    """Get APK path with enhanced debugging"""
    print(f"[DEBUG] Looking for APK in folder: {apk_folder}")

    if not os.path.exists(apk_folder):
        print(f"[ERROR] APK folder does not exist: {apk_folder}")
        raise FileNotFoundError(f"APK folder not found: {apk_folder}")

    apks = [f for f in os.listdir(apk_folder) if f.endswith('.apk')]
    print(f"[DEBUG] Found {len(apks)} APK files: {apks}")

    if not apks:
        print(f"[ERROR] No APK files found in: {apk_folder}")
        print(f"[DEBUG] Folder contents: {os.listdir(apk_folder)}")
        raise FileNotFoundError("No APK found in folder: " + apk_folder)

    apk_path = os.path.join(apk_folder, apks[0])
    print(f"[DEBUG] Using APK: {apk_path}")
    return apk_path

def get_package_name(apk_path, config):
    """Get package name from APK with enhanced debugging and fallback"""
    print(f"[DEBUG] Getting package name from APK: {apk_path}")

    # Check if APK file exists
    if not os.path.exists(apk_path):
        raise FileNotFoundError(f"APK file not found: {apk_path}")

    print(f"[DEBUG] APK file exists, size: {os.path.getsize(apk_path)} bytes")

    # Try multiple methods to get package name
    aapt_path = config.get('aapt_path', 'aapt')
    print(f"[DEBUG] Using aapt path: {aapt_path}")

    try:
        # Method 1: Try aapt with timeout
        print("[DEBUG] Attempting aapt dump badging...")
        result = subprocess.run(
            [aapt_path, 'dump', 'badging', apk_path],
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )

        print(f"[DEBUG] aapt return code: {result.returncode}")
        if result.stderr:
            print(f"[DEBUG] aapt stderr: {result.stderr}")

        if result.returncode == 0:
            for line in result.stdout.splitlines():
                if line.startswith('package:'):
                    for part in line.split():
                        if part.startswith('name='):
                            package_name = part.split('=')[1].strip("'\"")
                            print(f"[DEBUG] Found package name: {package_name}")
                            return package_name

    except subprocess.TimeoutExpired:
        print("[WARNING] aapt command timed out after 30 seconds")
    except FileNotFoundError:
        print(f"[WARNING] aapt tool not found at: {aapt_path}")
    except Exception as e:
        print(f"[WARNING] aapt command failed: {e}")

    # Method 2: Try alternative aapt paths
    alternative_paths = [
        '/usr/local/bin/aapt',
        '/opt/homebrew/bin/aapt',
        'aapt2',
        '/usr/local/bin/aapt2'
    ]

    for alt_path in alternative_paths:
        try:
            print(f"[DEBUG] Trying alternative aapt path: {alt_path}")
            result = subprocess.run(
                [alt_path, 'dump', 'badging', apk_path],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                for line in result.stdout.splitlines():
                    if line.startswith('package:'):
                        for part in line.split():
                            if part.startswith('name='):
                                package_name = part.split('=')[1].strip("'\"")
                                print(f"[DEBUG] Found package name with {alt_path}: {package_name}")
                                return package_name
        except:
            continue

    # Method 3: Fallback to known package name
    print("[WARNING] Could not determine package name from APK, using fallback")
    fallback_package = "com.kemendikdasmen.rumahpendidikan"
    print(f"[FALLBACK] Using known package name: {fallback_package}")
    return fallback_package

def is_app_installed(package):
    """Check if app is installed with timeout to prevent hanging"""
    try:
        print(f"[DEBUG] Checking if {package} is installed...")
        result = subprocess.run(
            ['adb', 'shell', 'pm', 'list', 'packages'],
            capture_output=True,
            text=True,
            timeout=15  # 15 second timeout
        )

        if result.returncode == 0:
            is_installed = package in result.stdout
            print(f"[DEBUG] App installed: {is_installed}")
            return is_installed
        else:
            print(f"[WARNING] adb pm list packages failed with return code: {result.returncode}")
            return False

    except subprocess.TimeoutExpired:
        print("[WARNING] adb pm list packages timed out, assuming app not installed")
        return False
    except Exception as e:
        print(f"[WARNING] Failed to check app installation: {e}")
        return False

def uninstall_app(package):
    """Uninstall app with timeout"""
    try:
        print(f"[DEBUG] Uninstalling {package}...")
        result = subprocess.run(['adb', 'uninstall', package], timeout=30)
        print(f"Uninstalled {package}")
    except subprocess.TimeoutExpired:
        print(f"[WARNING] Uninstall of {package} timed out")
    except Exception as e:
        print(f"[WARNING] Failed to uninstall {package}: {e}")

def install_app(apk_path):
    """Install app with timeout"""
    try:
        print(f"[DEBUG] Installing {apk_path}...")
        result = subprocess.run(['adb', 'install', '-r', apk_path], timeout=60)
        if result.returncode == 0:
            print(f"✅ Successfully installed {apk_path}")
        else:
            print(f"❌ Installation failed with return code: {result.returncode}")
    except subprocess.TimeoutExpired:
        print(f"[WARNING] Install of {apk_path} timed out")
    except Exception as e:
        print(f"[WARNING] Failed to install {apk_path}: {e}")

def launch_app(package):
    """Launch app on emulator with verification"""
    try:
        print(f"[DEBUG] Launching app: {package}")

        # Method 1: Try to launch using monkey command (more reliable)
        result = subprocess.run(
            ['adb', 'shell', 'monkey', '-p', package, '-c', 'android.intent.category.LAUNCHER', '1'],
            capture_output=True,
            text=True,
            timeout=15
        )

        if result.returncode == 0:
            print(f"✅ Monkey launch successful for {package}")
            # Verify app is running
            if verify_app_running(package):
                return True
        else:
            print(f"[WARNING] Monkey launch failed, trying alternative method...")

        # Method 2: Try to launch using am start command
        result = subprocess.run(
            ['adb', 'shell', 'am', 'start', '-n', f"{package}/.MainActivity"],
            capture_output=True,
            text=True,
            timeout=15
        )

        if result.returncode == 0:
            print(f"✅ am start successful for {package}")
            # Verify app is running
            if verify_app_running(package):
                return True
        else:
            print(f"[WARNING] am start also failed")

        # Method 3: Try intent-based launch
        result = subprocess.run(
            ['adb', 'shell', 'am', 'start', '-a', 'android.intent.action.MAIN', '-c', 'android.intent.category.LAUNCHER', package],
            capture_output=True,
            text=True,
            timeout=15
        )

        if result.returncode == 0:
            print(f"✅ Intent launch successful for {package}")
            # Verify app is running
            if verify_app_running(package):
                return True

        print(f"❌ All launch methods failed for {package}")
        return False

    except subprocess.TimeoutExpired:
        print(f"[WARNING] App launch timed out for {package}")
        return False
    except Exception as e:
        print(f"[WARNING] Failed to launch {package}: {e}")
        return False

def verify_app_running(package, timeout=10):
    """Verify that the app is actually running"""
    try:
        print(f"[VERIFY] Checking if {package} is running...")

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # Check if app is in foreground
                result = subprocess.run(
                    ['adb', 'shell', 'dumpsys', 'activity', 'activities'],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if result.returncode == 0 and package in result.stdout:
                    # Look for "mResumedActivity" which indicates foreground app
                    lines = result.stdout.splitlines()
                    for line in lines:
                        if "mResumedActivity" in line and package in line:
                            print(f"[VERIFY] ✅ {package} is running in foreground")
                            return True

                time.sleep(1)

            except subprocess.TimeoutExpired:
                print(f"[VERIFY] dumpsys timed out, trying alternative check...")
                break
            except Exception as e:
                print(f"[VERIFY] Error checking app status: {e}")
                break

        # Fallback: Just check if process exists
        try:
            result = subprocess.run(
                ['adb', 'shell', 'ps', '|', 'grep', package],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0 and package in result.stdout:
                print(f"[VERIFY] ✅ {package} process found")
                return True

        except:
            pass

        print(f"[VERIFY] ❌ {package} does not appear to be running")
        return False

    except Exception as e:
        print(f"[VERIFY] Error verifying app: {e}")
        return False

def is_appium_running():
    """Check if Appium server is running"""
    try:
        import requests
        response = requests.get('http://localhost:4723/status', timeout=5)
        if response.status_code == 200:
            print("✅ Appium server is running")
            return True
        else:
            print(f"❌ Appium server responded with status: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("❌ Appium server is not running")
        return False
    except Exception as e:
        print(f"❌ Error checking Appium server: {e}")
        return False

def start_appium_server():
    """Start Appium server"""
    try:
        print("[DEBUG] Starting Appium server...")

        # Try to start Appium server in background
        import subprocess
        import time

        # Start Appium server
        appium_process = subprocess.Popen(
            ['appium', '--port', '4723'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        print("[DEBUG] Waiting for Appium server to start...")

        # Wait up to 30 seconds for server to start
        for i in range(30):
            time.sleep(1)
            if is_appium_running():
                print("✅ Appium server started successfully!")
                return True
            print(f"[DEBUG] Waiting for Appium server... ({i+1}/30)")

        print("❌ Appium server failed to start within 30 seconds")
        return False

    except FileNotFoundError:
        print("❌ Appium command not found. Please install Appium:")
        print("   npm install -g appium")
        print("   appium driver install uiautomator2")
        return False
    except Exception as e:
        print(f"❌ Failed to start Appium server: {e}")
        return False

def prompt_user(msg):
    ans = input(msg + ' [y/n]: ').strip().lower()
    return ans == 'y'

def attempt_app_recovery(driver):
    """Attempt to recover from app crash or UiAutomator2 crash"""
    try:
        print("[RECOVERY] Starting app crash recovery...")

        # Step 1: Try to get current activity to see if session is alive
        try:
            current_activity = driver.current_activity
            print(f"[RECOVERY] Current activity: {current_activity}")

            # If we can get activity, session might be OK, just app crashed
            if current_activity:
                print("[RECOVERY] Session alive, attempting to restart app...")

                # Try to restart the app
                driver.activate_app("com.kemendikdasmen.rumahpendidikan")
                time.sleep(5)

                # Verify app is running
                new_activity = driver.current_activity
                if new_activity and "kemendikdasmen" in new_activity:
                    print(f"[RECOVERY] ✅ App restarted successfully: {new_activity}")
                    return True

        except Exception as session_error:
            print(f"[RECOVERY] Session appears dead: {session_error}")

        # Step 2: Try to launch app using ADB (fallback)
        print("[RECOVERY] Trying ADB app launch...")
        try:
            import subprocess
            result = subprocess.run(
                ['adb', 'shell', 'monkey', '-p', 'com.kemendikdasmen.rumahpendidikan', '-c', 'android.intent.category.LAUNCHER', '1'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                print("[RECOVERY] ADB launch successful")
                time.sleep(5)

                # Try to verify session is working
                try:
                    elements = driver.find_elements("xpath", "//*")
                    if len(elements) > 0:
                        print(f"[RECOVERY] ✅ Session recovered, found {len(elements)} elements")
                        return True
                except:
                    pass

        except Exception as adb_error:
            print(f"[RECOVERY] ADB launch failed: {adb_error}")

        # Step 3: Try to reset UiAutomator2 server
        print("[RECOVERY] Attempting UiAutomator2 server reset...")
        try:
            # Try to reset the session
            driver.reset()
            time.sleep(5)

            # Verify reset worked
            elements = driver.find_elements("xpath", "//*")
            if len(elements) > 0:
                print(f"[RECOVERY] ✅ UiAutomator2 reset successful, found {len(elements)} elements")
                return True

        except Exception as reset_error:
            print(f"[RECOVERY] UiAutomator2 reset failed: {reset_error}")

        print("[RECOVERY] ❌ All recovery attempts failed")
        return False

    except Exception as e:
        print(f"[RECOVERY] Recovery process failed: {e}")
        return False

def detect_app_crash(driver):
    """Detect if the app has crashed or closed"""
    try:
        # Method 1: Check current activity
        current_activity = driver.current_activity
        if not current_activity or "kemendikdasmen" not in current_activity:
            return True

        # Method 2: Try to find any elements
        elements = driver.find_elements("xpath", "//*")
        if len(elements) == 0:
            return True

        # Method 3: Check if we can get page source
        page_source = driver.page_source
        if not page_source or len(page_source) < 100:
            return True

        return False

    except Exception:
        # If any of these fail, assume app crashed
        return True

def smart_wait_for_app_ready(driver, timeout=20):
    """Smart wait system that waits until all elements are fully loaded"""
    print(f"[SMART_WAIT] Waiting for application to fully load (max {timeout}s)...")

    start_time = time.time()
    stable_count = 0
    required_stable_checks = 2  # Reduced from 3 to 2
    check_interval = 1  # Reduced from 2 to 1 second

    previous_element_count = 0
    previous_page_source_length = 0

    while time.time() - start_time < timeout:
        try:
            current_time = time.time() - start_time
            print(f"[SMART_WAIT] Check {int(current_time/check_interval)+1} - Analyzing page stability...")

            # Get current page state
            page_source = driver.page_source
            current_page_source_length = len(page_source)

            # Count meaningful elements
            meaningful_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='' or @clickable='true']")
            current_element_count = len(meaningful_elements)

            # Check for loading indicators
            loading_indicators = driver.find_elements("xpath", "//*[contains(@text, 'Loading') or contains(@text, 'loading') or contains(@content-desc, 'Loading') or contains(@content-desc, 'loading')]")
            progress_bars = driver.find_elements("xpath", "//android.widget.ProgressBar")
            spinners = driver.find_elements("xpath", "//*[contains(@class, 'ProgressBar') or contains(@class, 'Spinner')]")

            has_loading_indicators = len(loading_indicators) > 0 or len(progress_bars) > 0 or len(spinners) > 0

            print(f"[SMART_WAIT]   Elements: {current_element_count}, Page size: {current_page_source_length}, Loading indicators: {len(loading_indicators + progress_bars + spinners)}")

            # Check if page is stable (no loading indicators and element count is stable)
            if not has_loading_indicators:
                element_count_stable = abs(current_element_count - previous_element_count) <= 2  # Allow small variations
                page_size_stable = abs(current_page_source_length - previous_page_source_length) <= 100  # Allow small variations

                if element_count_stable and page_size_stable and current_element_count > 5:  # Must have meaningful content
                    stable_count += 1
                    print(f"[SMART_WAIT]   ✅ Page appears stable ({stable_count}/{required_stable_checks})")

                    if stable_count >= required_stable_checks:
                        total_time = time.time() - start_time
                        print(f"[SMART_WAIT] ✅ Application fully loaded! ({total_time:.1f}s)")
                        print(f"[SMART_WAIT]   Final state: {current_element_count} elements, {current_page_source_length} chars")
                        return True
                else:
                    stable_count = 0
                    print(f"[SMART_WAIT]   ⏳ Page still changing (elements: {previous_element_count}→{current_element_count}, size: {previous_page_source_length}→{current_page_source_length})")
            else:
                stable_count = 0
                print(f"[SMART_WAIT]   ⏳ Loading indicators detected, waiting...")

            # Update previous values
            previous_element_count = current_element_count
            previous_page_source_length = current_page_source_length

            # Wait before next check
            time.sleep(check_interval)

        except Exception as e:
            error_msg = str(e)
            print(f"[SMART_WAIT] Error during stability check: {e}")

            # Check if this is an app crash or UiAutomator2 crash
            if "instrumentation process is not running" in error_msg or "crashed" in error_msg.lower():
                print("[SMART_WAIT] 🚨 App or UiAutomator2 crashed detected!")
                print("[SMART_WAIT] Attempting to recover...")

                # Try to recover the session
                if attempt_app_recovery(driver):
                    print("[SMART_WAIT] ✅ Recovery successful, continuing...")
                    # Reset counters after recovery
                    stable_count = 0
                    previous_element_count = 0
                    previous_page_source_length = 0
                else:
                    print("[SMART_WAIT] ❌ Recovery failed, aborting smart wait")
                    return False

            time.sleep(check_interval)
            continue

    # Timeout reached
    total_time = time.time() - start_time
    print(f"[SMART_WAIT] ⚠️ Timeout reached ({total_time:.1f}s), proceeding anyway...")
    print(f"[SMART_WAIT]   Final state: {current_element_count} elements detected")
    return False

def wait_for_specific_elements(driver, timeout=30):
    """Wait for specific key elements that indicate the app is ready"""
    print(f"[ELEMENT_WAIT] Waiting for key elements to appear...")

    start_time = time.time()

    # Key elements that indicate the app is ready
    key_element_patterns = [
        "//*[@text='Ruang GTK' or @content-desc='Ruang GTK']",
        "//*[@text='Ruang Murid' or @content-desc='Ruang Murid']",
        "//*[@text='Ruang Sekolah' or @content-desc='Ruang Sekolah']",
        "//*[contains(@text, 'Ruang') or contains(@content-desc, 'Ruang')]",  # Any "Ruang" element
        "//android.widget.TextView[@text!='' and string-length(@text)>3]",  # Any meaningful text
        "//*[@clickable='true' and (@text!='' or @content-desc!='')]"  # Any clickable element with text
    ]

    while time.time() - start_time < timeout:
        try:
            current_time = time.time() - start_time
            print(f"[ELEMENT_WAIT] Check {int(current_time/2)+1} - Looking for key elements...")

            found_elements = []
            for pattern in key_element_patterns:
                try:
                    elements = driver.find_elements("xpath", pattern)
                    if elements:
                        found_elements.append(f"{len(elements)} elements matching '{pattern[:50]}...'")
                except:
                    continue

            if found_elements:
                print(f"[ELEMENT_WAIT] ✅ Key elements found:")
                for element_info in found_elements[:3]:  # Show first 3
                    print(f"[ELEMENT_WAIT]   - {element_info}")

                total_time = time.time() - start_time
                print(f"[ELEMENT_WAIT] ✅ App appears ready! ({total_time:.1f}s)")
                return True
            else:
                print(f"[ELEMENT_WAIT]   ⏳ Key elements not yet visible...")

            time.sleep(2)

        except Exception as e:
            error_msg = str(e)
            print(f"[ELEMENT_WAIT] Error checking elements: {e}")

            # Check if this is an app crash
            if "instrumentation process is not running" in error_msg or "crashed" in error_msg.lower():
                print("[ELEMENT_WAIT] 🚨 App crash detected during element wait!")

                # Try to recover
                if attempt_app_recovery(driver):
                    print("[ELEMENT_WAIT] ✅ Recovery successful, continuing element wait...")
                    continue
                else:
                    print("[ELEMENT_WAIT] ❌ Recovery failed, aborting element wait")
                    return False

            time.sleep(2)
            continue

    # Timeout reached
    total_time = time.time() - start_time
    print(f"[ELEMENT_WAIT] ⚠️ Timeout reached ({total_time:.1f}s), proceeding anyway...")
    return False

def start_appium_session(package, wait_timeout):
    """Start Appium session with enhanced error handling and known activity fallback"""
    print(f"[DEBUG] Starting Appium session for package: {package}")

    # Try to get main activity with timeout
    main_activity = None
    try:
        print("[DEBUG] Attempting to resolve main activity...")
        result = subprocess.run(
            ['adb', 'shell', 'cmd', 'package', 'resolve-activity', '--brief', package],
            capture_output=True,
            text=True,
            timeout=15  # 15 second timeout
        )

        if result.returncode == 0:
            lines = result.stdout.splitlines()
            if len(lines) > 1:
                main_activity = lines[1].strip()
                print(f"[DEBUG] Found main activity: {main_activity}")
            else:
                print("[WARNING] Could not parse main activity from output")
        else:
            print(f"[WARNING] resolve-activity failed with return code: {result.returncode}")

    except subprocess.TimeoutExpired:
        print("[WARNING] resolve-activity command timed out")
    except Exception as e:
        print(f"[WARNING] Error resolving main activity: {e}")

    # Fallback to known main activity for the target package
    if not main_activity:
        if package == "com.kemendikdasmen.rumahpendidikan":
            main_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
            print(f"[FALLBACK] Using known main activity: {main_activity}")
        else:
            # Generic fallback
            main_activity = f"{package}.MainActivity"
            print(f"[FALLBACK] Using generic main activity: {main_activity}")

    print(f"[DEBUG] Creating Appium session with activity: {main_activity}")

    try:
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator-5554"
        options.app_package = package
        options.app_activity = main_activity
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        options.set_capability('adbExecTimeout', 60000)
        options.set_capability('uiautomator2ServerLaunchTimeout', 60000)

        # Prevent app restart - connect to existing app instance
        options.set_capability('noReset', True)  # Don't reset app state
        options.set_capability('fullReset', False)  # Don't reinstall app
        options.set_capability('autoLaunch', False)  # Don't auto-launch app
        options.set_capability('dontStopAppOnReset', True)  # Don't stop app when resetting
        options.set_capability('skipDeviceInitialization', True)  # Skip device setup
        options.set_capability('skipServerInstallation', True)  # Skip server reinstall

        print("[DEBUG] Connecting to Appium server at http://localhost:4723...")
        driver = webdriver.Remote('http://localhost:4723', options=options)
        driver.implicitly_wait(wait_timeout)

        print("✅ Appium session started successfully!")

        # Smart wait for app to be fully loaded
        print("\n" + "="*60)
        print("SMART WAIT SYSTEM - ENSURING APP IS FULLY LOADED")
        print("="*60)

        # Method 1: Wait for page stability
        stability_result = smart_wait_for_app_ready(driver, timeout=20)

        # Method 2: Wait for specific key elements
        elements_result = wait_for_specific_elements(driver, timeout=15)

        if stability_result or elements_result:
            print("\n✅ SMART WAIT COMPLETE - Application is ready for automation!")
        else:
            print("\n⚠️ SMART WAIT TIMEOUT - Proceeding with caution...")

        print("="*60 + "\n")

        return driver

    except Exception as e:
        print(f"❌ Failed to start Appium session: {e}")
        raise

def extract_ui_hierarchy(driver):
    # Dump UI hierarchy as XML
    xml = driver.page_source
    return xml

def query_ollama(prompt, model):
    response = requests.post(
        "http://localhost:11434/api/generate",
        json={"model": model, "prompt": prompt},
        stream=True
    )
    result = ""
    for line in response.iter_lines():
        if line:
            data = json.loads(line)
            if "response" in data:
                result += data["response"]
    return result

def save_json(data, path):
    with open(path, 'w') as f:
        json.dump(data, f, indent=2)

def save_feature(content, path):
    with open(path, 'w') as f:
        f.write(content)

def remove_bounds(obj):
    if isinstance(obj, dict):
        return {k: remove_bounds(v) for k, v in obj.items() if k.lower() not in ["bounds", "bound", "coordinate", "coordinates", "position"]}
    elif isinstance(obj, list):
        return [remove_bounds(i) for i in obj]
    else:
        return obj

def remove_bounds_from_string(json_str):
    # Remove all "bounds": ... (including arrays or objects) from the string
    return re.sub(r'"bounds"\s*:\s*(\[[^\]]*\]|\{[^}]*\}),?', '', json_str)

def flatten_tree_to_locators(data):
    # Recursively flatten a tree of nodes/attributes to a flat mapping of locator strategies per element
    locators = {}
    def recurse(node, parent_index=[0]):
        if isinstance(node, dict):
            # If this node has locator strategies, extract them
            if 'attributes' in node:
                attrs = node['attributes']
                # Compose a unique key for the element
                key = attrs.get('resource-id') or attrs.get('content-desc') or attrs.get('text') or f"element_{parent_index[0]}"
                # Only keep relevant locator strategies
                locator = {}
                for k in ['resource-id', 'accessibility-id', 'class', 'text', 'content-desc', 'xpath', 'index']:
                    if k in attrs and attrs[k] is not None:
                        locator[k] = attrs[k]
                # Add class if present at the node level
                if 'class' in node and 'class' not in locator:
                    locator['class'] = node['class']
                locators[key] = locator
                parent_index[0] += 1
                # Recurse into children
                if 'children' in attrs:
                    for child in attrs['children']:
                        recurse(child, parent_index)
            # Some trees may have 'children' at the node level
            if 'children' in node:
                for child in node['children']:
                    recurse(child, parent_index)
        elif isinstance(node, list):
            for child in node:
                recurse(child, parent_index)
    # Start recursion
    if 'nodes' in data:
        recurse(data['nodes'])
    else:
        recurse(data)
    return locators

def list_to_locator_dict(locator_list):
    locators = {}
    for i, item in enumerate(locator_list):
        # Use resource-id, content-desc, text, or fallback to class+index
        key = (
            item.get("resource-id")
            or item.get("content-desc")
            or item.get("text")
            or f"{item.get('class', 'element')}_{item.get('index', i)}"
        )
        # Only keep relevant locator strategies
        locator = {}
        for k in ['resource-id', 'accessibility-id', 'class', 'text', 'content-desc', 'xpath', 'index']:
            if k in item and item[k] is not None:
                locator[k] = item[k]
        locators[key] = locator
    return locators

def extract_locators_from_xml(xml, screen_name, package):
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    locators = []
    def recurse(elem, path):
        # Count siblings of the same tag to get the index
        parent = elem.getparent() if hasattr(elem, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == elem.tag]
            idx = same_tag_siblings.index(elem) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{elem.tag}[{idx}]"
        attrs = elem.attrib.copy()
        class_name = attrs.get('class', elem.tag)
        text = attrs.get('text', elem.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        # Only collect if text or content-desc is non-empty
        if (
            (text and text.strip()) or
            (content_desc and content_desc.strip()) or
            (resource_id and resource_id.strip()) or
            (attrs.get('clickable', '') == 'true') or
            (attrs.get('focusable', '') == 'true')
        ):
            locator = {
                "screen_name": screen_name,
                "package": package,
                "resource_id": resource_id,
                "class_name": class_name,
                "text": text,
                "content_desc": content_desc,
                "xpath": this_xpath,
                "element_type": class_name,
            }
            # Add other attributes if present
            for k in ["clickable", "focusable", "enabled", "selected", "checkable", "checked", "scrollable", "index"]:
                if k in attrs:
                    locator[k] = attrs[k]
            locators.append(locator)
        for child in list(elem):
            recurse(child, this_xpath)
    recurse(root, '')
    return locators

def get_screen_name(driver):
    # Try to get a unique screen name using activity and package
    try:
        activity = driver.current_activity
    except Exception:
        activity = "unknown_activity"
    try:
        package = driver.current_package
    except Exception:
        package = "unknown_package"
    return f"{package}:{activity}"

def hash_xml(xml):
    return hashlib.md5(xml.encode('utf-8')).hexdigest()

def get_full_xpath_map(xml):
    """
    Build a mapping from element attributes (resource-id, class, text, content-desc, bounds) to their full XPath in the XML tree.
    Returns: {xpath: {attr_dict}}
    """
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    xpath_map = {}

    def recurse(node, path):
        # Count siblings of the same tag to get the index
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        # Build this node's XPath
        this_xpath = f"{path}/{node.tag}[{idx}]"
        # Save attributes for this node
        attrs = node.attrib.copy()
        attrs['tag'] = node.tag
        xpath_map[this_xpath] = attrs
        # Recurse into children
        for child in list(node):
            recurse(child, this_xpath)
    # Use a stack to avoid recursion limit
    def recurse_stack(node, path):
        stack = [(node, path)]
        while stack:
            node, path = stack.pop()
            parent = node.getparent() if hasattr(node, 'getparent') else None
            if parent is not None:
                same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                idx = same_tag_siblings.index(node) + 1
            else:
                idx = 1
            this_xpath = f"{path}/{node.tag}[{idx}]"
            attrs = node.attrib.copy()
            attrs['tag'] = node.tag
            xpath_map[this_xpath] = attrs
            for child in reversed(list(node)):
                stack.append((child, this_xpath))
    # Use stack version for safety
    recurse_stack(root, '')
    return xpath_map

# Replace get_xpath_for_element to use full-path XPath from XML

def get_xpath_for_element_by_attrs(el, xml):
    # Get element's attributes
    rid = el.get_attribute('resource-id')
    text = el.get_attribute('text')
    cdesc = el.get_attribute('content-desc')
    cls = el.get_attribute('class')
    bounds = el.get_attribute('bounds')
    attrs = {'resource-id': rid, 'text': text, 'content-desc': cdesc, 'class': cls, 'bounds': bounds}
    xpath_map = get_full_xpath_map(xml)
    # Try to find the matching node in the map (strict)
    for xpath, node_attrs in xpath_map.items():
        match = True
        for k in ['resource-id', 'text', 'content-desc', 'class', 'bounds']:
            if attrs[k] and node_attrs.get(k, None) != attrs[k]:
                match = False
                break
        if match:
            return xpath
    # Fallback: try to match by resource-id
    if rid:
        for xpath, node_attrs in xpath_map.items():
            if node_attrs.get('resource-id', None) == rid:
                return xpath
    # Fallback: try to match by content-desc
    if cdesc:
        for xpath, node_attrs in xpath_map.items():
            if node_attrs.get('content-desc', None) == cdesc:
                return xpath
    # Fallback: try to match by class and text
    if cls and text:
        for xpath, node_attrs in xpath_map.items():
            if node_attrs.get('class', None) == cls and node_attrs.get('text', None) == text:
                return xpath
    return None

def save_screen_xml(screen_name, xml, folder="screen_xmls"):
    os.makedirs(folder, exist_ok=True)
    fname = os.path.join(folder, f"{screen_name}.xml")
    with open(fname, 'w') as f:
        f.write(xml)
    print(f"[XML-DUMP] Saved UI XML to {fname}")

# Track all visited screens and their element counts, per package
visited_screens_summary = {}
visited_per_package = {}

def robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=20):
    import time
    start = time.time()
    while time.time() - start < timeout:
        curr_activity = driver.current_activity
        curr_xml = driver.page_source
        if curr_activity != prev_activity or curr_xml != prev_xml:
            # Also wait for new clickable elements to appear
            try:
                new_clickables = driver.find_elements("xpath", '//*[( @clickable="true" or @focusable="true" or @visible="true") and not(@password="true")]')
                if len(new_clickables) > 0:
                    return True
            except Exception:
                pass
        time.sleep(1)
    return False

def wait_for_native_page_load(driver, timeout=15):
    import time
    start = time.time()
    prev_xml = driver.page_source
    while time.time() - start < timeout:
        time.sleep(1)
        curr_xml = driver.page_source
        if curr_xml != prev_xml:
            return True
        prev_xml = curr_xml
    return False

def wait_for_webview_page_load(driver, timeout=15):
    import time
    from selenium.common.exceptions import WebDriverException
    start = time.time()
    while time.time() - start < timeout:
        try:
            ready = driver.execute_script('return document.readyState')
            if ready == 'complete':
                return True
        except WebDriverException:
            pass
        time.sleep(1)
    return False

def detect_context(driver):
    try:
        package = driver.current_package
        contexts = driver.contexts
        curr_context = driver.current_context if hasattr(driver, 'current_context') else None
        if package == 'com.android.chrome':
            for ctx in contexts:
                if 'WEBVIEW' in ctx or 'CHROMIUM' in ctx:
                    return 'webview'
            return 'chrome_native'
        elif curr_context and (curr_context.startswith('WEBVIEW') or curr_context.startswith('CHROMIUM')):
            return 'webview'
        else:
            return 'native'
    except Exception as e:
        print(f"[CONTEXT DETECT] Error: {e}")
        return 'unknown'

def smart_go_back(driver, main_package, chrome_opened_from_app=False):
    try:
        contexts = driver.contexts
        current_package = driver.current_package
        if current_package == main_package:
            print("[SMART-BACK] In native app, using driver.back()")
            driver.back()
        elif 'WEBVIEW' in ''.join(contexts) or 'CHROMIUM' in ''.join(contexts) or 'chrome' in current_package:
            print("[SMART-BACK] In webview/chrome, switching to main app")
            driver.activate_app(main_package)
        else:
            print("[SMART-BACK] Unknown context, defaulting to driver.back()")
            driver.back()
        time.sleep(0.7)
        # After back, if context is chrome_native and not opened from app, switch back to app
        context = detect_context(driver)
        if context == 'chrome_native' and not chrome_opened_from_app:
            print("[SMART-BACK] Chrome context detected after back, but NOT opened from app. Switching back to app.")
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"[SMART-BACK] Could not return to main app: {e}")
    except Exception as e:
        print(f"[SMART-BACK] Error during smart go back: {e}")

# --- WebView crawling and improved smart crawl (move to top-level) ---
def crawl_webview_elements(driver, all_locators, webview_name=None):
    print(f"[WEBVIEW] Crawling webview: {webview_name if webview_name else ''}")
    try:
        # Switch to webview context
        webview_contexts = [c for c in driver.contexts if 'WEBVIEW' in c]
        if not webview_contexts:
            print("[WEBVIEW] No webview context found.")
            return
        driver.switch_to.context(webview_contexts[0])
        print(f"[WEBVIEW] Switched to context: {webview_contexts[0]}")
        # Collect actionable elements
        elements = driver.find_elements(By.XPATH, "//a | //button | //*[@role='button']")
        for el in elements:
            try:
                tag = el.tag_name
                text = el.text
                el_id = el.get_attribute('id')
                el_class = el.get_attribute('class')
                href = el.get_attribute('href')
                locator = {
                    "webview": True,
                    "tag": tag,
                    "text": text,
                    "id": el_id,
                    "class": el_class,
                    "href": href
                }
                print(f"[WEBVIEW-ELEMENT] {locator}")
                all_locators.append(locator)
            except Exception as e:
                print(f"[WEBVIEW] Error extracting element: {e}")
        # Switch back to native context
        driver.switch_to.context(driver.contexts[0])
        print(f"[WEBVIEW] Switched back to native context: {driver.contexts[0]}")
    except Exception as e:
        print(f"[WEBVIEW] Error during webview crawling: {e}")

def improved_smart_crawl(driver, package, main_package, all_locators):
    max_retries = 3
    retries = 0
    while True:
        context = detect_context(driver)
        print(f"[SMART-CRAWL] Detected context: {context}")
        if context == 'native':
            print("[SMART-CRAWL] Waiting for native page to load...")
            wait_for_native_page_load(driver)
            print("[SMART-CRAWL] Collecting ALL native elements...")
            screen_name = get_screen_name(driver)
            native_elements = collect_all_native_elements(driver, screen_name, driver.current_package)
            all_locators.extend(native_elements)
            break
        elif context == 'webview':
            print("[SMART-CRAWL] Waiting for webview page to load...")
            wait_for_webview_page_load(driver)
            print("[SMART-CRAWL] Collecting ALL webview elements...")
            webview_contexts = [c for c in driver.contexts if 'WEBVIEW' in c]
            if webview_contexts:
                driver.switch_to.context(webview_contexts[0])
                webview_elements = collect_all_webview_elements(driver)
                all_locators.extend(webview_elements)
                driver.switch_to.context(driver.contexts[0])
                print(f"[SMART-CRAWL] Switched back to native context: {driver.contexts[0]}")
            else:
                print("[SMART-CRAWL] No webview context found.")
            break
        elif context == 'chrome_native':
            print("[SMART-CRAWL] Chrome native context detected. Attempting CDP collection...")
            collect_chrome_cdp_locators()
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"[SMART-CRAWL] Could not return to main app: {e}")
            continue
        else:
            print("[SMART-CRAWL] Unknown context. Stopping crawl.")
            break
        retries += 1
        if retries >= max_retries:
            print("[SMART-CRAWL] Max retries reached. Stopping crawl.")
            break

def crawl_menus_on_page(driver, main_package, all_locators, menu_descs, depth=0, is_main_page=True, visited_texts=None, config=None, package=None):
    """
    Crawl all main menu icons and any clickable element with actionable label in itself or its descendants, with scrolling and substring matching.
    """
    import time
    from selenium.common.exceptions import WebDriverException
    if visited_texts is None:
        visited_texts = set()
    # Remove hardcoded ACTIONABLE_LABELS - collect ALL meaningful elements dynamically
    def is_meaningful_element_legacy(element):
        """Check if element is meaningful without hardcoded labels"""
        try:
            text = element.get_attribute('text') or ''
            desc = element.get_attribute('content-desc') or ''
            resource_id = element.get_attribute('resource-id') or ''
            clickable = element.get_attribute('clickable') == 'true'

            # Include if has meaningful text (length > 2)
            if text.strip() and len(text.strip()) > 2:
                return True
            # Include if has content description
            if desc.strip() and len(desc.strip()) > 2:
                return True
            # Include if clickable
            if clickable:
                return True
            # Include if has resource ID
            if resource_id.strip():
                return True
            return False
        except:
            return False
    if is_main_page:
        # Scroll down until 'Layanan Paling Banyak Diakses' is found
        print("[SCROLL-SEARCH] Scrolling main page to find 'Layanan Paling Banyak Diakses'...")
        found_section = False
        max_scrolls = 8
        scroll_count = 0
        while not found_section and scroll_count < max_scrolls:
            elements = driver.find_elements("xpath", "//*[contains(@content-desc, 'Layanan Paling Banyak Diakses') or contains(@text, 'Layanan Paling Banyak Diakses')]")
            if elements:
                found_section = True
                print(f"[SCROLL-SEARCH] Found section 'Layanan Paling Banyak Diakses' after {scroll_count} scrolls.")
                break
            else:
                try:
                    driver.swipe(300, 1000, 300, 300, 800)
                    time.sleep(1.2)
                except Exception as e:
                    print(f"[SCROLL-SEARCH] Scroll failed: {e}")
                    break
                scroll_count += 1
        if not found_section:
            print("[SCROLL-SEARCH] Could not find 'Layanan Paling Banyak Diakses' after scrolling.")
        # After finding the section, look for 'Lihat Semua'
        found_lihat_semua = False
        if found_section:
            print("[SCROLL-SEARCH] Searching for 'Lihat Semua' after finding the section...")
            # Try up to 5 more scrolls in case it's just below
            for i in range(5):
                lihat_semua_els = driver.find_elements("xpath", "//*[@content-desc='Lihat Semua' or @text='Lihat Semua']")
                if lihat_semua_els:
                    el = lihat_semua_els[0]
                    locator = {
                        "screen_name": get_screen_name(driver),
                        "package": driver.current_package,
                        "resource_id": el.get_attribute("resource-id"),
                        "class_name": el.get_attribute("class"),
                        "text": el.get_attribute("text"),
                        "content_desc": el.get_attribute("content-desc"),
                        "xpath": get_xpath_for_element_by_attrs(el, driver.page_source),
                        "element_type": el.get_attribute("class"),
                    }
                    all_locators.append(locator)
                    print(f"[SCROLL-SEARCH] Found and collected locator for 'Lihat Semua': {locator}")
                    found_lihat_semua = True
                    break
                else:
                    try:
                        driver.swipe(300, 1000, 300, 300, 800)
                        time.sleep(1.2)
                    except Exception as e:
                        print(f"[SCROLL-SEARCH] Scroll for 'Lihat Semua' failed: {e}")
                        break
            if not found_lihat_semua:
                print("[SCROLL-SEARCH] Could not find 'Lihat Semua' after finding the section.")
        # 1. Main menu icons (by content-desc)
        for menu_desc in menu_descs:
            try:
                xpath = f"//*[@content-desc='{menu_desc}']"
                menu_elements = driver.find_elements("xpath", xpath)
                if not menu_elements:
                    print(f"  [MENU] No element found for menu '{menu_desc}' on this page.")
                    continue
                menu_el = menu_elements[0]
                el_class = menu_el.get_attribute('class')
                print(f"[MENU] Clicking menu: content_desc='{menu_desc}', class='{el_class}', xpath='{xpath}' at depth {depth}")
                try:
                    step_start = time.time()
                    # Save locator for the actionable menu element before clicking
                    menu_locator = {
                        "screen_name": get_screen_name(driver),
                        "package": driver.current_package,
                        "resource_id": menu_el.get_attribute("resource-id"),
                        "class_name": menu_el.get_attribute("class"),
                        "text": menu_el.get_attribute("text"),
                        "content_desc": menu_el.get_attribute("content-desc"),
                        "xpath": get_xpath_for_element_by_attrs(menu_el, driver.page_source),
                        "element_type": menu_el.get_attribute("class"),
                    }
                    all_locators.append(menu_locator)
                    prev_activity = driver.current_activity
                    prev_xml = driver.page_source
                    print(f"[DEBUG-TIME] Before menu_el.click() for '{menu_desc}' at {step_start:.2f}")
                    menu_el.click()
                    print(f"[DEBUG] After menu click: activity={driver.current_activity}, package={driver.current_package}")
                    robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                    print(f"[DEBUG-TIME] After click+wait for '{menu_desc}': took {time.time() - step_start:.2f} seconds")
                    context = detect_context(driver)
                    print(f"[MENU] Detected context after menu click: {context}")
                    new_xml = driver.page_source
                    new_screen_name = get_screen_name(driver)
                    new_locators = extract_locators_from_xml(new_xml, new_screen_name, driver.current_package)
                    print(f"[MENU] Collected {len(new_locators)} locators on navigated screen.")
                    all_locators.extend(new_locators)
                    save_screen_xml(f"{driver.current_package}__{new_screen_name}", new_xml)
                    # Log submenus present on this page (do not click recursively)
                    for sub_desc in menu_descs:
                        sub_xpath = f"//*[@content-desc='{sub_desc}']"
                        sub_elements = driver.find_elements("xpath", sub_xpath)
                        if sub_elements:
                            sub_class = sub_elements[0].get_attribute('class')
                            print(f"[SUBMENU] Present submenu: content_desc='{sub_desc}', class='{sub_class}', xpath='{sub_xpath}' at depth {depth+1}")
                    prev_activity = driver.current_activity
                    prev_xml = driver.page_source
                    back_start = time.time()
                    print(f"[DEBUG-TIME] Before smart_go_back for '{menu_desc}' at {back_start:.2f}")
                    smart_go_back(driver, main_package)
                    robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                    print(f"[DEBUG-TIME] After smart_go_back+wait for '{menu_desc}': took {time.time() - back_start:.2f} seconds")
                except WebDriverException as e:
                    print(f"[FATAL] Appium session lost or UiAutomator2 crashed: {e}")
                    if config and package:
                        print("[RECOVERY] Attempting to restart Appium session and continue crawling...")
                        driver.quit()
                        driver = start_appium_session(package, config['wait_timeout'])
                        print("[RECOVERY] Appium session restarted. Retrying current menu.")
                        return crawl_menus_on_page(driver, main_package, all_locators, menu_descs, depth, is_main_page, visited_texts, config, package)
                    else:
                        print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                        return
                    print("[FATAL] Exiting crawl. Please check Appium server and device logs.")
                    return
                except Exception as e:
                    print(f"[MENU] Error processing menu '{menu_desc}': {e}")
                    continue
            except Exception as e:
                print(f"[MENU] Error processing menu '{menu_desc}': {e}")
                continue
        # 2. Scroll and collect all clickable elements (deduplicate by location, size, class, content-desc, text)
        print("[PHASE] Finished all main menu clicks. Starting scroll-and-collect-clickables phase.")
        def scroll_and_collect_clickables():
            all_clickables = []
            unique_keys = set()
            last_page_source = None
            max_scrolls = 8  # Increased to ensure thorough scrolling
            scroll_count = 0
            while scroll_count < max_scrolls:
                print(f"[DEBUG] scroll-and-collect-clickables: scroll_count={scroll_count}")
                try:
                    clickables = driver.find_elements("xpath", "//*[@clickable='true']")
                except WebDriverException as e:
                    print(f"[FATAL] Appium session lost or UiAutomator2 crashed during scroll-and-collect: {e}")
                    if config and package:
                        print("[RECOVERY] Attempting to restart Appium session and continue crawling (scroll phase)...")
                        driver.quit()
                        print("[RECOVERY] Appium session restart required. Exiting scroll phase to allow outer recovery.")
                        return []
                    else:
                        print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                        return []
                for el in clickables:
                    try:
                        key = (
                            el.location['x'], el.location['y'],
                            el.size['width'], el.size['height'],
                            el.get_attribute('class'),
                            el.get_attribute('content-desc') or '',
                            el.get_attribute('text') or ''
                        )
                        if key not in unique_keys:
                            all_clickables.append(el)
                            unique_keys.add(key)
                    except Exception:
                        continue
                # Try to scroll
                try:
                    driver.swipe(300, 1000, 300, 300, 800)
                    time.sleep(1.5)
                except Exception:
                    break
                new_page_source = driver.page_source
                if new_page_source == last_page_source:
                    break
                last_page_source = new_page_source
                scroll_count += 1
            if scroll_count >= max_scrolls:
                print(f"[WARNING] Reached max_scrolls ({max_scrolls}) in scroll-and-collect-clickables. Breaking to prevent infinite loop.")
            return all_clickables

        # --- Debug logging: print all elements with text/content-desc/clickable/focusable ---
        def debug_log_all_elements():
            xml = driver.page_source
            import xml.etree.ElementTree as ET
            tree = ET.ElementTree(ET.fromstring(xml))
            root = tree.getroot()
            def recurse(node, path):
                parent = node.getparent() if hasattr(node, 'getparent') else None
                if parent is not None:
                    same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                    idx = same_tag_siblings.index(node) + 1
                else:
                    idx = 1
                this_xpath = f"{path}/{node.tag}[{idx}]"
                attrs = node.attrib.copy()
                class_name = attrs.get('class', node.tag)
                text = attrs.get('text', node.text or "")
                content_desc = attrs.get('content-desc', "")
                clickable = attrs.get('clickable', '')
                focusable = attrs.get('focusable', '')
                if (
                    (text and text.strip()) or
                    (content_desc and content_desc.strip()) or
                    (clickable == 'true') or
                    (focusable == 'true')
                ):
                    print(f"[ALL-ELEMENT] class='{class_name}', text='{text}', content_desc='{content_desc}', clickable='{clickable}', focusable='{focusable}', xpath='{this_xpath}'")
                for child in list(node):
                    recurse(child, this_xpath)
            recurse(root, '')

        print("[PHASE] Debug log all elements on this screen:")
        debug_log_all_elements()

        # --- Collect all labeled elements (not just clickable) and check visibility ---
        def collect_all_labeled_elements_and_visibility():
            xml = driver.page_source
            import xml.etree.ElementTree as ET
            tree = ET.ElementTree(ET.fromstring(xml))
            root = tree.getroot()
            all_labeled = []
            not_visible = []
            def recurse(node, path):
                parent = node.getparent() if hasattr(node, 'getparent') else None
                if parent is not None:
                    same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                    idx = same_tag_siblings.index(node) + 1
                else:
                    idx = 1
                this_xpath = f"{path}/{node.tag}[{idx}]"
                attrs = node.attrib.copy()
                class_name = attrs.get('class', node.tag)
                text = attrs.get('text', node.text or "")
                content_desc = attrs.get('content-desc', "")
                resource_id = attrs.get('resource-id', "")
                if (
                    (text and text.strip()) or
                    (content_desc and content_desc.strip())
                ):
                    el_info = {
                        'class': class_name,
                        'text': text,
                        'content_desc': content_desc,
                        'resource_id': resource_id,
                        'xpath': this_xpath
                    }
                    # Try to find the element in the UI using xpath
                    try:
                        el = driver.find_element('xpath', this_xpath)
                        is_visible = False
                        try:
                            is_visible = el.is_displayed()
                        except Exception:
                            # Some elements may not support is_displayed
                            is_visible = False
                        el_info['visible'] = is_visible
                        if not is_visible:
                            # Try to scroll to the element
                            try:
                                driver.execute_script('mobile: scroll', {'direction': 'down', 'element': el.id})
                                is_visible = el.is_displayed()
                                el_info['visible_after_scroll'] = is_visible
                            except Exception:
                                el_info['visible_after_scroll'] = False
                            if not el_info.get('visible_after_scroll', False):
                                not_visible.append(el_info)
                    except Exception:
                        el_info['visible'] = False
                        el_info['visible_after_scroll'] = False
                        not_visible.append(el_info)
                    all_labeled.append(el_info)
                for child in list(node):
                    recurse(child, this_xpath)
            recurse(root, '')
            print(f"[ALL-LABELED] Total labeled elements found: {len(all_labeled)}")
            for el in all_labeled:
                print(f"[ALL-LABELED] {el}")
            if not_visible:
                print(f"[NOT-VISIBLE] Elements not visible after scroll attempts: {len(not_visible)}")
                for el in not_visible:
                    print(f"[NOT-VISIBLE] {el}")
            return all_labeled, not_visible

        # Call the new labeled element collector with visibility check
        all_labeled_elements, not_visible_elements = collect_all_labeled_elements_and_visibility()
        # Optionally, add these to all_locators (if you want to save them)
        # for el in all_labeled_elements:
        #     all_locators.append(el)
        print("[PHASE] Finished scroll-and-collect-clickables. Starting actionable element loop.")
        def fuzzy_match(label, candidates):
            label_norm = re.sub(r'\s+', ' ', label.strip().lower())
            for cand in candidates:
                cand_norm = re.sub(r'\s+', ' ', cand.strip().lower())
                if label_norm in cand_norm or cand_norm in label_norm:
                    return True
                # Use difflib for partial similarity
                if SequenceMatcher(None, label_norm, cand_norm).ratio() > 0.85:
                    return True
            return False

        def wait_for_element_visible(driver, xpath, timeout=10):
            try:
                return WebDriverWait(driver, timeout).until(
                    lambda d: d.find_element('xpath', xpath).is_displayed()
                )
            except Exception:
                return False

        def try_dismiss_popups(driver):
            # Try to dismiss common popups/dialogs
            popup_xpaths = [
                "//android.widget.Button[@text='OK']",
                "//android.widget.Button[@text='Tutup']",
                "//android.widget.Button[@text='Close']",
                "//android.widget.Button[@text='Cancel']",
                "//android.widget.Button[@text='Batal']",
                "//android.widget.Button[@content-desc='Tutup']",
                "//android.widget.Button[@content-desc='Close']",
                "//android.widget.Button[@content-desc='OK']",
            ]
            for xpath in popup_xpaths:
                try:
                    btns = driver.find_elements('xpath', xpath)
                    for btn in btns:
                        if btn.is_displayed():
                            print(f"[POPUP] Dismissing popup/button: {xpath}")
                            btn.click()
                            return True
                except Exception:
                    continue
            return False

        def multi_directional_scroll(driver):
            # Try vertical and horizontal scrolls
            try:
                driver.swipe(300, 1000, 300, 300, 800)  # vertical down
                driver.swipe(300, 300, 300, 1000, 800)  # vertical up
                driver.swipe(1000, 800, 300, 800, 800)  # horizontal left
                driver.swipe(300, 800, 1000, 800, 800)  # horizontal right
            except Exception:
                pass

        def scroll_and_collect_clickables():
            all_clickables = []
            unique_keys = set()
            last_page_source = None
            max_scrolls = 8
            scroll_count = 0
            while scroll_count < max_scrolls:
                print(f"[DEBUG] scroll-and-collect-clickables: scroll_count={scroll_count}")
                try:
                    clickables = driver.find_elements("xpath", "//*[@clickable='true']")
                except WebDriverException as e:
                    print(f"[FATAL] Appium session lost or UiAutomator2 crashed during scroll-and-collect: {e}")
                    if config and package:
                        print("[RECOVERY] Attempting to restart Appium session and continue crawling (scroll phase)...")
                        driver.quit()
                        print("[RECOVERY] Appium session restart required. Exiting scroll phase to allow outer recovery.")
                        return []
                    else:
                        print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                        return []
                for el in clickables:
                    try:
                        key = (
                            el.location['x'], el.location['y'],
                            el.size['width'], el.size['height'],
                            el.get_attribute('class'),
                            el.get_attribute('content-desc') or '',
                            el.get_attribute('text') or ''
                        )
                        if key not in unique_keys:
                            all_clickables.append(el)
                            unique_keys.add(key)
                    except Exception:
                        continue
                # Try multi-directional scroll
                multi_directional_scroll(driver)
                time.sleep(1.5)
                new_page_source = driver.page_source
                if new_page_source == last_page_source:
                    break
                last_page_source = new_page_source
                scroll_count += 1
            if scroll_count >= max_scrolls:
                print(f"[WARNING] Reached max_scrolls ({max_scrolls}) in scroll-and-collect-clickables. Breaking to prevent infinite loop.")
            return all_clickables

        print("[PHASE] Starting actionable element click phase.")
        clickable_elements = scroll_and_collect_clickables()
        print("[PHASE] Finished scroll-and-collect-clickables. Starting actionable element loop.")
        for el in clickable_elements:
            try:
                # Aggregate all text/content-desc from self and descendants
                def aggregate_labels(element):
                    labels = []
                    try:
                        text = element.get_attribute('text') or ''
                        desc = element.get_attribute('content-desc') or ''
                        if text.strip():
                            labels.append(text.strip())
                        if desc.strip():
                            labels.append(desc.strip())
                        children = element.find_elements('xpath', './*')
                        for child in children:
                            labels.extend(aggregate_labels(child))
                    except Exception:
                        pass
                    return labels
                all_labels = aggregate_labels(el)
                aggregated_label = ' '.join(all_labels)

                # Check if element is meaningful without hardcoded labels
                if not is_meaningful_element_legacy(el):
                    continue

                # Use aggregated label as actionable identifier
                actionable = aggregated_label.strip() if aggregated_label.strip() else "Unnamed Element"

                # Skip if already visited
                if actionable in visited_texts:
                    continue
                # Wait for element to be visible/clickable
                try:
                    xpath = el.get_attribute('xpath') if hasattr(el, 'get_attribute') else None
                    if xpath:
                        wait_for_element_visible(driver, xpath, timeout=10)
                except Exception:
                    pass
                # Save locator for the actionable element before clicking
                actionable_locator = {
                    "screen_name": get_screen_name(driver),
                    "package": driver.current_package,
                    "resource_id": el.get_attribute("resource-id"),
                    "class_name": el.get_attribute("class"),
                    "text": el.get_attribute("text"),
                    "content_desc": el.get_attribute("content-desc"),
                    "xpath": get_xpath_for_element_by_attrs(el, driver.page_source),
                    "element_type": el.get_attribute("class"),
                }
                all_locators.append(actionable_locator)
                el_class = el.get_attribute('class')
                print(f"[ACTIONABLE] Clicking: label='{actionable}', class='{el_class}' at depth {depth}")
                visited_texts.add(actionable)
                step_start = time.time()
                prev_activity = driver.current_activity
                prev_xml = driver.page_source
                print(f"[DEBUG-TIME] Before el.click() for '{actionable}' at {step_start:.2f}")
                el.click()
                # Try to dismiss popups/dialogs after click
                try_dismiss_popups(driver)
                robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                print(f"[DEBUG-TIME] After click+wait for '{actionable}': took {time.time() - step_start:.2f} seconds")
                context = detect_context(driver)
                print(f"[ACTIONABLE] Detected context after click: {context}")
                new_xml = driver.page_source
                new_screen_name = get_screen_name(driver)
                new_locators = extract_locators_from_xml(new_xml, new_screen_name, driver.current_package)
                print(f"[ACTIONABLE] Collected {len(new_locators)} locators on navigated screen.")
                all_locators.extend(new_locators)
                save_screen_xml(f"{driver.current_package}__{new_screen_name}", new_xml)
                back_start = time.time()
                print(f"[DEBUG-TIME] Before smart_go_back for '{actionable}' at {back_start:.2f}")
                prev_activity = driver.current_activity
                prev_xml = driver.page_source
                smart_go_back(driver, main_package)
                robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                print(f"[DEBUG-TIME] After smart_go_back+wait for '{actionable}': took {time.time() - back_start:.2f} seconds")
            except WebDriverException as e:
                print(f"[FATAL] Appium session lost or UiAutomator2 crashed: {e}")
                if config and package:
                    print("[RECOVERY] Attempting to restart Appium session and continue crawling (actionable phase)...")
                    driver.quit()
                    driver = start_appium_session(package, config['wait_timeout'])
                    print("[RECOVERY] Appium session restarted. Retrying actionable element loop.")
                    return crawl_menus_on_page(driver, main_package, all_locators, menu_descs, depth, is_main_page, visited_texts, config, package)
                else:
                    print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                    return
                print("[FATAL] Exiting crawl. Please check Appium server and device logs.")
                return
            except Exception as e:
                print(f"[ACTIONABLE] Error processing element: {e}")
                continue
        print("[PHASE] Finished actionable element loop.")
    else:
        # For submenus, only log/collect submenus present on this page (do not click recursively)
        for menu_desc in menu_descs:
            try:
                xpath = f"//*[@content-desc='{menu_desc}']"
                menu_elements = driver.find_elements("xpath", xpath)
                if not menu_elements:
                    continue
                menu_el = menu_elements[0]
                el_class = menu_el.get_attribute('class')
                print(f"[SUBMENU] Present submenu: content_desc='{menu_desc}', class='{el_class}', xpath='{xpath}' at depth {depth}")
            except Exception as e:
                print(f"[SUBMENU] Error processing submenu '{menu_desc}': {e}")
                continue

def crawl_app(driver, package, max_depth=3, max_elements=100, config=None):
    """
    Main recursive app crawler. Calls crawl_menus_on_page and handles depth/element limits.
    """
    main_package = package
    all_locators = []
    # Define main menu content-descs
    MAIN_MENUS = [
        'Ruang GTK', 'Ruang Murid', 'Ruang Sekolah', 'Ruang Bahasa',
        'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua',
        'Sumber Belajar', 'Pusat Perbukuan', 'Pengelolaan Kinerja', 'Lihat Semua',
        'Butuh Bantuan?', 'Ruang', 'Pemberitahuan', 'Akun'

    ]
    crawl_menus_on_page(driver, package, all_locators, MAIN_MENUS, depth=0, config=config, package=package)
    # Ensure crawl_app always returns a list
    if all_locators is None:
        all_locators = []
    return all_locators

def collect_all_labeled_elements(driver, xml, log_prefix=""):
    """
    Collect and log ALL elements with non-empty text or content-desc, regardless of clickable/focusable/interactivity.
    Log their class, text, content-desc, resource-id, and XPath.
    """
    from xml.etree import ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    all_labeled = []
    def recurse(node, path):
        # Count siblings of the same tag to get the index
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        if (
            (text and text.strip()) or
            (content_desc and content_desc.strip()) or
            (resource_id and resource_id.strip()) or
            (attrs.get('clickable', '') == 'true') or
            (attrs.get('focusable', '') == 'true')
        ):
            print(f"{log_prefix}[ALL-ELEMENT] class='{class_name}', text='{text}', content_desc='{content_desc}', resource_id='{resource_id}', xpath='{this_xpath}'")
            all_labeled.append({
                'class': class_name,
                'text': text,
                'content_desc': content_desc,
                'resource_id': resource_id,
                'xpath': this_xpath
            })
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"{log_prefix}[ALL-ELEMENT] Total labeled elements found: {len(all_labeled)}")
    return all_labeled

def collect_gtk_web_locators(gtk_url):
    from appium import webdriver as web_driver
    from selenium.webdriver.common.by import By
    import time
    caps = {
        "platformName": "Android",
        "deviceName": "emulator-5554",
        "browserName": "Chrome",
        # "chromedriverExecutableDir": "/path/to/chromedriver",  # Uncomment and set if needed
        "automationName": "UiAutomator2"
    }
    print("[INFO] [CHROME] Starting temporary Appium session for Chrome web collection...")
    driver = web_driver.Remote("http://localhost:4723/wd/hub", caps)
    time.sleep(3)
    print(f"[INFO] [CHROME] Navigating to {gtk_url}")
    driver.get(gtk_url)
    time.sleep(5)
    elements = driver.find_elements(By.XPATH, "//a | //button | //*[@role='button']")
    locators = []
    for el in elements:
        try:
            tag = el.tag_name
            text = el.text
            el_id = el.get_attribute('id')
            el_class = el.get_attribute('class')
            href = el.get_attribute('href')
            locators.append({
                "tag": tag,
                "text": text,
                "id": el_id,
                "class": el_class,
                "href": href
            })
        except Exception as e:
            print(f"[CHROME] Error extracting element: {e}")
    os.makedirs("locators", exist_ok=True)
    with open("locators/gtk_web_elements.json", "w") as f:
        json.dump(locators, f, indent=2)
    print(f"[INFO] [CHROME] Collected {len(locators)} web elements from GTK page. Saved to locators/gtk_web_elements.json.")
    driver.quit()

def collect_chrome_cdp_locators():
    print("[CDP] Attempting to collect web locators from Chrome via DevTools Protocol...")
    try:
        resp = requests.get("http://localhost:9222/json")
        tabs = resp.json()
        if not tabs:
            print("[CDP] No Chrome tabs found.")
            return []
        tab = tabs[0]
        ws_url = tab['webSocketDebuggerUrl']
        ws = websocket.create_connection(ws_url)
        ws.send(json.dumps({"id": 1, "method": "DOM.enable"}))
        ws.send(json.dumps({"id": 2, "method": "Runtime.enable"}))
        ws.send(json.dumps({
            "id": 3,
            "method": "Runtime.evaluate",
            "params": {
                "expression": """
                JSON.stringify(
                  [...document.querySelectorAll('a,button,[role=button]')].map(el => ({
                    tag: el.tagName,
                    text: el.innerText,
                    id: el.id,
                    class: el.className,
                    href: el.href
                  }))
                )
                """,
                "returnByValue": True
            }
        }))
        while True:
            result = ws.recv()
            if '"id":3' in result:
                break
        data = json.loads(result)
        elements = json.loads(data['result']['result']['value'])
        ws.close()
        print(f"[CDP] Collected {len(elements)} web elements from Chrome page.")
        import os
        os.makedirs("locators", exist_ok=True)
        with open("locators/gtk_web_elements.json", "w") as f:
            json.dump(elements, f, indent=2)
        return elements
    except Exception as e:
        print(f"[CDP] Error collecting web locators: {e}")
        return []

DEBUG_ACTIONABLE_ONLY = False  # Set to True to only visit actionable items for debugging
DEBUG_SCROLL_ONLY = False  # Set to True to only scroll and collect all locators, skip menu clicks

def find_clickable_by_attrs(driver, attrs):
    candidates = driver.find_elements("xpath", "//*[@clickable='true']")
    for el in candidates:
        try:
            if (
                el.get_attribute("resource-id") == attrs["resource-id"] and
                el.get_attribute("content-desc") == attrs["content-desc"] and
                el.get_attribute("text") == attrs["text"] and
                el.get_attribute("class") == attrs["class"] and
                el.get_attribute("bounds") == attrs["bounds"]
            ):
                return el
        except Exception:
            continue
    return None

# Helper to group click candidates by row and find arrow/button in each row
def group_submenu_rows(click_candidates):
    # Group by parent element (row)
    rows = defaultdict(lambda: {"labels": [], "arrows": []})
    for el in click_candidates:
        try:
            parent = el.find_element("xpath", "..")
            parent_id = parent.id if hasattr(parent, 'id') else str(parent)
            class_name = el.get_attribute("class")
            content_desc = el.get_attribute("content-desc")
            text = el.get_attribute("text")
            # Heuristic: arrow is likely an ImageView or Button with right arrow or empty text
            if (class_name in ["android.widget.ImageView", "android.widget.Button"] and
                ("arrow" in (content_desc or "").lower() or "arrow" in (text or "").lower() or (content_desc == '' and text == ''))):
                rows[parent_id]["arrows"].append(el)
            elif class_name in ["android.widget.TextView", "android.widget.Button", "android.view.View"]:
                rows[parent_id]["labels"].append(el)
            else:
                rows[parent_id]["labels"].append(el)
        except Exception:
            continue
    return rows

def replay_navigation_stack(driver, navigation_stack, main_package, config):
    print(f"[RECOVERY] Replaying navigation stack: {navigation_stack}")
    for depth, node_name in enumerate(navigation_stack[1:], 1):
        print(f"[RECOVERY] Navigating to '{node_name}' at depth {depth}")
        try:
            el = None
            try:
                el = driver.find_element("xpath", f"//*[@content-desc='{node_name}']")
            except Exception:
                pass
            if not el:
                try:
                    el = driver.find_element("xpath", f"//*[@text='{node_name}']")
                except Exception:
                    pass
            if not el:
                print(f"[RECOVERY] Could not find element for '{node_name}' during replay. Aborting recovery.")
                return False
            el.click()
            time.sleep(2)
        except Exception as e:
            print(f"[RECOVERY] Error navigating to '{node_name}': {e}")
            return False
    print(f"[RECOVERY] Navigation stack replayed successfully.")
    return True

def crawl_menu_hierarchy(driver, node_name, main_package, config, depth=0, max_depth=5, visited_hashes=None, chrome_opened_from_app=False, navigation_stack=None, retry_count=0, max_retries=3):
    import hashlib
    if visited_hashes is None:
        visited_hashes = set()
    if navigation_stack is None:
        navigation_stack = [node_name]
    if depth > max_depth:
        return None
    indent = '  ' * depth
    print(f"{indent}[ENTER] Node: '{node_name}' at depth {depth}")
    node = {
        "name": node_name,
        "locators": [],
        "children": []
    }
    try:
        robust_wait_for_new_screen(driver, driver.current_activity, driver.page_source, timeout=10)
        context = detect_context(driver)
        print(f"{indent}[CONTEXT] Detected context: {context}")
        current_package = driver.current_package
        # Strict enforcement: Only crawl main package or webview/Chrome opened from app
        if context == 'native':
            if current_package != main_package:
                print(f"{indent}[STRICT] Skipping non-main package '{current_package}'. Switching back to main app.")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            xml = driver.page_source
            screen_name = get_screen_name(driver)
            print(f"{indent}[COLLECT] Collecting native locators on '{node_name}' at depth {depth}")
            node["locators"] = extract_locators_from_xml(xml, screen_name, driver.current_package)
            if not node["locators"]:
                print(f"{indent}[INFO] No element locators found on '{node_name}' at depth {depth}. Going back to previous.")
                if depth > 0:
                    smart_go_back(driver, main_package, chrome_opened_from_app)
                    robust_wait_for_new_screen(driver, None, None, timeout=10)
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            try:
                while True:
                    # Strict enforcement: Only process if in main package or directly opened Chrome/webview
                    current_package = driver.current_package
                    context_now = detect_context(driver)
                    if context_now == 'native' and current_package != main_package:
                        print(f"{indent}[STRICT] Not in main package '{main_package}' (current: '{current_package}'). Switching back and skipping.")
                        try:
                            driver.activate_app(main_package)
                            time.sleep(2)
                        except Exception as e:
                            print(f"{indent}[STRICT] Could not return to main app: {e}")
                        break
                    if context_now in ['chrome_native', 'webview'] and not chrome_opened_from_app:
                        print(f"{indent}[STRICT] In Chrome/webview context NOT opened from app. Switching back and skipping.")
                        try:
                            driver.activate_app(main_package)
                            time.sleep(2)
                        except Exception as e:
                            print(f"{indent}[STRICT] Could not return to main app: {e}")
                        break
                    try:
                        click_candidates = driver.find_elements(
                            "xpath",
                            "//*[@clickable='true' or @focusable='true' or string-length(@content-desc)>0 or string-length(@text)>0]"
                        )
                        clickable_attrs_list = []
                        print(f"{indent}  [LOG] All click candidate elements on this page:")
                        for el in click_candidates:
                            try:
                                attrs = {
                                    "resource-id": el.get_attribute("resource-id"),
                                    "content-desc": el.get_attribute("content-desc"),
                                    "text": el.get_attribute("text"),
                                    "class": el.get_attribute("class"),
                                    "bounds": el.get_attribute("bounds"),
                                    "clickable": el.get_attribute("clickable"),
                                    "focusable": el.get_attribute("focusable"),
                                }
                                print(f"{indent}    - class='{attrs['class']}', text='{attrs['text']}', content_desc='{attrs['content-desc']}', clickable='{attrs['clickable']}', focusable='{attrs['focusable']}'")
                                el_hash = hashlib.md5(str(tuple(attrs.values())).encode()).hexdigest()
                                if el_hash in visited_hashes:
                                    print(f"{indent}  [SKIP] Already visited element with hash '{el_hash}' at depth {depth+1}")
                                    continue
                                clickable_attrs_list.append((attrs, el_hash))
                            except Exception:
                                continue
                    except StaleElementReferenceException:
                        print(f"{indent}[WARN] StaleElementReferenceException when finding clickables, re-collecting.")
                        continue
                    any_clicked = False
                    for attrs, el_hash in clickable_attrs_list:
                        try:
                            visited_hashes.add(el_hash)
                            el_label = (attrs['content-desc'] or attrs['text'] or '').strip() or f"Element"
                            print(f"{indent}[DEBUG] Visiting submenu '{el_label}' at depth {depth+1}")
                            print(f"{indent}  [LOG] Label locator: class='{attrs['class']}', text='{attrs['text']}', content_desc='{attrs['content-desc']}'")
                            print(f"{indent}  [LOG] Arrow locator: class='{attrs['class']}', text='{attrs['text']}', content_desc='{attrs['content-desc']}'")
                            prev_activity = driver.current_activity
                            prev_xml = driver.page_source
                            clicked = False
                            # Try to click the label/description first
                            if attrs.get("resource-id") or attrs.get("content-desc") or attrs.get("text"):
                                try:
                                    print(f"{indent}  [CLICK] Trying label for submenu '{el_label}' at depth {depth+1}")
                                    el = find_clickable_by_attrs(driver, attrs)
                                    if el:
                                        el.click()
                                        clicked = True
                                        clicked_type = "label"
                                        print(f"{indent}  [INFO] Clicked using: {clicked_type}")
                                    else:
                                        print(f"{indent}  [WARN] Could not re-find element for label '{el_label}' at depth {depth+1}")
                                except Exception as e:
                                    print(f"{indent}  [WARN] Could not click label for '{el_label}': {e}")
                            # If label not clickable, try the arrow as fallback
                            if not clicked and (attrs.get("resource-id") or attrs.get("content-desc") or attrs.get("text")):
                                try:
                                    print(f"{indent}  [CLICK] Trying arrow for submenu '{el_label}' at depth {depth+1}")
                                    el = find_clickable_by_attrs(driver, attrs)
                                    if el:
                                        el.click()
                                        clicked = True
                                        clicked_type = "arrow"
                                        print(f"{indent}  [INFO] Clicked using: {clicked_type}")
                                    else:
                                        print(f"{indent}  [WARN] Could not re-find element for arrow '{el_label}' at depth {depth+1}")
                                except Exception as e:
                                    print(f"{indent}  [WARN] Could not click arrow for '{el_label}': {e}")
                            if not clicked:
                                print(f"{indent}  [SKIP] Could not click label or arrow for '{el_label}' at depth {depth+1}")
                                continue
                            # Verify that click led to a new page
                            robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=10)
                            new_activity = driver.current_activity
                            new_xml = driver.page_source
                            if new_activity == prev_activity and new_xml == prev_xml:
                                print(f"{indent}  [SKIP] Click on '{el_label}' did not change screen at depth {depth+1}. Skipping recursion.")
                                continue
                            child_context = detect_context(driver)
                            print(f"{indent}  [CONTEXT] Detected context after click: {child_context}")
                            # Strict enforcement after click
                            current_package_after = driver.current_package
                            context_after = detect_context(driver)
                            if context_after == 'native' and current_package_after != main_package:
                                print(f"{indent}[STRICT] After click: Not in main package '{main_package}' (current: '{current_package_after}'). Switching back and skipping.")
                                try:
                                    driver.activate_app(main_package)
                                    time.sleep(2)
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                                continue
                            if context_after in ['chrome_native', 'webview'] and not chrome_opened_from_app:
                                print(f"{indent}[STRICT] After click: In Chrome/webview context NOT opened from app. Switching back and skipping.")
                                try:
                                    driver.activate_app(main_package)
                                    time.sleep(2)
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                                continue
                            # If context is chrome_native or webview, collect locators, kill external app, and return to main app
                            if child_context in ['chrome_native', 'webview']:
                                print(f"{indent}[STRICT] External app (Chrome/webview) detected after click on '{el_label}'. Collecting first page locators.")
                                ext_locators = collect_gtk_web_locators(driver.current_url) if hasattr(driver, 'current_url') else []
                                child_node = {
                                    "name": el_label,
                                    "locators": ext_locators,
                                    "children": [],
                                    "external": True
                                }
                                print(f"{indent}[STRICT] Collected {len(ext_locators)} locators from external app. Killing external app and returning to main app.")
                                try:
                                    subprocess.run(["adb", "shell", "am", "force-stop", driver.current_package])
                                    print(f"{indent}[STRICT] Killed external app: {driver.current_package}")
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not kill external app: {e}")
                                try:
                                    driver.activate_app(main_package)
                                    time.sleep(2)
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                                node["children"].append(child_node)
                                any_clicked = True
                                break
                            # RECURSE: Add to navigation stack
                            recursed_child = crawl_menu_hierarchy(
                                driver, el_label, main_package, config, depth+1, max_depth, visited_hashes, chrome_opened_from_app=False,
                                navigation_stack=navigation_stack + [el_label]
                            )
                            if recursed_child:
                                child_node = {
                                    "name": el_label,
                                    "locators": recursed_child.get("locators", []),
                                    "children": recursed_child.get("children", []),
                                    "locator": {
                                        "screen_name": screen_name,
                                        "package": driver.current_package,
                                        "resource_id": attrs["resource-id"],
                                        "class_name": attrs["class"],
                                        "text": attrs["text"],
                                        "content_desc": attrs["content-desc"],
                                        "bounds": attrs["bounds"],
                                        "element_type": attrs["class"],
                                    }
                                }
                                node["children"].append(child_node)
                            print(f"{indent}  [BACK] Returning from child '{el_label}' to '{node_name}' at depth {depth}")
                            smart_go_back(driver, main_package, chrome_opened_from_app)
                            robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=10)
                            print(f"{indent}  [RETURN] Finished child: '{el_label}' at depth {depth+1}")
                            any_clicked = True
                            break  # After any navigation, break and re-collect clickables
                        except StaleElementReferenceException:
                            print(f"{indent}  [WARN] StaleElementReferenceException on element access, skipping this branch.")
                            continue
                        except WebDriverException as e:
                            print(f"{indent}  [ERROR] WebDriverException: {e}. Skipping this branch.")
                            # RECOVERY: Restart Appium, replay navigation stack, and resume crawling
                            if retry_count < max_retries:
                                print(f"{indent}[RECOVERY] Appium/WebDriverException detected. Restarting Appium and replaying navigation stack (attempt {retry_count+1}/{max_retries})...")
                                try:
                                    driver.quit()
                                except Exception:
                                    pass
                                driver_new = start_appium_session(main_package, config['wait_timeout'])
                                success = replay_navigation_stack(driver_new, navigation_stack, main_package, config)
                                if success:
                                    print(f"{indent}[RECOVERY] Successfully returned to '{node_name}' at depth {depth}. Resuming crawl...")
                                    # Resume crawl from this node with new driver and incremented retry_count
                                    recursed_child = crawl_menu_hierarchy(
                                        driver_new, node_name, main_package, config, depth, max_depth, visited_hashes, chrome_opened_from_app,
                                        navigation_stack=navigation_stack, retry_count=retry_count+1, max_retries=max_retries
                                    )
                                    if recursed_child:
                                        node.update(recursed_child)
                                    return node
                                else:
                                    print(f"{indent}[RECOVERY] Failed to return to '{node_name}' after Appium restart. Skipping this branch.")
                                    return node
                            else:
                                print(f"{indent}[RECOVERY] Max retries reached for '{node_name}'. Skipping this branch.")
                                return node
                        except Exception as e:
                            print(f"{indent}  [ERROR] Exception during element click/recursion: {e}")
                            continue
                    if not any_clicked:
                        break  # No more new clickables, exit loop
            except StaleElementReferenceException:
                print(f"{indent}[WARN] StaleElementReferenceException when finding clickables, skipping this node.")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
        elif context == 'webview':
            if not chrome_opened_from_app:
                print(f"{indent}[STRICT] Webview context NOT opened from app. Skipping and switching back to main app.")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            print(f"{indent}[COLLECT] Collecting webview locators on '{node_name}' at depth {depth}")
            node["locators"] = collect_gtk_web_locators(driver.current_url) if hasattr(driver, 'current_url') else []
            print(f"{indent}[STRICT] After collecting webview locators, killing external app and returning to main app.")
            # Kill the external webview app (Chrome)
            try:
                subprocess.run(["adb", "shell", "am", "force-stop", driver.current_package])
                print(f"{indent}[STRICT] Killed external app: {driver.current_package}")
            except Exception as e:
                print(f"{indent}[STRICT] Could not kill external app: {e}")
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"{indent}[STRICT] Could not return to main app: {e}")
            print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
            return node
        elif context == 'chrome_native':
            if chrome_opened_from_app:
                print(f"{indent}[CHROME] Detected Chrome mobile context opened from app. Collecting only the first page's element locators and returning to app.")
                node["locators"] = collect_gtk_web_locators(driver.current_url) if hasattr(driver, 'current_url') else []
                print(f"{indent}[CHROME] Collected {len(node['locators'])} locators from Chrome mobile. Killing external app and switching back to app.")
                # Kill the external Chrome app
                try:
                    subprocess.run(["adb", "shell", "am", "force-stop", driver.current_package])
                    print(f"{indent}[STRICT] Killed external app: {driver.current_package}")
                except Exception as e:
                    print(f"{indent}[STRICT] Could not kill external app: {e}")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[CHROME] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            else:
                print(f"{indent}[STRICT] Chrome context detected but NOT opened from app. Skipping Chrome crawl and switching back to app.")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
        else:
            print(f"{indent}[STRICT] Unknown or external context '{context}'. Skipping and switching back to main app.")
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"{indent}[STRICT] Could not return to main app: {e}")
            print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
            return node
        print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
        return node
    except WebDriverException as e:
        print(f"{indent}[RECOVERY] WebDriverException detected at node '{node_name}': {e}")
        if retry_count < max_retries:
            print(f"{indent}[RECOVERY] Restarting Appium and replaying navigation stack (attempt {retry_count+1}/{max_retries})...")
            try:
                driver.quit()
            except Exception:
                pass
            driver_new = start_appium_session(main_package, config['wait_timeout'])
            success = replay_navigation_stack(driver_new, navigation_stack, main_package, config)
            if success:
                print(f"{indent}[RECOVERY] Successfully returned to '{node_name}' at depth {depth}. Resuming crawl...")
                # Resume crawl from this node with new driver and incremented retry_count
                recursed_child = crawl_menu_hierarchy(
                    driver_new, node_name, main_package, config, depth, max_depth, visited_hashes, chrome_opened_from_app,
                    navigation_stack=navigation_stack, retry_count=retry_count+1, max_retries=max_retries
                )
                if recursed_child:
                    node.update(recursed_child)
                return node
            else:
                print(f"{indent}[RECOVERY] Failed to return to '{node_name}' after Appium restart. Skipping this branch.")
                return node
        else:
            print(f"{indent}[RECOVERY] Max retries reached for '{node_name}'. Skipping this branch.")
            return node
    except Exception as e:
        print(f"{indent}[ERROR] Exception at node '{node_name}': {e}")
        return node

# Update crawl_and_save_menu_hierarchy to call crawl_menu_hierarchy with visited_hashes

def crawl_and_save_menu_hierarchy(driver, config, main_package):
    print("[HIERARCHY] Starting dynamic crawl from main page...")
    root_node = crawl_menu_hierarchy(driver, "Main Page", main_package, config, depth=0, visited_hashes=None)
    os.makedirs(config['locators_folder'], exist_ok=True)
    out_path = os.path.join(config['locators_folder'], "menu_hierarchy.json")
    with open(out_path, "w") as f:
        json.dump(root_node, f, indent=2)
    print(f"[HIERARCHY] Saved menu hierarchy to {out_path}")

def collect_all_native_elements(driver, screen_name, package):
    xml = driver.page_source
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    all_elements = []
    def recurse(node, path):
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        if (
            (text and text.strip()) or
            (content_desc and content_desc.strip()) or
            (resource_id and resource_id.strip())
        ):
            el_info = {
                'screen_name': screen_name,
                'package': package,
                'class': class_name,
                'text': text,
                'content_desc': content_desc,
                'resource_id': resource_id,
                'xpath': this_xpath
            }
            all_elements.append(el_info)
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"[ALL-NATIVE-ELEMENTS] Total: {len(all_elements)}")
    for el in all_elements:
        print(f"[ALL-NATIVE-ELEMENTS] {el}")
    return all_elements

def collect_all_webview_elements(driver):
    print(f"[WEBVIEW] Collecting all labeled/identifiable elements in webview...")
    elements = driver.find_elements(By.XPATH, '//*')
    all_elements = []
    for el in elements:
        try:
            tag = el.tag_name
            text = el.text
            el_id = el.get_attribute('id')
            el_class = el.get_attribute('class')
            name = el.get_attribute('name')
            aria_label = el.get_attribute('aria-label')
            if (
                (text and text.strip()) or
                (el_id and el_id.strip()) or
                (el_class and el_class.strip()) or
                (name and name.strip()) or
                (aria_label and aria_label.strip())
            ):
                # Try to get XPath and CSS selector if possible
                try:
                    xpath = driver.execute_script(
                        '''
                        function absoluteXPath(element) {
                            var comp, comps = [];
                            var parent = null;
                            var xpath = '';
                            var getPos = function(element) {
                                var position = 1, curNode;
                                if (element.nodeType == Node.ATTRIBUTE_NODE) {
                                    return null;
                                }
                                for (curNode = element.previousSibling; curNode; curNode = curNode.previousSibling) {
                                    if (curNode.nodeName == element.nodeName) {
                                        ++position;
                                    }
                                }
                                return position;
                            };
                            if (element instanceof Document) {
                                return '/';
                            }
                            for (; element && !(element instanceof Document); element = element.nodeType ==Node.ATTRIBUTE_NODE ? element.ownerElement : element.parentNode) {
                                comp = comps[comps.length] = {};
                                switch (element.nodeType) {
                                    case Node.TEXT_NODE:
                                        comp.name = 'text()';
                                        break;
                                    case Node.ATTRIBUTE_NODE:
                                        comp.name = '@' + element.nodeName;
                                        break;
                                    case Node.PROCESSING_INSTRUCTION_NODE:
                                        comp.name = 'processing-instruction()';
                                        break;
                                    case Node.COMMENT_NODE:
                                        comp.name = 'comment()';
                                        break;
                                    case Node.ELEMENT_NODE:
                                        comp.name = element.nodeName;
                                        break;
                                }
                                comp.position = getPos(element);
                            }
                            for (var i = comps.length - 1; i >= 0; i--) {
                                comp = comps[i];
                                xpath += '/' + comp.name.toLowerCase();
                                if (comp.position !== null && comp.position > 1) {
                                    xpath += '[' + comp.position + ']';
                                }
                            }
                            return xpath;
                        }
                        return absoluteXPath(arguments[0]);
                        ''', el)
                except Exception:
                    xpath = None
                try:
                    css_selector = driver.execute_script(
                        '''
                        function cssPath(el) {
                            if (!(el instanceof Element)) return;
                            var path = [];
                            while (el.nodeType === Node.ELEMENT_NODE) {
                                var selector = el.nodeName.toLowerCase();
                                if (el.id) {
                                    selector += '#' + el.id;
                                    path.unshift(selector);
                                    break;
                                } else {
                                    var sib = el, nth = 1;
                                    while (sib = sib.previousElementSibling) {
                                        if (sib.nodeName.toLowerCase() == selector) nth++;
                                    }
                                    if (nth != 1) selector += ':nth-of-type(' + nth + ')';
                                }
                                path.unshift(selector);
                                el = el.parentNode;
                            }
                            return path.join(' > ');
                        }
                        return cssPath(arguments[0]);
                        ''', el)
                except Exception:
                    css_selector = None
                locator = {
                    "webview": True,
                    "tag": tag,
                    "text": text,
                    "id": el_id,
                    "class": el_class,
                    "name": name,
                    "aria-label": aria_label,
                    "xpath": xpath,
                    "css_selector": css_selector
                }
                all_elements.append(locator)
        except Exception as e:
            print(f"[WEBVIEW] Error extracting element: {e}")
    print(f"[ALL-WEBVIEW-ELEMENTS] Total: {len(all_elements)}")
    for el in all_elements:
        print(f"[ALL-WEBVIEW-ELEMENTS] {el}")
    return all_elements

def load_previous_all_elements(path):
    if os.path.exists(path):
        with open(path, 'r') as f:
            try:
                data = json.load(f)
                if isinstance(data, dict) and 'all_elements' in data:
                    return data['all_elements']
                elif isinstance(data, list):
                    return data
            except Exception:
                return []
    return []

def collect_full_xml_elements(driver, screen_name, package):
    xml = driver.page_source
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    all_elements = []
    def recurse(node, path):
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        el_info = {
            'screen_name': screen_name,
            'package': package,
            'class': class_name,
            'text': text,
            'content_desc': content_desc,
            'resource_id': resource_id,
            'xpath': this_xpath
        }
        # Add all other attributes for completeness
        for k, v in attrs.items():
            if k not in el_info:
                el_info[k] = v
        all_elements.append(el_info)
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"[FULL-XML-ELEMENTS] Total: {len(all_elements)} on screen '{screen_name}'")
    return all_elements

def hash_node_attrs(attrs):
    # Hash a node's identifying attributes for deduplication
    key = (attrs.get('class', ''), attrs.get('text', ''), attrs.get('content-desc', ''), attrs.get('resource-id', ''), attrs.get('xpath', ''))
    return hashlib.md5(str(key).encode('utf-8')).hexdigest()

def perform_scroll(driver, scroll_count=None):
    try:
        # Try to find the main scrollable container
        scrollable = None
        for class_name in ['ScrollView', 'RecyclerView', 'android.widget.ScrollView', 'androidx.recyclerview.widget.RecyclerView']:
            try:
                scrollable = driver.find_element('xpath', f"//*[contains(@class, '{class_name}')]")
                break
            except Exception:
                continue
        if scrollable:
            loc = scrollable.location
            size = scrollable.size
            start_x = loc['x'] + size['width'] // 2
            start_y = loc['y'] + int(size['height'] * 0.8)
            end_y = loc['y'] + int(size['height'] * 0.2)
            print(f"[SCROLL] Found scrollable container: class={scrollable.get_attribute('class')}, location={loc}, size={size}")
            print(f"[SCROLL] Swiping within container from ({start_x},{start_y}) to ({start_x},{end_y}) [scroll #{scroll_count}]")
        else:
            # Fallback: use whole screen
            size = driver.get_window_size()
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.8)
            end_y = int(size['height'] * 0.2)
            print(f"[SCROLL] No scrollable container found, swiping on screen from ({start_x},{start_y}) to ({start_x},{end_y}) [scroll #{scroll_count}]")
        driver.execute_script('mobile: swipeGesture', {
            'left': start_x,
            'top': start_y,
            'width': 1,
            'height': 1,
            'direction': 'up',
            'percent': 0.8
        })
        print(f"[SCROLL] Performed mobile: swipeGesture (up) [scroll #{scroll_count}]")
        screenshot_path = f"scroll_screenshot_{scroll_count}.png" if scroll_count is not None else "scroll_screenshot.png"
        driver.save_screenshot(screenshot_path)
        print(f"[SCROLL] Screenshot saved: {screenshot_path}")
    except Exception as e:
        print(f"[SCROLL] Scroll within container failed: {e}")

def collect_full_scrollable_page_elements(driver, screen_name, package):
    print(f"[SCROLL] Starting full scroll-and-collect for screen '{screen_name}'...")
    scroll_until_bottom(driver)
    seen_hashes = set()
    all_elements = []
    xml = driver.page_source
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    def recurse(node, path):
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        el_info = {
            'screen_name': screen_name,
            'package': package,
            'class': class_name,
            'text': text,
            'content_desc': content_desc,
            'resource_id': resource_id,
            'xpath': this_xpath
        }
        for k, v in attrs.items():
            if k not in el_info:
                el_info[k] = v
        node_hash = hash_node_attrs(el_info)
        if node_hash not in seen_hashes:
            all_elements.append(el_info)
            seen_hashes.add(node_hash)
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"[FULL-SCROLL] Collected {len(all_elements)} unique elements for screen '{screen_name}'.")
    return all_elements

def recursive_full_crawl(driver, package, visited_screens, nav_path, all_collected, nav_tree, depth=0, max_depth=10):
    screen_name = get_screen_name(driver)
    print(f"[RECURSIVE-CRAWL] Visiting screen: {screen_name}, depth={depth}")
    if screen_name in visited_screens or depth > max_depth:
        print(f"[RECURSIVE-CRAWL] Already visited or max depth reached: {screen_name}")
        return
    visited_screens.add(screen_name)
    elements = collect_full_scrollable_page_elements(driver, screen_name, package)
    all_collected.extend([el for el in elements if el not in all_collected])
    nav_tree[screen_name] = {'elements': elements, 'children': {}}
    # Find clickable elements with content-desc
    nav_targets = [el for el in elements if el.get('clickable') == 'true' and el.get('content_desc')]
    for el in nav_targets:
        print(f"[RECURSIVE-CRAWL] Clicking element: content-desc='{el.get('content_desc')}', xpath='{el.get('xpath')}'")
        try:
            # Find element by xpath and click
            found_els = driver.find_elements('xpath', el['xpath'])
            if found_els:
                found_els[0].click()
                time.sleep(2)
                recursive_full_crawl(driver, package, visited_screens, nav_path + [el.get('content_desc')], all_collected, nav_tree[screen_name]['children'], depth+1, max_depth)
                driver.back()
                time.sleep(1)
            else:
                print(f"[RECURSIVE-CRAWL] Could not find element by xpath: {el['xpath']}")
        except Exception as e:
            print(f"[RECURSIVE-CRAWL] Error clicking element: {e}")

def scroll_to_section(driver, marker_text=None, marker_desc=None):
    print(f"[SCROLL] scroll_to_section called with marker_text={marker_text}, marker_desc={marker_desc}")
    try:
        if marker_text:
            print(f"[SCROLL] Attempting UiScrollable scroll to text: {marker_text}")
            driver.find_element(
                f"android=new UiScrollable(new UiSelector().scrollable(true)).scrollIntoView(new UiSelector().textContains(\"{marker_text}\"))"
            )
            print(f"[SCROLL] UiScrollable scrolled to text: {marker_text}")
        elif marker_desc:
            print(f"[SCROLL] Attempting UiScrollable scroll to content-desc: {marker_desc}")
            driver.find_element(
                f"android=new UiScrollable(new UiSelector().scrollable(true)).scrollIntoView(new UiSelector().descriptionContains(\"{marker_desc}\"))"
            )
            print(f"[SCROLL] UiScrollable scrolled to content-desc: {marker_desc}")
    except Exception as e:
        print(f"[SCROLL] UiScrollable failed to scroll to marker (text='{marker_text}', desc='{marker_desc}'): {e}")

def perform_swipe(driver, direction='down', duration=1000):
    """Universal swipe function that works on any Android view"""
    window = driver.get_window_size()
    width, height = window['width'], window['height']
    if direction.lower() == 'down':
        start_x, start_y = width//2, int(height*0.7)
        end_x, end_y = width//2, int(height*0.3)
    elif direction.lower() == 'up':
        start_x, start_y = width//2, int(height*0.3)
        end_x, end_y = width//2, int(height*0.7)
    else:
        raise ValueError("Direction must be 'up' or 'down'")
    print(f"[SCROLL] Swiping {direction} from ({start_x},{start_y}) to ({end_x},{end_y})")
    driver.swipe(start_x, start_y, end_x, end_y, duration)
    time.sleep(1.5)

def find_scrollable_element(driver):
    """Locates the main scrollable view if one exists"""
    scrollables = driver.find_elements(AppiumBy.ANDROID_UIAUTOMATOR,
        'new UiSelector().scrollable(true)')
    if scrollables:
        return max(scrollables, key=lambda e: e.size['height'] * e.size['width'])
    return None

def smart_scroll(driver, direction='down'):
    scrollable = find_scrollable_element(driver)
    if scrollable:
        loc = scrollable.location
        size = scrollable.size
        center_x = loc['x'] + size['width']//2
        if direction == 'down':
            start_y = loc['y'] + int(size['height'] * 0.8)
            end_y = loc['y'] + int(size['height'] * 0.2)
        else:
            start_y = loc['y'] + int(size['height'] * 0.2)
            end_y = loc['y'] + int(size['height'] * 0.8)
        print(f"[SCROLL] Smart scroll {direction} in container from ({center_x},{start_y}) to ({center_x},{end_y})")
        driver.swipe(center_x, int(start_y), center_x, int(end_y), 1000)
        time.sleep(1.5)
    else:
        print("[SCROLL] No scrollable container found, falling back to full screen swipe.")
        perform_swipe(driver, direction)

def is_same_content(driver, previous_source, similarity_threshold=0.95):
    current = driver.page_source
    if previous_source == current:
        return True
    matcher = difflib.SequenceMatcher(None, previous_source, current)
    return matcher.ratio() > similarity_threshold

def scroll_until_end(driver, max_attempts=10):
    previous_source = driver.page_source
    attempt = 0
    while attempt < max_attempts:
        for method in [smart_scroll, perform_swipe]:
            try:
                method(driver, 'down')
                driver.save_screenshot(f"scroll_debug_{attempt}_{method.__name__}.png")
                time.sleep(2)
                if is_same_content(driver, previous_source):
                    print("[SCROLL] Reached end of content.")
                    return True
                previous_source = driver.page_source
                break
            except Exception as e:
                print(f"[SCROLL] Scroll failed: {str(e)}")
                continue
        attempt += 1
    print("[SCROLL] Max scroll attempts reached.")
    return False

def extract_current_elements(driver):
    elements = []
    for el in driver.find_elements(AppiumBy.XPATH, '//*'):
        try:
            elements.append({
                'text': el.text,
                'class': el.get_attribute('class'),
                'content-desc': el.get_attribute('content-desc'),
                'resource-id': el.get_attribute('resource-id'),
                'xpath': el.get_attribute('xpath') if hasattr(el, 'get_attribute') else '',
                'bounds': el.rect
            })
        except StaleElementReferenceException:
            continue
    return elements

def collect_elements_with_scroll(driver, max_attempts=8):
    print("[COLLECT] Starting element collection with scrolling...")
    collected_elements = []
    seen_xpaths = set()
    previous_source = driver.page_source
    attempt = 0

    while attempt < max_attempts:
        # Extract and merge elements after each scroll
        current_elements = extract_current_elements(driver)
        new_elements = [e for e in current_elements if e['xpath'] not in seen_xpaths]
        for e in new_elements:
            collected_elements.append(e)
            seen_xpaths.add(e['xpath'])
        print(f"[COLLECT] After scroll {attempt}: {len(new_elements)} new elements, {len(collected_elements)} total.")

        # Save screenshot for debugging
        driver.save_screenshot(f"scroll_debug_{attempt}.png")

        # Try to scroll
        smart_scroll(driver, 'down')
        time.sleep(1.5)  # Wait for content to load

        # Check if new content appeared
        current_source = driver.page_source
        if current_source == previous_source:
            print("[COLLECT] No new content after scroll, stopping.")
            break
        previous_source = current_source
        attempt += 1

    print(f"[COLLECT] Finished. Total unique elements collected: {len(collected_elements)}")
    return collected_elements

def debug_scroll(driver):
    for i in range(3):
        driver.save_screenshot(f"scroll_debug_{i}_before.png")
        perform_swipe(driver)
        time.sleep(1)
        driver.save_screenshot(f"scroll_debug_{i}_after.png")

def safe_scroll_down(driver, scroll_count=1):
    """Perform safe scroll down that won't trigger pull-to-refresh"""
    try:
        size = driver.get_window_size()
        start_x = size['width'] // 2

        # Start from safe zone (not too high to avoid pull-to-refresh)
        start_y = int(size['height'] * 0.4)  # Start from 40% down
        end_y = int(size['height'] * 0.2)    # End at 20% down

        for i in range(scroll_count):
            print(f"[SAFE_SCROLL] Safe scroll down {i+1}/{scroll_count}")
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)  # Pause between scrolls

        return True

    except Exception as e:
        print(f"[SAFE_SCROLL] Error in safe scroll: {e}")
        return False

def scroll_and_collect_interactive(driver, log_prefix="", max_scrolls=8):
    def get_interactive_elements(current_xml):
        elements = driver.find_elements("xpath", '//*[((@clickable="true" or @focusable="true") and @enabled="true") or (string-length(@content-desc)>0 or string-length(@text)>0)]')
        interactive_info = []
        for idx, el in enumerate(elements):
            try:
                xpath = get_xpath_for_element_by_attrs(el, current_xml)
                el_desc = el.get_attribute('content-desc')
                el_text = el.get_attribute('text')
                el_class = el.get_attribute('class')
                clickable = el.get_attribute('clickable') == 'true'
                focusable = el.get_attribute('focusable') == 'true'
                child_labels = []
                children = []
                try:
                    children = el.find_elements('xpath', './*')
                    for child in children:
                        child_text = child.get_attribute('text')
                        child_desc = child.get_attribute('content-desc')
                        if child_text:
                            child_labels.append(child_text)
                        if child_desc:
                            child_labels.append(child_desc)
                except Exception:
                    pass
                combined_label = ' | '.join(filter(None, [el_text, el_desc] + child_labels))
                unique_key = (xpath, combined_label, el_class)
                interactive_info.append({'xpath': xpath, 'desc': el_desc, 'text': el_text, 'class': el_class, 'label': combined_label, 'unique_key': unique_key})
            except Exception:
                continue
        return interactive_info

    all_interactive = []
    unique_keys = set()
    last_page_source = None
    max_scrolls = 10
    scroll_count = 0
    current_xml = driver.page_source
    while scroll_count < max_scrolls:
        interactives = get_interactive_elements(current_xml)
        for c in interactives:
            if c['unique_key'] not in unique_keys:
                all_interactive.append(c)
                unique_keys.add(c['unique_key'])
        scrollables = driver.find_elements('xpath', '//*[@scrollable="true"]')
        if not scrollables:
            print(f"{log_prefix}  [SCROLL] No scrollable elements found. Stopping scroll.")
            break
        try:
            print(f"{log_prefix}  [SCROLL] Swiping to reveal more elements (scroll {scroll_count+1}/{max_scrolls})...")

            # Use safe scroll to prevent pull-to-refresh
            if not safe_scroll_down(driver, 1):
                print(f"{log_prefix}    Safe scroll failed, stopping")
                break

        except Exception as e:
            print(f"{log_prefix}    Could not swipe: {e}")
            break
        new_page_source = driver.page_source
        if new_page_source == last_page_source:
            print(f"{log_prefix}  [SCROLL] No new elements after scroll. Stopping scroll.")
            break
        last_page_source = new_page_source
        current_xml = new_page_source
        scroll_count += 1
    print(f"{log_prefix}  [SCROLL] Total unique interactive elements collected: {len(all_interactive)}")
    for c in all_interactive:
        print(f"{log_prefix}  [SCROLL] Element: text='{c['text']}', content_desc='{c['desc']}', class='{c['class']}', xpath='{c['xpath']}'")
    return all_interactive

def enhanced_main():
    """Enhanced main function using the new comprehensive crawling system"""

    # Initialize logging if not already done
    logger = None
    try:
        from logger_system import TerminalLogger, create_log_summary
        import atexit

        logger = TerminalLogger("analyze", "execution_logs")
        logger.start_logging()
        atexit.register(logger.stop_logging)
        atexit.register(create_log_summary)

    except ImportError:
        # logger_system not available, continue without logging
        pass
    except Exception as e:
        print(f"⚠️  Logging initialization failed: {e}")

    try:
        print("=" * 80)
        print("ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION")
        print("=" * 80)

        # Load configuration
        config = load_config()
        config['maximum_coverage'] = config.get('maximum_coverage', True)

        # Setup emulator and app
        print("[SETUP] Starting emulator...")
        start_emulator(config['emulator']['avd_name'], config['emulator']['headless'])

        print("[SETUP] Preparing APK...")

        # Always get APK path first (needed for installation)
        try:
            apk_path = get_apk_path(config['apk_folder'])
            print(f"[DEBUG] APK path: {apk_path}")
        except Exception as e:
            print(f"[ERROR] Failed to get APK path: {e}")
            raise

        # Option 1: Use known package name (faster)
        known_package = "com.kemendikdasmen.rumahpendidikan"
        use_known = input(f"Use known package '{known_package}'? [y/n]: ").strip().lower()

        if use_known == 'y':
            package = known_package
            print(f"[SETUP] Using known package: {package}")
        else:
            # Option 2: Detect from APK (slower, may hang)
            try:
                package = get_package_name(apk_path, config)
                print(f"[SETUP] Detected package: {package}")
            except Exception as e:
                print(f"[ERROR] Failed to detect package from APK: {e}")
                print(f"[FALLBACK] Using known package: {known_package}")
                package = known_package

        # Handle app installation according to user requirements
        print("[SETUP] Checking if app is installed on emulator...")

        # Check if app is installed (with timeout protection)
        app_installed = is_app_installed(package)

        if app_installed:
            print(f"✅ {package} is already installed on emulator.")
            if prompt_user(f"Continue with existing {package} or reinstall?"):
                print("[SETUP] Using existing app installation.")
                # Launch the app
                print(f"[SETUP] Launching {package}...")
                launch_app(package)
            else:
                print("[SETUP] Uninstalling existing app...")
                uninstall_app(package)
                print("[SETUP] Installing fresh app from APK...")
                install_app(apk_path)
                print(f"[SETUP] Launching {package}...")
                launch_app(package)
        else:
            print(f"❌ {package} is not installed on emulator.")
            print("[SETUP] Installing app from APK...")
            install_app(apk_path)
            print(f"[SETUP] Launching {package}...")
            launch_app(package)

        # Start Appium session
        print("[SETUP] Connecting to Appium...")

        # Check if Appium server is running
        if not is_appium_running():
            print("[SETUP] Appium server not running, starting it...")
            start_appium_server()

        driver = start_appium_session(package, config['wait_timeout'])

        if not driver:
            print("[FATAL] Could not establish Appium session")
            return

        try:
            # Initialize enhanced crawler
            print("[ENHANCED] Initializing comprehensive crawler...")
            crawler = EnhancedAppCrawler(driver, package, config)

            # Start comprehensive crawling
            print("[ENHANCED] Starting comprehensive crawling process...")
            crawl_results = crawler.start_comprehensive_crawl()

            # Save comprehensive results
            print("[ENHANCED] Saving comprehensive results...")
            os.makedirs(config['locators_folder'], exist_ok=True)

            # Save main results file
            results_path = os.path.join(config['locators_folder'], f"{package}_comprehensive.json")
            with open(results_path, 'w') as f:
                json.dump(crawl_results, f, indent=2)
            print(f"[ENHANCED] Saved comprehensive results to {results_path}")

            # Generate Gherkin scenarios
            print("[ENHANCED] Generating Gherkin test scenarios...")
            gherkin_engine = GherkinGenerationEngine(config)
            gherkin_content = gherkin_engine.generate_gherkin_scenarios(crawl_results, package)

            # Save Gherkin file
            os.makedirs(config.get('features_folder', './features'), exist_ok=True)
            gherkin_path = os.path.join(config.get('features_folder', './features'), f"{package}.feature")
            with open(gherkin_path, 'w') as f:
                f.write(gherkin_content)
            print(f"[ENHANCED] Saved Gherkin scenarios to {gherkin_path}")

            # Generate summary report
            summary = crawl_results.get('crawl_summary', {})
            print("\n" + "=" * 80)
            print("COMPREHENSIVE CRAWL SUMMARY")
            print("=" * 80)
            print(f"Total elements collected: {summary.get('total_elements_collected', 0)}")
            print(f"Total pages visited: {summary.get('total_pages_visited', 0)}")
            print(f"Crash recovery attempts: {summary.get('crash_count', 0)}")
            print(f"Crawl completed successfully: {summary.get('crawl_completed', False)}")
            print(f"Last successful action: {summary.get('last_successful_action', 'N/A')}")

            # Show navigation summary
            nav_summary = crawl_results.get('navigation_path', {})
            print(f"\nFinal navigation path: {' -> '.join(nav_summary.get('final_path', []))}")
            print(f"Maximum depth reached: {nav_summary.get('final_depth', 0)}")

            print("\n" + "=" * 80)
            print("ANALYSIS COMPLETE - Enhanced comprehensive crawling finished successfully!")
            print("=" * 80)

        finally:
            # Always quit driver
            try:
                driver.quit()
                print("[CLEANUP] Appium session closed")
            except:
                pass

    except WebDriverException as e:
        print(f"[FATAL] Appium session lost or UiAutomator2 crashed: {e}")
        print("[FATAL] This may be due to device/emulator issues. Check Appium server and device logs.")
    except Exception as e:
        print(f"[FATAL] Unexpected error in enhanced main: {e}")
        import traceback
        traceback.print_exc()

def scroll_until_bottom(driver, max_attempts=10):
    """Helper function for backward compatibility"""
    try:
        previous_source = driver.page_source
        attempt = 0
        while attempt < max_attempts:
            # Perform scroll
            size = driver.get_window_size()
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.8)
            end_y = int(size['height'] * 0.2)

            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1.5)

            # Check if content changed
            current_source = driver.page_source
            if current_source == previous_source:
                print(f"[SCROLL] Reached bottom after {attempt} attempts")
                return True

            previous_source = current_source
            attempt += 1

        print(f"[SCROLL] Max attempts ({max_attempts}) reached")
        return False
    except Exception as e:
        print(f"[SCROLL] Error in scroll_until_bottom: {e}")
        return False

def main():
    """Legacy main function - now calls enhanced version"""
    enhanced_main()

if __name__ == "__main__":
    main()