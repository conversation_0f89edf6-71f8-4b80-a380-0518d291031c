import subprocess
import time
import json
import yaml
import requests
import websocket
from appium import webdriver
from appium.options.android import <PERSON>iA<PERSON>mator2Options
import re
import xml.etree.ElementTree as ET
import hashlib
from appium.webdriver.webelement import WebElement
import datetime
from selenium.common.exceptions import WebD<PERSON>Exception, StaleElementReferenceException, NoSuchElementException
import os
from appium.webdriver.common.appiumby import AppiumBy
from collections import defaultdict
import re
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.common.exceptions import NoSuchElementException
from difflib import SequenceMatcher
from selenium.webdriver.common.by import By
import difflib
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import pickle
import threading
import queue
from selenium import webdriver as web_driver
from selenium.webdriver.chrome.options import Options as ChromeOptions
# _time import removed - using standard time module

CONFIG_PATH = os.path.join("config", "config.yaml")

# Enhanced State Management Classes
class PageContext(Enum):
    NATIVE = "native"
    WEBVIEW = "webview"
    CHROME_NATIVE = "chrome_native"
    UNKNOWN = "unknown"

class NavigationState(Enum):
    MAIN_PAGE = "main_page"
    MENU_LEVEL_1 = "menu_level_1"
    MENU_LEVEL_2 = "menu_level_2"
    MENU_LEVEL_3 = "menu_level_3"
    UNKNOWN_LEVEL = "unknown_level"

@dataclass
class ElementInfo:
    xpath: str
    text: str
    content_desc: str
    resource_id: str
    class_name: str
    clickable: bool
    visible: bool
    bounds: Dict[str, int]
    page_context: PageContext
    navigation_level: NavigationState
    collected_at: str

@dataclass
class PageState:
    page_name: str
    context: PageContext
    navigation_level: NavigationState
    elements: List[ElementInfo]
    fully_explored: bool
    scroll_position: int
    page_source_hash: str
    timestamp: str

@dataclass
class NavigationPath:
    path: List[str]
    current_depth: int
    current_page: str
    context_history: List[PageContext]

@dataclass
class CrawlState:
    navigation_path: NavigationPath
    visited_pages: Set[str]
    collected_elements: List[ElementInfo]
    page_states: Dict[str, PageState]
    current_menu_index: int
    current_submenu_index: int
    recovery_checkpoint: Optional[str]
    last_successful_action: str
    crash_count: int

class StateManager:
    def __init__(self, package_name: str, state_file: str = None):
        self.package_name = package_name
        self.state_file = state_file or f"state_{package_name}.pkl"
        self.state_lock = threading.Lock()
        self.crawl_state = CrawlState(
            navigation_path=NavigationPath([], 0, "Main Page", []),
            visited_pages=set(),
            collected_elements=[],
            page_states={},
            current_menu_index=0,
            current_submenu_index=0,
            recovery_checkpoint=None,
            last_successful_action="",
            crash_count=0
        )

    def save_state(self):
        """Save current state to disk for crash recovery"""
        with self.state_lock:
            try:
                with open(self.state_file, 'wb') as f:
                    pickle.dump(self.crawl_state, f)
                print(f"[STATE] Saved state to {self.state_file}")
            except Exception as e:
                print(f"[STATE] Error saving state: {e}")

    def load_state(self) -> bool:
        """Load state from disk if available"""
        if not os.path.exists(self.state_file):
            return False

        try:
            with open(self.state_file, 'rb') as f:
                self.crawl_state = pickle.load(f)
            print(f"[STATE] Loaded state from {self.state_file}")
            return True
        except Exception as e:
            print(f"[STATE] Error loading state: {e}")
            return False

    def update_navigation_path(self, page_name: str, context: PageContext):
        """Update current navigation path"""
        with self.state_lock:
            self.crawl_state.navigation_path.path.append(page_name)
            self.crawl_state.navigation_path.current_page = page_name
            self.crawl_state.navigation_path.context_history.append(context)
            self.crawl_state.navigation_path.current_depth = len(self.crawl_state.navigation_path.path) - 1

    def add_page_state(self, page_state: PageState):
        """Add or update page state"""
        with self.state_lock:
            self.crawl_state.page_states[page_state.page_name] = page_state
            self.crawl_state.visited_pages.add(page_state.page_name)

    def add_elements(self, elements: List[ElementInfo]):
        """Add collected elements to state"""
        with self.state_lock:
            self.crawl_state.collected_elements.extend(elements)

    def set_recovery_checkpoint(self, checkpoint: str):
        """Set recovery checkpoint for crash recovery"""
        with self.state_lock:
            self.crawl_state.recovery_checkpoint = checkpoint
            self.crawl_state.last_successful_action = checkpoint

    def increment_crash_count(self):
        """Increment crash counter"""
        with self.state_lock:
            self.crawl_state.crash_count += 1

    def get_current_state(self) -> CrawlState:
        """Get current crawl state"""
        with self.state_lock:
            return self.crawl_state

class SmartNavigationEngine:
    def __init__(self, driver, state_manager: StateManager, config: dict):
        self.driver = driver
        self.state_manager = state_manager
        self.config = config
        self.main_menus = [
            'Ruang GTK', 'Ruang Murid', 'Ruang Sekolah', 'Ruang Bahasa',
            'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua',
            'Sumber Belajar', 'Pusat Perbukuan', 'Pengelolaan Kinerja', 'Lihat Semua',
            'Butuh Bantuan?', 'Ruang', 'Pemberitahuan', 'Akun'
        ]

    def detect_page_context(self) -> PageContext:
        """Enhanced context detection"""
        try:
            package = self.driver.current_package
            contexts = self.driver.contexts
            curr_context = getattr(self.driver, 'current_context', None)

            if package == 'com.android.chrome':
                for ctx in contexts:
                    if 'WEBVIEW' in ctx or 'CHROMIUM' in ctx:
                        return PageContext.WEBVIEW
                return PageContext.CHROME_NATIVE
            elif curr_context and (curr_context.startswith('WEBVIEW') or curr_context.startswith('CHROMIUM')):
                return PageContext.WEBVIEW
            else:
                return PageContext.NATIVE
        except Exception as e:
            print(f"[CONTEXT] Error detecting context: {e}")
            return PageContext.UNKNOWN

    def determine_navigation_level(self, page_name: str) -> NavigationState:
        """Determine current navigation level based on page name and path"""
        if page_name == "Main Page" or page_name in self.main_menus:
            return NavigationState.MAIN_PAGE

        depth = self.state_manager.crawl_state.navigation_path.current_depth
        if depth == 1:
            return NavigationState.MENU_LEVEL_1
        elif depth == 2:
            return NavigationState.MENU_LEVEL_2
        elif depth == 3:
            return NavigationState.MENU_LEVEL_3
        else:
            return NavigationState.UNKNOWN_LEVEL

    def smart_back_navigation(self, target_context: PageContext):
        """Smart back navigation based on context"""
        try:
            current_context = self.detect_page_context()
            main_package = self.state_manager.package_name

            if current_context == PageContext.NATIVE:
                print("[NAV] Using native back navigation")
                self.driver.back()
            elif current_context in [PageContext.WEBVIEW, PageContext.CHROME_NATIVE]:
                print("[NAV] Switching back to main app from webview/chrome")
                self.driver.activate_app(main_package)
            else:
                print("[NAV] Unknown context, using default back")
                self.driver.back()

            time.sleep(1)
            return True
        except Exception as e:
            print(f"[NAV] Error in back navigation: {e}")
            return False

    def navigate_to_main_page(self) -> bool:
        """Navigate back to main page"""
        try:
            main_package = self.state_manager.package_name
            self.driver.activate_app(main_package)
            time.sleep(2)

            # Update state
            self.state_manager.crawl_state.navigation_path = NavigationPath(
                ["Main Page"], 0, "Main Page", [self.detect_page_context()]
            )
            return True
        except Exception as e:
            print(f"[NAV] Error navigating to main page: {e}")
            return False

class ComprehensiveElementCollector:
    def __init__(self, driver, state_manager: StateManager, config: dict):
        self.driver = driver
        self.state_manager = state_manager
        self.config = config
        self.actionable_labels = [
            # Main Page
            'Pengelolaan Kinerja', 'Lihat Semua', 'Butuh Bantuan?',
            # Ruang GTK
            'Pengelolaan Kinerja\nPerencanaan, pelaksanaan, dan penilaian kinerja Anda untuk pengembangan diri dan satdik',
            'Seleksi Kepala Sekolah', 'Refleksi Kompetensi', 'Perangkat Ajar',
            'CP/ATP', 'Ide Praktik', 'Bukti Karya', 'Video Inspirasi'
            # Add more as needed
        ]

    def scroll_to_bottom_step_by_step(self, max_scrolls: int = 20) -> List[ElementInfo]:
        """Scroll through page step by step collecting all elements"""
        collected_elements = []
        seen_elements = set()
        scroll_count = 0
        last_page_source = None

        print(f"[COLLECT] Starting step-by-step scroll collection (max {max_scrolls} scrolls)")

        while scroll_count < max_scrolls:
            try:
                # Collect elements at current scroll position
                current_elements = self._collect_visible_elements()

                # Add new elements
                for element in current_elements:
                    element_key = f"{element.xpath}_{element.text}_{element.content_desc}"
                    if element_key not in seen_elements:
                        collected_elements.append(element)
                        seen_elements.add(element_key)

                # Check if we've reached the bottom
                current_page_source = self.driver.page_source
                if last_page_source and self._is_same_content(current_page_source, last_page_source):
                    print(f"[COLLECT] Reached bottom of page after {scroll_count} scrolls")
                    break

                last_page_source = current_page_source

                # Perform scroll
                if not self._perform_smart_scroll():
                    print("[COLLECT] Could not scroll further")
                    break

                scroll_count += 1
                time.sleep(1.5)  # Wait for content to load

            except Exception as e:
                print(f"[COLLECT] Error during scroll collection: {e}")
                break

        print(f"[COLLECT] Collected {len(collected_elements)} elements after {scroll_count} scrolls")
        return collected_elements

    def _collect_visible_elements(self) -> List[ElementInfo]:
        """Collect all visible elements on current screen"""
        elements = []
        try:
            xml = self.driver.page_source
            tree = ET.ElementTree(ET.fromstring(xml))
            root = tree.getroot()

            current_context = self._get_current_context()
            current_level = self._get_current_navigation_level()
            timestamp = datetime.datetime.now().isoformat()

            def recurse_elements(node, path):
                parent = node.getparent() if hasattr(node, 'getparent') else None
                if parent is not None:
                    same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                    idx = same_tag_siblings.index(node) + 1
                else:
                    idx = 1

                this_xpath = f"{path}/{node.tag}[{idx}]"
                attrs = node.attrib.copy()

                text = attrs.get('text', node.text or "")
                content_desc = attrs.get('content-desc', "")
                resource_id = attrs.get('resource-id', "")
                class_name = attrs.get('class', node.tag)
                clickable = attrs.get('clickable', 'false') == 'true'

                # Check if element has meaningful content
                if (text and text.strip()) or (content_desc and content_desc.strip()) or clickable:
                    try:
                        # Try to find element and check visibility
                        element = self.driver.find_element('xpath', this_xpath)
                        visible = element.is_displayed()
                        bounds = element.rect

                        element_info = ElementInfo(
                            xpath=this_xpath,
                            text=text,
                            content_desc=content_desc,
                            resource_id=resource_id,
                            class_name=class_name,
                            clickable=clickable,
                            visible=visible,
                            bounds=bounds,
                            page_context=current_context,
                            navigation_level=current_level,
                            collected_at=timestamp
                        )
                        elements.append(element_info)

                    except Exception:
                        # Element not found or not accessible, create info anyway
                        element_info = ElementInfo(
                            xpath=this_xpath,
                            text=text,
                            content_desc=content_desc,
                            resource_id=resource_id,
                            class_name=class_name,
                            clickable=clickable,
                            visible=False,
                            bounds={},
                            page_context=current_context,
                            navigation_level=current_level,
                            collected_at=timestamp
                        )
                        elements.append(element_info)

                # Recurse into children
                for child in list(node):
                    recurse_elements(child, this_xpath)

            recurse_elements(root, "")

        except Exception as e:
            print(f"[COLLECT] Error collecting elements: {e}")

        return elements

    def _get_current_context(self) -> PageContext:
        """Get current page context"""
        try:
            package = self.driver.current_package
            contexts = self.driver.contexts

            if package == 'com.android.chrome':
                return PageContext.CHROME_NATIVE
            elif any('WEBVIEW' in ctx or 'CHROMIUM' in ctx for ctx in contexts):
                return PageContext.WEBVIEW
            else:
                return PageContext.NATIVE
        except:
            return PageContext.UNKNOWN

    def _get_current_navigation_level(self) -> NavigationState:
        """Get current navigation level"""
        depth = self.state_manager.crawl_state.navigation_path.current_depth
        if depth == 0:
            return NavigationState.MAIN_PAGE
        elif depth == 1:
            return NavigationState.MENU_LEVEL_1
        elif depth == 2:
            return NavigationState.MENU_LEVEL_2
        elif depth == 3:
            return NavigationState.MENU_LEVEL_3
        else:
            return NavigationState.UNKNOWN_LEVEL

    def _perform_smart_scroll(self) -> bool:
        """Perform smart scrolling"""
        try:
            # Try to find scrollable container
            scrollable = None
            for class_name in ['ScrollView', 'RecyclerView', 'android.widget.ScrollView', 'androidx.recyclerview.widget.RecyclerView']:
                try:
                    scrollable = self.driver.find_element('xpath', f"//*[contains(@class, '{class_name}')]")
                    break
                except:
                    continue

            if scrollable:
                loc = scrollable.location
                size = scrollable.size
                start_x = loc['x'] + size['width'] // 2
                start_y = loc['y'] + int(size['height'] * 0.8)
                end_y = loc['y'] + int(size['height'] * 0.2)

                self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                return True
            else:
                # Fallback to screen swipe
                size = self.driver.get_window_size()
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.8)
                end_y = int(size['height'] * 0.2)

                self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                return True

        except Exception as e:
            print(f"[SCROLL] Error performing scroll: {e}")
            return False

    def _is_same_content(self, source1: str, source2: str) -> bool:
        """Check if two page sources represent the same content"""
        try:
            # Simple hash comparison
            hash1 = hashlib.md5(source1.encode()).hexdigest()
            hash2 = hashlib.md5(source2.encode()).hexdigest()
            return hash1 == hash2
        except:
            return False

    def collect_actionable_elements(self) -> List[ElementInfo]:
        """Collect elements matching actionable labels"""
        actionable_elements = []

        try:
            all_elements = self._collect_visible_elements()

            for element in all_elements:
                if self._is_actionable_element(element):
                    actionable_elements.append(element)

        except Exception as e:
            print(f"[COLLECT] Error collecting actionable elements: {e}")

        return actionable_elements

    def _is_actionable_element(self, element: ElementInfo) -> bool:
        """Check if element matches actionable criteria"""
        # Check if element text or content_desc matches actionable labels
        element_text = element.text.lower().strip()
        element_desc = element.content_desc.lower().strip()

        for label in self.actionable_labels:
            label_norm = label.lower().strip()
            if (label_norm in element_text or element_text in label_norm or
                label_norm in element_desc or element_desc in label_norm):
                return True

        # Also consider clickable elements with meaningful text
        if element.clickable and (element.text.strip() or element.content_desc.strip()):
            return True

        return False

class CrashRecoverySystem:
    def __init__(self, state_manager: StateManager, config: dict):
        self.state_manager = state_manager
        self.config = config
        self.max_retries = 3

    def restart_appium_session(self, package_name: str) -> Optional[webdriver.Remote]:
        """Restart Appium session after crash"""
        try:
            print("[RECOVERY] Restarting Appium session...")

            # Increment crash count
            self.state_manager.increment_crash_count()

            # Create new driver
            driver = start_appium_session(package_name, self.config['wait_timeout'])

            if driver:
                print("[RECOVERY] Appium session restarted successfully")
                return driver
            else:
                print("[RECOVERY] Failed to restart Appium session")
                return None

        except Exception as e:
            print(f"[RECOVERY] Error restarting Appium session: {e}")
            return None

    def replay_navigation_path(self, driver, navigation_path: List[str]) -> bool:
        """Replay navigation path to restore state"""
        try:
            print(f"[RECOVERY] Replaying navigation path: {navigation_path}")

            # Start from main page
            main_package = self.state_manager.package_name
            driver.activate_app(main_package)
            time.sleep(2)

            # Navigate through each step in the path (skip main page)
            for step_name in navigation_path[1:]:
                print(f"[RECOVERY] Navigating to: {step_name}")

                # Try to find element by content-desc first
                element = None
                try:
                    element = driver.find_element("xpath", f"//*[@content-desc='{step_name}']")
                except:
                    pass

                # Try by text if content-desc failed
                if not element:
                    try:
                        element = driver.find_element("xpath", f"//*[@text='{step_name}']")
                    except:
                        pass

                # Try partial matching
                if not element:
                    try:
                        element = driver.find_element("xpath", f"//*[contains(@content-desc, '{step_name}')]")
                    except:
                        pass

                if not element:
                    try:
                        element = driver.find_element("xpath", f"//*[contains(@text, '{step_name}')]")
                    except:
                        pass

                if element:
                    element.click()
                    time.sleep(2)
                    print(f"[RECOVERY] Successfully navigated to: {step_name}")
                else:
                    print(f"[RECOVERY] Could not find element for: {step_name}")
                    return False

            print("[RECOVERY] Navigation path replay completed successfully")
            return True

        except Exception as e:
            print(f"[RECOVERY] Error replaying navigation path: {e}")
            return False

    def attempt_recovery(self, package_name: str) -> Optional[webdriver.Remote]:
        """Attempt full crash recovery"""
        try:
            crash_count = self.state_manager.crawl_state.crash_count

            if crash_count >= self.max_retries:
                print(f"[RECOVERY] Max retries ({self.max_retries}) reached. Giving up.")
                return None

            print(f"[RECOVERY] Attempting recovery (attempt {crash_count + 1}/{self.max_retries})")

            # Restart Appium session
            driver = self.restart_appium_session(package_name)
            if not driver:
                return None

            # Replay navigation path
            navigation_path = self.state_manager.crawl_state.navigation_path.path
            if len(navigation_path) > 1:  # More than just main page
                success = self.replay_navigation_path(driver, navigation_path)
                if not success:
                    print("[RECOVERY] Failed to replay navigation path")
                    driver.quit()
                    return None

            # Update state with successful recovery
            self.state_manager.set_recovery_checkpoint("Recovery completed successfully")
            self.state_manager.save_state()

            return driver

        except Exception as e:
            print(f"[RECOVERY] Error during recovery attempt: {e}")
            return None

class SmartMenuDetector:
    def __init__(self, driver, state_manager: StateManager):
        self.driver = driver
        self.state_manager = state_manager

    def detect_remaining_menus(self, current_level: NavigationState) -> List[str]:
        """Detect remaining unvisited menus at current level"""
        try:
            # Get all clickable elements
            clickable_elements = self.driver.find_elements("xpath", "//*[@clickable='true']")

            menu_candidates = []
            for element in clickable_elements:
                try:
                    text = element.get_attribute('text') or ''
                    content_desc = element.get_attribute('content-desc') or ''

                    # Skip if already visited
                    element_name = text or content_desc
                    if element_name and element_name not in self.state_manager.crawl_state.visited_pages:
                        # Check if it looks like a menu item
                        if self._is_menu_like(element_name, current_level):
                            menu_candidates.append(element_name)

                except Exception:
                    continue

            return menu_candidates

        except Exception as e:
            print(f"[MENU_DETECT] Error detecting menus: {e}")
            return []

    def _is_menu_like(self, element_name: str, level: NavigationState) -> bool:
        """Check if element looks like a menu item"""
        # Basic heuristics for menu detection
        if not element_name or len(element_name.strip()) < 2:
            return False

        # Skip common UI elements
        skip_terms = ['back', 'close', 'cancel', 'ok', 'yes', 'no', 'search', 'filter']
        if any(term in element_name.lower() for term in skip_terms):
            return False

        # For main page, check against known main menus
        if level == NavigationState.MAIN_PAGE:
            main_menus = [
                'Ruang GTK', 'Ruang Murid', 'Ruang Sekolah', 'Ruang Bahasa',
                'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua',
                'Sumber Belajar', 'Pusat Perbukuan', 'Pengelolaan Kinerja', 'Lihat Semua',
                'Butuh Bantuan?', 'Ruang', 'Pemberitahuan', 'Akun'
            ]
            return any(menu in element_name for menu in main_menus)

        # For other levels, use general heuristics
        return len(element_name.strip()) > 3 and not element_name.isdigit()

    def is_page_fully_explored(self, page_name: str) -> bool:
        """Check if current page has been fully explored"""
        try:
            # Check if page state exists and is marked as fully explored
            page_state = self.state_manager.crawl_state.page_states.get(page_name)
            if page_state:
                return page_state.fully_explored

            # If no state exists, consider it not explored
            return False

        except Exception as e:
            print(f"[MENU_DETECT] Error checking exploration status: {e}")
            return False

class EnhancedAppCrawler:
    def __init__(self, driver, package_name: str, config: dict):
        self.driver = driver
        self.package_name = package_name
        self.config = config
        self.state_manager = StateManager(package_name)
        self.navigator = SmartNavigationEngine(driver, self.state_manager, config)
        self.collector = ComprehensiveElementCollector(driver, self.state_manager, config)
        self.recovery_system = CrashRecoverySystem(self.state_manager, config)
        self.menu_detector = SmartMenuDetector(driver, self.state_manager)

    def start_comprehensive_crawl(self) -> Dict[str, Any]:
        """Start the comprehensive crawling process"""
        try:
            print("[ENHANCED_CRAWL] Starting comprehensive app crawling...")

            # Try to load previous state
            if self.state_manager.load_state():
                print("[ENHANCED_CRAWL] Resuming from previous state...")
                return self._resume_crawl()
            else:
                print("[ENHANCED_CRAWL] Starting fresh crawl...")
                return self._start_fresh_crawl()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in comprehensive crawl: {e}")
            return self._handle_crawl_error(e)

    def _start_fresh_crawl(self) -> Dict[str, Any]:
        """Start crawling from the beginning"""
        try:
            # Initialize state
            self.state_manager.update_navigation_path("Main Page", PageContext.NATIVE)
            self.state_manager.set_recovery_checkpoint("Starting fresh crawl")

            # Step 1: Collect all elements on main page
            print("[ENHANCED_CRAWL] Step 1: Collecting all elements on main page...")
            main_page_elements = self._collect_complete_page("Main Page")

            # Step 2: Navigate through all main menus
            print("[ENHANCED_CRAWL] Step 2: Navigating through main menus...")
            self._crawl_all_main_menus()

            # Step 3: Generate final results
            return self._generate_final_results()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in fresh crawl: {e}")
            return self._attempt_recovery_and_continue(e)

    def _resume_crawl(self) -> Dict[str, Any]:
        """Resume crawling from saved state"""
        try:
            print("[ENHANCED_CRAWL] Resuming crawl from saved state...")

            # Attempt to restore session and navigation state
            current_path = self.state_manager.crawl_state.navigation_path.path

            if len(current_path) > 1:
                # Try to navigate back to where we were
                success = self.recovery_system.replay_navigation_path(self.driver, current_path)
                if not success:
                    print("[ENHANCED_CRAWL] Could not restore navigation state, starting fresh...")
                    return self._start_fresh_crawl()

            # Continue from where we left off
            return self._continue_crawl_from_current_state()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error resuming crawl: {e}")
            return self._attempt_recovery_and_continue(e)

    def _collect_complete_page(self, page_name: str) -> List[ElementInfo]:
        """Collect all elements on a page by scrolling to bottom"""
        try:
            print(f"[ENHANCED_CRAWL] Collecting complete page: {page_name}")

            # Scroll to top first
            self._scroll_to_top()

            # Collect all elements by scrolling step by step
            all_elements = self.collector.scroll_to_bottom_step_by_step()

            # Also collect actionable elements specifically
            actionable_elements = self.collector.collect_actionable_elements()

            # Combine and deduplicate
            combined_elements = self._deduplicate_elements(all_elements + actionable_elements)

            # Create page state
            page_context = self.navigator.detect_page_context()
            nav_level = self.navigator.determine_navigation_level(page_name)

            page_state = PageState(
                page_name=page_name,
                context=page_context,
                navigation_level=nav_level,
                elements=combined_elements,
                fully_explored=True,
                scroll_position=0,
                page_source_hash=hashlib.md5(self.driver.page_source.encode()).hexdigest(),
                timestamp=datetime.datetime.now().isoformat()
            )

            # Save state
            self.state_manager.add_page_state(page_state)
            self.state_manager.add_elements(combined_elements)
            self.state_manager.save_state()

            print(f"[ENHANCED_CRAWL] Collected {len(combined_elements)} elements from {page_name}")
            return combined_elements

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error collecting page {page_name}: {e}")
            return []

    def _crawl_all_main_menus(self):
        """Crawl through all main menus systematically"""
        try:
            main_menus = self.navigator.main_menus

            for menu_index, menu_name in enumerate(main_menus):
                try:
                    print(f"[ENHANCED_CRAWL] Processing main menu {menu_index + 1}/{len(main_menus)}: {menu_name}")

                    # Update state
                    self.state_manager.crawl_state.current_menu_index = menu_index
                    self.state_manager.set_recovery_checkpoint(f"Processing main menu: {menu_name}")

                    # Navigate to main page first
                    if not self.navigator.navigate_to_main_page():
                        print(f"[ENHANCED_CRAWL] Could not navigate to main page for menu: {menu_name}")
                        continue

                    # Try to click the menu
                    if self._click_menu_item(menu_name):
                        # Collect elements on the menu page
                        menu_elements = self._collect_complete_page(menu_name)

                        # Crawl submenus recursively
                        self._crawl_submenus(menu_name, 1)

                    else:
                        print(f"[ENHANCED_CRAWL] Could not click menu: {menu_name}")

                except Exception as e:
                    print(f"[ENHANCED_CRAWL] Error processing menu {menu_name}: {e}")
                    # Try recovery
                    recovered_driver = self.recovery_system.attempt_recovery(self.package_name)
                    if recovered_driver:
                        self.driver = recovered_driver
                        continue
                    else:
                        print(f"[ENHANCED_CRAWL] Could not recover from error in menu {menu_name}")
                        break

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in main menu crawling: {e}")

    def _crawl_submenus(self, parent_menu: str, depth: int, max_depth: int = 3):
        """Recursively crawl submenus"""
        if depth > max_depth:
            print(f"[ENHANCED_CRAWL] Max depth {max_depth} reached for {parent_menu}")
            return

        try:
            print(f"[ENHANCED_CRAWL] Crawling submenus of {parent_menu} at depth {depth}")

            # Detect available submenus
            current_level = NavigationState.MENU_LEVEL_1 if depth == 1 else NavigationState.MENU_LEVEL_2 if depth == 2 else NavigationState.MENU_LEVEL_3
            remaining_menus = self.menu_detector.detect_remaining_menus(current_level)

            for submenu_index, submenu_name in enumerate(remaining_menus):
                try:
                    print(f"[ENHANCED_CRAWL] Processing submenu {submenu_index + 1}/{len(remaining_menus)}: {submenu_name}")

                    # Update state
                    self.state_manager.crawl_state.current_submenu_index = submenu_index
                    self.state_manager.set_recovery_checkpoint(f"Processing submenu: {submenu_name} at depth {depth}")

                    # Click submenu
                    if self._click_menu_item(submenu_name):
                        # Update navigation path
                        context = self.navigator.detect_page_context()
                        self.state_manager.update_navigation_path(submenu_name, context)

                        # Collect elements
                        submenu_elements = self._collect_complete_page(submenu_name)

                        # Recursively crawl deeper submenus
                        self._crawl_submenus(submenu_name, depth + 1, max_depth)

                        # Navigate back
                        self.navigator.smart_back_navigation(context)
                        time.sleep(2)

                    else:
                        print(f"[ENHANCED_CRAWL] Could not click submenu: {submenu_name}")

                except Exception as e:
                    print(f"[ENHANCED_CRAWL] Error processing submenu {submenu_name}: {e}")
                    continue

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error in submenu crawling: {e}")

    def _click_menu_item(self, menu_name: str) -> bool:
        """Try to click a menu item by various methods"""
        try:
            # Method 1: By content-desc
            try:
                element = self.driver.find_element("xpath", f"//*[@content-desc='{menu_name}']")
                element.click()
                time.sleep(2)
                return True
            except:
                pass

            # Method 2: By text
            try:
                element = self.driver.find_element("xpath", f"//*[@text='{menu_name}']")
                element.click()
                time.sleep(2)
                return True
            except:
                pass

            # Method 3: Partial matching
            try:
                element = self.driver.find_element("xpath", f"//*[contains(@content-desc, '{menu_name}')]")
                element.click()
                time.sleep(2)
                return True
            except:
                pass

            # Method 4: Partial text matching
            try:
                element = self.driver.find_element("xpath", f"//*[contains(@text, '{menu_name}')]")
                element.click()
                time.sleep(2)
                return True
            except:
                pass

            print(f"[ENHANCED_CRAWL] Could not find clickable element for: {menu_name}")
            return False

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error clicking menu item {menu_name}: {e}")
            return False

    def _scroll_to_top(self):
        """Scroll to top of the page"""
        try:
            for _ in range(5):  # Multiple scrolls to ensure we reach top
                size = self.driver.get_window_size()
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.2)
                end_y = int(size['height'] * 0.8)

                self.driver.swipe(start_x, start_y, start_x, end_y, 1000)
                time.sleep(0.5)
        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error scrolling to top: {e}")

    def _deduplicate_elements(self, elements: List[ElementInfo]) -> List[ElementInfo]:
        """Remove duplicate elements"""
        seen = set()
        unique_elements = []

        for element in elements:
            # Create unique key based on xpath, text, and content_desc
            key = f"{element.xpath}_{element.text}_{element.content_desc}"
            if key not in seen:
                seen.add(key)
                unique_elements.append(element)

        return unique_elements

    def _continue_crawl_from_current_state(self) -> Dict[str, Any]:
        """Continue crawling from current state"""
        try:
            # Get current state
            current_state = self.state_manager.get_current_state()

            # Continue from current menu index
            main_menus = self.navigator.main_menus
            start_index = current_state.current_menu_index

            # Continue crawling remaining menus
            for menu_index in range(start_index, len(main_menus)):
                menu_name = main_menus[menu_index]
                print(f"[ENHANCED_CRAWL] Continuing with menu {menu_index + 1}/{len(main_menus)}: {menu_name}")

                # Process menu (similar to _crawl_all_main_menus)
                self.state_manager.crawl_state.current_menu_index = menu_index
                self.state_manager.set_recovery_checkpoint(f"Continuing with menu: {menu_name}")

                if not self.navigator.navigate_to_main_page():
                    continue

                if self._click_menu_item(menu_name):
                    menu_elements = self._collect_complete_page(menu_name)
                    self._crawl_submenus(menu_name, 1)

            return self._generate_final_results()

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error continuing crawl: {e}")
            return self._attempt_recovery_and_continue(e)

    def _attempt_recovery_and_continue(self, error: Exception) -> Dict[str, Any]:
        """Attempt recovery after error and continue"""
        try:
            print(f"[ENHANCED_CRAWL] Attempting recovery after error: {error}")

            # Try to recover
            recovered_driver = self.recovery_system.attempt_recovery(self.package_name)

            if recovered_driver:
                self.driver = recovered_driver
                print("[ENHANCED_CRAWL] Recovery successful, continuing crawl...")
                return self._continue_crawl_from_current_state()
            else:
                print("[ENHANCED_CRAWL] Recovery failed, generating results with collected data...")
                return self._generate_final_results()

        except Exception as recovery_error:
            print(f"[ENHANCED_CRAWL] Recovery attempt failed: {recovery_error}")
            return self._generate_final_results()

    def _handle_crawl_error(self, error: Exception) -> Dict[str, Any]:
        """Handle crawl error and return results"""
        print(f"[ENHANCED_CRAWL] Handling crawl error: {error}")
        return self._generate_final_results()

    def _generate_final_results(self) -> Dict[str, Any]:
        """Generate final crawl results"""
        try:
            current_state = self.state_manager.get_current_state()

            # Convert ElementInfo objects to dictionaries for JSON serialization
            elements_dict = []
            for element in current_state.collected_elements:
                element_dict = asdict(element)
                # Convert enums to strings
                element_dict['page_context'] = element.page_context.value
                element_dict['navigation_level'] = element.navigation_level.value
                elements_dict.append(element_dict)

            # Convert PageState objects to dictionaries
            page_states_dict = {}
            for page_name, page_state in current_state.page_states.items():
                page_dict = asdict(page_state)
                page_dict['context'] = page_state.context.value
                page_dict['navigation_level'] = page_state.navigation_level.value
                # Convert elements in page state
                page_dict['elements'] = []
                for element in page_state.elements:
                    elem_dict = asdict(element)
                    elem_dict['page_context'] = element.page_context.value
                    elem_dict['navigation_level'] = element.navigation_level.value
                    page_dict['elements'].append(elem_dict)
                page_states_dict[page_name] = page_dict

            results = {
                "crawl_summary": {
                    "total_elements_collected": len(current_state.collected_elements),
                    "total_pages_visited": len(current_state.visited_pages),
                    "crash_count": current_state.crash_count,
                    "last_successful_action": current_state.last_successful_action,
                    "crawl_completed": True
                },
                "navigation_path": {
                    "final_path": current_state.navigation_path.path,
                    "final_depth": current_state.navigation_path.current_depth,
                    "context_history": [ctx.value for ctx in current_state.navigation_path.context_history]
                },
                "collected_elements": elements_dict,
                "page_states": page_states_dict,
                "visited_pages": list(current_state.visited_pages)
            }

            print(f"[ENHANCED_CRAWL] Generated final results: {results['crawl_summary']}")
            return results

        except Exception as e:
            print(f"[ENHANCED_CRAWL] Error generating final results: {e}")
            return {
                "crawl_summary": {
                    "total_elements_collected": 0,
                    "total_pages_visited": 0,
                    "crash_count": 0,
                    "last_successful_action": "Error generating results",
                    "crawl_completed": False
                },
                "error": str(e)
            }

class GherkinGenerationEngine:
    def __init__(self, config: dict):
        self.config = config
        self.ai_model = config.get('ai_model', 'phi3:medium')

    def generate_gherkin_scenarios(self, crawl_results: Dict[str, Any], package_name: str) -> str:
        """Generate comprehensive Gherkin scenarios from crawl results"""
        try:
            print("[GHERKIN] Starting Gherkin scenario generation...")

            # Prepare data for AI processing
            elements_summary = self._prepare_elements_summary(crawl_results)
            navigation_summary = self._prepare_navigation_summary(crawl_results)

            # Generate scenarios using AI
            gherkin_content = self._generate_ai_scenarios(elements_summary, navigation_summary, package_name)

            # Post-process and validate
            final_gherkin = self._post_process_gherkin(gherkin_content, package_name)

            print("[GHERKIN] Gherkin generation completed")
            return final_gherkin

        except Exception as e:
            print(f"[GHERKIN] Error generating Gherkin scenarios: {e}")
            return self._generate_fallback_gherkin(crawl_results, package_name)

    def _prepare_elements_summary(self, crawl_results: Dict[str, Any]) -> str:
        """Prepare a summary of collected elements for AI processing"""
        try:
            elements = crawl_results.get('collected_elements', [])

            # Group elements by page/navigation level
            grouped_elements = {}
            for element in elements:
                nav_level = element.get('navigation_level', 'unknown')
                if nav_level not in grouped_elements:
                    grouped_elements[nav_level] = []

                # Create meaningful element description
                element_desc = {
                    'text': element.get('text', ''),
                    'content_desc': element.get('content_desc', ''),
                    'clickable': element.get('clickable', False),
                    'class_name': element.get('class_name', ''),
                    'page_context': element.get('page_context', 'unknown')
                }
                grouped_elements[nav_level].append(element_desc)

            # Create summary text
            summary_parts = []
            for level, elements_list in grouped_elements.items():
                summary_parts.append(f"\n{level.upper()} ELEMENTS:")

                # Focus on clickable and meaningful elements
                meaningful_elements = [
                    e for e in elements_list
                    if (e['clickable'] or e['text'].strip() or e['content_desc'].strip())
                ]

                for element in meaningful_elements[:20]:  # Limit to avoid token overflow
                    text = element['text'] or element['content_desc'] or 'Unnamed element'
                    context = element['page_context']
                    clickable = "clickable" if element['clickable'] else "non-clickable"
                    summary_parts.append(f"  - {text} ({clickable}, {context})")

            return '\n'.join(summary_parts)

        except Exception as e:
            print(f"[GHERKIN] Error preparing elements summary: {e}")
            return "Error preparing elements summary"

    def _prepare_navigation_summary(self, crawl_results: Dict[str, Any]) -> str:
        """Prepare navigation flow summary"""
        try:
            navigation = crawl_results.get('navigation_path', {})
            page_states = crawl_results.get('page_states', {})

            summary_parts = [
                "NAVIGATION FLOW:",
                f"Final navigation path: {' -> '.join(navigation.get('final_path', []))}",
                f"Maximum depth reached: {navigation.get('final_depth', 0)}",
                f"Total pages visited: {len(crawl_results.get('visited_pages', []))}",
                "\nPAGE DETAILS:"
            ]

            for page_name, page_info in page_states.items():
                elements_count = len(page_info.get('elements', []))
                context = page_info.get('context', 'unknown')
                summary_parts.append(f"  - {page_name}: {elements_count} elements ({context})")

            return '\n'.join(summary_parts)

        except Exception as e:
            print(f"[GHERKIN] Error preparing navigation summary: {e}")
            return "Error preparing navigation summary"

    def _generate_ai_scenarios(self, elements_summary: str, navigation_summary: str, package_name: str) -> str:
        """Use AI to generate human-readable Gherkin scenarios"""
        try:
            prompt = f"""
You are an expert QA engineer creating comprehensive Gherkin test scenarios for an Android application.

APPLICATION: {package_name}

COLLECTED UI ELEMENTS:
{elements_summary}

NAVIGATION STRUCTURE:
{navigation_summary}

TASK: Create comprehensive, human-readable Gherkin scenarios that cover:
1. Main navigation flows through all discovered menus and submenus
2. Element interaction scenarios for clickable components
3. Content verification scenarios for important text/information
4. Cross-platform scenarios (native, webview, chrome contexts)
5. Error handling and edge cases

REQUIREMENTS:
- Use natural, human-readable language in scenario descriptions
- Create realistic user journeys that make business sense
- Include proper Given/When/Then structure
- Add meaningful scenario names and descriptions
- Group related scenarios under appropriate features
- Include data validation where applicable
- Consider different user personas and use cases

EXAMPLE FORMAT:
Feature: Main Navigation
  As a user of the application
  I want to navigate through different sections
  So that I can access all available features

  Scenario: Navigate to Teacher Workspace
    Given I am on the main page
    When I tap on "Ruang GTK" menu
    Then I should see the teacher workspace page
    And I should see performance management options

Generate comprehensive scenarios covering all discovered elements and navigation paths.
Focus on creating meaningful, business-relevant test cases that a human tester would understand and execute.
"""

            # Query AI model
            gherkin_content = query_ollama(prompt, self.ai_model)
            return gherkin_content

        except Exception as e:
            print(f"[GHERKIN] Error generating AI scenarios: {e}")
            return self._generate_basic_scenarios(elements_summary, navigation_summary, package_name)

    def _generate_basic_scenarios(self, elements_summary: str, navigation_summary: str, package_name: str) -> str:
        """Generate basic scenarios without AI"""
        try:
            basic_gherkin = f"""Feature: {package_name} Application Testing
  As a user of the {package_name} application
  I want to interact with all available features
  So that I can verify the application works correctly

  Scenario: Launch Application
    Given the application is installed
    When I launch the application
    Then I should see the main page
    And all main menu items should be visible

  Scenario: Navigate Main Menus
    Given I am on the main page
    When I tap on each main menu item
    Then I should navigate to the corresponding section
    And I should be able to return to the main page

  Scenario: Verify Page Content
    Given I navigate to different pages
    When I scroll through the content
    Then all text and interactive elements should be visible
    And clickable elements should respond to taps

  Scenario: Test Navigation Flow
    Given I am on any page
    When I use back navigation
    Then I should return to the previous page
    And the navigation state should be consistent
"""
            return basic_gherkin

        except Exception as e:
            print(f"[GHERKIN] Error generating basic scenarios: {e}")
            return f"# Error generating scenarios for {package_name}"

    def _post_process_gherkin(self, gherkin_content: str, package_name: str) -> str:
        """Post-process and validate Gherkin content"""
        try:
            # Basic validation and cleanup
            lines = gherkin_content.split('\n')
            processed_lines = []

            for line in lines:
                # Clean up common AI artifacts
                line = line.strip()
                if line and not line.startswith('#'):
                    processed_lines.append(line)

            # Ensure proper Gherkin structure
            if not any(line.startswith('Feature:') for line in processed_lines):
                processed_lines.insert(0, f"Feature: {package_name} Application Testing")

            return '\n'.join(processed_lines)

        except Exception as e:
            print(f"[GHERKIN] Error post-processing Gherkin: {e}")
            return gherkin_content

    def _generate_fallback_gherkin(self, crawl_results: Dict[str, Any], package_name: str) -> str:
        """Generate fallback Gherkin when AI fails"""
        return f"""Feature: {package_name} Basic Testing
  As a tester
  I want to verify basic application functionality
  So that I can ensure the app works correctly

  Scenario: Application Launch
    Given the application is installed
    When I launch the application
    Then the application should start successfully

  Scenario: Basic Navigation
    Given I am on the main page
    When I interact with available elements
    Then the application should respond appropriately
"""

# Load config
def load_config():
    with open(CONFIG_PATH, 'r') as f:
        return yaml.safe_load(f)

def start_emulator(avd_name, headless=True):
    # Check if emulator is already running
    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
    if 'emulator-' in result.stdout:
        print("Emulator already running.")
        return
    headless_flag = ['-no-window'] if headless else []
    subprocess.Popen(['emulator', '-avd', avd_name] + headless_flag)
    print(f"Starting emulator {avd_name} (headless={headless})...")
    # Wait for device
    while True:
        result = subprocess.run(['adb', 'shell', 'getprop', 'sys.boot_completed'], capture_output=True, text=True)
        if '1' in result.stdout:
            print("Emulator booted.")
            break
        print("Waiting for emulator to boot...")
        time.sleep(5)

def get_apk_path(apk_folder):
    apks = [f for f in os.listdir(apk_folder) if f.endswith('.apk')]
    if not apks:
        raise FileNotFoundError("No APK found in folder: " + apk_folder)
    return os.path.join(apk_folder, apks[0])

def get_package_name(apk_path, config):
    aapt_path = config.get('aapt_path', 'aapt')
    result = subprocess.run([aapt_path, 'dump', 'badging', apk_path], capture_output=True, text=True)
    for line in result.stdout.splitlines():
        if line.startswith('package:'):
            for part in line.split():
                if part.startswith('name='):
                    return part.split('=')[1].strip("'")
    raise Exception("Could not determine package name from APK.")

def is_app_installed(package):
    result = subprocess.run(['adb', 'shell', 'pm', 'list', 'packages'], capture_output=True, text=True)
    return package in result.stdout

def uninstall_app(package):
    subprocess.run(['adb', 'uninstall', package])
    print(f"Uninstalled {package}")

def install_app(apk_path):
    subprocess.run(['adb', 'install', '-r', apk_path])
    print(f"Installed {apk_path}")

def prompt_user(msg):
    ans = input(msg + ' [y/n]: ').strip().lower()
    return ans == 'y'

def start_appium_session(package, wait_timeout):
    # Get main activity
    result = subprocess.run(['adb', 'shell', 'cmd', 'package', 'resolve-activity', '--brief', package], capture_output=True, text=True)
    lines = result.stdout.splitlines()
    if len(lines) > 1:
        main_activity = lines[1].strip()
    else:
        raise Exception("Could not determine main activity.")

    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = main_activity
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    options.set_capability('adbExecTimeout', 60000)  # Increase ADB execution timeout to 60 seconds
    options.set_capability('uiautomator2ServerLaunchTimeout', 60000)  # Increase UiAutomator2 server launch timeout to 60 seconds

    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(wait_timeout)
    return driver

def extract_ui_hierarchy(driver):
    # Dump UI hierarchy as XML
    xml = driver.page_source
    return xml

def query_ollama(prompt, model):
    response = requests.post(
        "http://localhost:11434/api/generate",
        json={"model": model, "prompt": prompt},
        stream=True
    )
    result = ""
    for line in response.iter_lines():
        if line:
            data = json.loads(line)
            if "response" in data:
                result += data["response"]
    return result

def save_json(data, path):
    with open(path, 'w') as f:
        json.dump(data, f, indent=2)

def save_feature(content, path):
    with open(path, 'w') as f:
        f.write(content)

def remove_bounds(obj):
    if isinstance(obj, dict):
        return {k: remove_bounds(v) for k, v in obj.items() if k.lower() not in ["bounds", "bound", "coordinate", "coordinates", "position"]}
    elif isinstance(obj, list):
        return [remove_bounds(i) for i in obj]
    else:
        return obj

def remove_bounds_from_string(json_str):
    # Remove all "bounds": ... (including arrays or objects) from the string
    return re.sub(r'"bounds"\s*:\s*(\[[^\]]*\]|\{[^}]*\}),?', '', json_str)

def flatten_tree_to_locators(data):
    # Recursively flatten a tree of nodes/attributes to a flat mapping of locator strategies per element
    locators = {}
    def recurse(node, parent_index=[0]):
        if isinstance(node, dict):
            # If this node has locator strategies, extract them
            if 'attributes' in node:
                attrs = node['attributes']
                # Compose a unique key for the element
                key = attrs.get('resource-id') or attrs.get('content-desc') or attrs.get('text') or f"element_{parent_index[0]}"
                # Only keep relevant locator strategies
                locator = {}
                for k in ['resource-id', 'accessibility-id', 'class', 'text', 'content-desc', 'xpath', 'index']:
                    if k in attrs and attrs[k] is not None:
                        locator[k] = attrs[k]
                # Add class if present at the node level
                if 'class' in node and 'class' not in locator:
                    locator['class'] = node['class']
                locators[key] = locator
                parent_index[0] += 1
                # Recurse into children
                if 'children' in attrs:
                    for child in attrs['children']:
                        recurse(child, parent_index)
            # Some trees may have 'children' at the node level
            if 'children' in node:
                for child in node['children']:
                    recurse(child, parent_index)
        elif isinstance(node, list):
            for child in node:
                recurse(child, parent_index)
    # Start recursion
    if 'nodes' in data:
        recurse(data['nodes'])
    else:
        recurse(data)
    return locators

def list_to_locator_dict(locator_list):
    locators = {}
    for i, item in enumerate(locator_list):
        # Use resource-id, content-desc, text, or fallback to class+index
        key = (
            item.get("resource-id")
            or item.get("content-desc")
            or item.get("text")
            or f"{item.get('class', 'element')}_{item.get('index', i)}"
        )
        # Only keep relevant locator strategies
        locator = {}
        for k in ['resource-id', 'accessibility-id', 'class', 'text', 'content-desc', 'xpath', 'index']:
            if k in item and item[k] is not None:
                locator[k] = item[k]
        locators[key] = locator
    return locators

def extract_locators_from_xml(xml, screen_name, package):
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    locators = []
    def recurse(elem, path):
        # Count siblings of the same tag to get the index
        parent = elem.getparent() if hasattr(elem, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == elem.tag]
            idx = same_tag_siblings.index(elem) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{elem.tag}[{idx}]"
        attrs = elem.attrib.copy()
        class_name = attrs.get('class', elem.tag)
        text = attrs.get('text', elem.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        # Only collect if text or content-desc is non-empty
        if (
            (text and text.strip()) or
            (content_desc and content_desc.strip()) or
            (resource_id and resource_id.strip()) or
            (attrs.get('clickable', '') == 'true') or
            (attrs.get('focusable', '') == 'true')
        ):
            locator = {
                "screen_name": screen_name,
                "package": package,
                "resource_id": resource_id,
                "class_name": class_name,
                "text": text,
                "content_desc": content_desc,
                "xpath": this_xpath,
                "element_type": class_name,
            }
            # Add other attributes if present
            for k in ["clickable", "focusable", "enabled", "selected", "checkable", "checked", "scrollable", "index"]:
                if k in attrs:
                    locator[k] = attrs[k]
            locators.append(locator)
        for child in list(elem):
            recurse(child, this_xpath)
    recurse(root, '')
    return locators

def get_screen_name(driver):
    # Try to get a unique screen name using activity and package
    try:
        activity = driver.current_activity
    except Exception:
        activity = "unknown_activity"
    try:
        package = driver.current_package
    except Exception:
        package = "unknown_package"
    return f"{package}:{activity}"

def hash_xml(xml):
    return hashlib.md5(xml.encode('utf-8')).hexdigest()

def get_full_xpath_map(xml):
    """
    Build a mapping from element attributes (resource-id, class, text, content-desc, bounds) to their full XPath in the XML tree.
    Returns: {xpath: {attr_dict}}
    """
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    xpath_map = {}

    def recurse(node, path):
        # Count siblings of the same tag to get the index
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        # Build this node's XPath
        this_xpath = f"{path}/{node.tag}[{idx}]"
        # Save attributes for this node
        attrs = node.attrib.copy()
        attrs['tag'] = node.tag
        xpath_map[this_xpath] = attrs
        # Recurse into children
        for child in list(node):
            recurse(child, this_xpath)
    # Use a stack to avoid recursion limit
    def recurse_stack(node, path):
        stack = [(node, path)]
        while stack:
            node, path = stack.pop()
            parent = node.getparent() if hasattr(node, 'getparent') else None
            if parent is not None:
                same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                idx = same_tag_siblings.index(node) + 1
            else:
                idx = 1
            this_xpath = f"{path}/{node.tag}[{idx}]"
            attrs = node.attrib.copy()
            attrs['tag'] = node.tag
            xpath_map[this_xpath] = attrs
            for child in reversed(list(node)):
                stack.append((child, this_xpath))
    # Use stack version for safety
    recurse_stack(root, '')
    return xpath_map

# Replace get_xpath_for_element to use full-path XPath from XML

def get_xpath_for_element_by_attrs(el, xml):
    # Get element's attributes
    rid = el.get_attribute('resource-id')
    text = el.get_attribute('text')
    cdesc = el.get_attribute('content-desc')
    cls = el.get_attribute('class')
    bounds = el.get_attribute('bounds')
    attrs = {'resource-id': rid, 'text': text, 'content-desc': cdesc, 'class': cls, 'bounds': bounds}
    xpath_map = get_full_xpath_map(xml)
    # Try to find the matching node in the map (strict)
    for xpath, node_attrs in xpath_map.items():
        match = True
        for k in ['resource-id', 'text', 'content-desc', 'class', 'bounds']:
            if attrs[k] and node_attrs.get(k, None) != attrs[k]:
                match = False
                break
        if match:
            return xpath
    # Fallback: try to match by resource-id
    if rid:
        for xpath, node_attrs in xpath_map.items():
            if node_attrs.get('resource-id', None) == rid:
                return xpath
    # Fallback: try to match by content-desc
    if cdesc:
        for xpath, node_attrs in xpath_map.items():
            if node_attrs.get('content-desc', None) == cdesc:
                return xpath
    # Fallback: try to match by class and text
    if cls and text:
        for xpath, node_attrs in xpath_map.items():
            if node_attrs.get('class', None) == cls and node_attrs.get('text', None) == text:
                return xpath
    return None

def save_screen_xml(screen_name, xml, folder="screen_xmls"):
    os.makedirs(folder, exist_ok=True)
    fname = os.path.join(folder, f"{screen_name}.xml")
    with open(fname, 'w') as f:
        f.write(xml)
    print(f"[XML-DUMP] Saved UI XML to {fname}")

# Track all visited screens and their element counts, per package
visited_screens_summary = {}
visited_per_package = {}

def robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=20):
    import time
    start = time.time()
    while time.time() - start < timeout:
        curr_activity = driver.current_activity
        curr_xml = driver.page_source
        if curr_activity != prev_activity or curr_xml != prev_xml:
            # Also wait for new clickable elements to appear
            try:
                new_clickables = driver.find_elements("xpath", '//*[( @clickable="true" or @focusable="true" or @visible="true") and not(@password="true")]')
                if len(new_clickables) > 0:
                    return True
            except Exception:
                pass
        time.sleep(1)
    return False

def wait_for_native_page_load(driver, timeout=15):
    import time
    start = time.time()
    prev_xml = driver.page_source
    while time.time() - start < timeout:
        time.sleep(1)
        curr_xml = driver.page_source
        if curr_xml != prev_xml:
            return True
        prev_xml = curr_xml
    return False

def wait_for_webview_page_load(driver, timeout=15):
    import time
    from selenium.common.exceptions import WebDriverException
    start = time.time()
    while time.time() - start < timeout:
        try:
            ready = driver.execute_script('return document.readyState')
            if ready == 'complete':
                return True
        except WebDriverException:
            pass
        time.sleep(1)
    return False

def detect_context(driver):
    try:
        package = driver.current_package
        contexts = driver.contexts
        curr_context = driver.current_context if hasattr(driver, 'current_context') else None
        if package == 'com.android.chrome':
            for ctx in contexts:
                if 'WEBVIEW' in ctx or 'CHROMIUM' in ctx:
                    return 'webview'
            return 'chrome_native'
        elif curr_context and (curr_context.startswith('WEBVIEW') or curr_context.startswith('CHROMIUM')):
            return 'webview'
        else:
            return 'native'
    except Exception as e:
        print(f"[CONTEXT DETECT] Error: {e}")
        return 'unknown'

def smart_go_back(driver, main_package, chrome_opened_from_app=False):
    try:
        contexts = driver.contexts
        current_package = driver.current_package
        if current_package == main_package:
            print("[SMART-BACK] In native app, using driver.back()")
            driver.back()
        elif 'WEBVIEW' in ''.join(contexts) or 'CHROMIUM' in ''.join(contexts) or 'chrome' in current_package:
            print("[SMART-BACK] In webview/chrome, switching to main app")
            driver.activate_app(main_package)
        else:
            print("[SMART-BACK] Unknown context, defaulting to driver.back()")
            driver.back()
        time.sleep(0.7)
        # After back, if context is chrome_native and not opened from app, switch back to app
        context = detect_context(driver)
        if context == 'chrome_native' and not chrome_opened_from_app:
            print("[SMART-BACK] Chrome context detected after back, but NOT opened from app. Switching back to app.")
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"[SMART-BACK] Could not return to main app: {e}")
    except Exception as e:
        print(f"[SMART-BACK] Error during smart go back: {e}")

# --- WebView crawling and improved smart crawl (move to top-level) ---
def crawl_webview_elements(driver, all_locators, webview_name=None):
    print(f"[WEBVIEW] Crawling webview: {webview_name if webview_name else ''}")
    try:
        # Switch to webview context
        webview_contexts = [c for c in driver.contexts if 'WEBVIEW' in c]
        if not webview_contexts:
            print("[WEBVIEW] No webview context found.")
            return
        driver.switch_to.context(webview_contexts[0])
        print(f"[WEBVIEW] Switched to context: {webview_contexts[0]}")
        # Collect actionable elements
        elements = driver.find_elements(By.XPATH, "//a | //button | //*[@role='button']")
        for el in elements:
            try:
                tag = el.tag_name
                text = el.text
                el_id = el.get_attribute('id')
                el_class = el.get_attribute('class')
                href = el.get_attribute('href')
                locator = {
                    "webview": True,
                    "tag": tag,
                    "text": text,
                    "id": el_id,
                    "class": el_class,
                    "href": href
                }
                print(f"[WEBVIEW-ELEMENT] {locator}")
                all_locators.append(locator)
            except Exception as e:
                print(f"[WEBVIEW] Error extracting element: {e}")
        # Switch back to native context
        driver.switch_to.context(driver.contexts[0])
        print(f"[WEBVIEW] Switched back to native context: {driver.contexts[0]}")
    except Exception as e:
        print(f"[WEBVIEW] Error during webview crawling: {e}")

def improved_smart_crawl(driver, package, main_package, all_locators):
    max_retries = 3
    retries = 0
    while True:
        context = detect_context(driver)
        print(f"[SMART-CRAWL] Detected context: {context}")
        if context == 'native':
            print("[SMART-CRAWL] Waiting for native page to load...")
            wait_for_native_page_load(driver)
            print("[SMART-CRAWL] Collecting ALL native elements...")
            screen_name = get_screen_name(driver)
            native_elements = collect_all_native_elements(driver, screen_name, driver.current_package)
            all_locators.extend(native_elements)
            break
        elif context == 'webview':
            print("[SMART-CRAWL] Waiting for webview page to load...")
            wait_for_webview_page_load(driver)
            print("[SMART-CRAWL] Collecting ALL webview elements...")
            webview_contexts = [c for c in driver.contexts if 'WEBVIEW' in c]
            if webview_contexts:
                driver.switch_to.context(webview_contexts[0])
                webview_elements = collect_all_webview_elements(driver)
                all_locators.extend(webview_elements)
                driver.switch_to.context(driver.contexts[0])
                print(f"[SMART-CRAWL] Switched back to native context: {driver.contexts[0]}")
            else:
                print("[SMART-CRAWL] No webview context found.")
            break
        elif context == 'chrome_native':
            print("[SMART-CRAWL] Chrome native context detected. Attempting CDP collection...")
            collect_chrome_cdp_locators()
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"[SMART-CRAWL] Could not return to main app: {e}")
            continue
        else:
            print("[SMART-CRAWL] Unknown context. Stopping crawl.")
            break
        retries += 1
        if retries >= max_retries:
            print("[SMART-CRAWL] Max retries reached. Stopping crawl.")
            break

def crawl_menus_on_page(driver, main_package, all_locators, menu_descs, depth=0, is_main_page=True, visited_texts=None, config=None, package=None):
    """
    Crawl all main menu icons and any clickable element with actionable label in itself or its descendants, with scrolling and substring matching.
    """
    import time
    from selenium.common.exceptions import WebDriverException
    if visited_texts is None:
        visited_texts = set()
    ACTIONABLE_LABELS = [
        # Main Page
        'Pengelolaan Kinerja', 'Lihat Semua', 'Butuh Bantuan?',
        # Ruang GTK
        'Pengelolaan Kinerja\nPerencanaan, pelaksanaan, dan penilaian kinerja Anda untuk pengembangan diri dan satdik',
        'Seleksi Kepala Sekolah',
        'Refleksi Kompetensi',
        'Perangkat Ajar',
        'CP/ATP',
        'Ide Praktik',
        'Bukti Karya',
        'Video Inspirasi',
        'Asesmen (Asesmen Murid & AKM Kelas)',
        'Kelas',
        'Pengelolaan Pembelajaran',
        'Pengelolaan Kinerja\nDokumen rujukan untuk Pengelolaan Kinerja',
        'Peningkatan Kompetensi',
        'Pengelolaan Satuan Pendidikan',
        # Ruang Murid
        'Riwayat Pendidikan',
        'Sumber Buku Teks Pembelajaran',
        'Pendidikan Jarak Jauh',
        # Ruang Sekolah
        'Pengadaan Barang dan Jasa Sekolah',
        # Add any other actionable labels as needed
    ]
    if is_main_page:
        # Scroll down until 'Layanan Paling Banyak Diakses' is found
        print("[SCROLL-SEARCH] Scrolling main page to find 'Layanan Paling Banyak Diakses'...")
        found_section = False
        max_scrolls = 30
        scroll_count = 0
        while not found_section and scroll_count < max_scrolls:
            elements = driver.find_elements("xpath", "//*[contains(@content-desc, 'Layanan Paling Banyak Diakses') or contains(@text, 'Layanan Paling Banyak Diakses')]")
            if elements:
                found_section = True
                print(f"[SCROLL-SEARCH] Found section 'Layanan Paling Banyak Diakses' after {scroll_count} scrolls.")
                break
            else:
                try:
                    driver.swipe(300, 1000, 300, 300, 800)
                    time.sleep(1.2)
                except Exception as e:
                    print(f"[SCROLL-SEARCH] Scroll failed: {e}")
                    break
                scroll_count += 1
        if not found_section:
            print("[SCROLL-SEARCH] Could not find 'Layanan Paling Banyak Diakses' after scrolling.")
        # After finding the section, look for 'Lihat Semua'
        found_lihat_semua = False
        if found_section:
            print("[SCROLL-SEARCH] Searching for 'Lihat Semua' after finding the section...")
            # Try up to 5 more scrolls in case it's just below
            for i in range(5):
                lihat_semua_els = driver.find_elements("xpath", "//*[@content-desc='Lihat Semua' or @text='Lihat Semua']")
                if lihat_semua_els:
                    el = lihat_semua_els[0]
                    locator = {
                        "screen_name": get_screen_name(driver),
                        "package": driver.current_package,
                        "resource_id": el.get_attribute("resource-id"),
                        "class_name": el.get_attribute("class"),
                        "text": el.get_attribute("text"),
                        "content_desc": el.get_attribute("content-desc"),
                        "xpath": get_xpath_for_element_by_attrs(el, driver.page_source),
                        "element_type": el.get_attribute("class"),
                    }
                    all_locators.append(locator)
                    print(f"[SCROLL-SEARCH] Found and collected locator for 'Lihat Semua': {locator}")
                    found_lihat_semua = True
                    break
                else:
                    try:
                        driver.swipe(300, 1000, 300, 300, 800)
                        time.sleep(1.2)
                    except Exception as e:
                        print(f"[SCROLL-SEARCH] Scroll for 'Lihat Semua' failed: {e}")
                        break
            if not found_lihat_semua:
                print("[SCROLL-SEARCH] Could not find 'Lihat Semua' after finding the section.")
        # 1. Main menu icons (by content-desc)
        for menu_desc in menu_descs:
            try:
                xpath = f"//*[@content-desc='{menu_desc}']"
                menu_elements = driver.find_elements("xpath", xpath)
                if not menu_elements:
                    print(f"  [MENU] No element found for menu '{menu_desc}' on this page.")
                    continue
                menu_el = menu_elements[0]
                el_class = menu_el.get_attribute('class')
                print(f"[MENU] Clicking menu: content_desc='{menu_desc}', class='{el_class}', xpath='{xpath}' at depth {depth}")
                try:
                    step_start = time.time()
                    # Save locator for the actionable menu element before clicking
                    menu_locator = {
                        "screen_name": get_screen_name(driver),
                        "package": driver.current_package,
                        "resource_id": menu_el.get_attribute("resource-id"),
                        "class_name": menu_el.get_attribute("class"),
                        "text": menu_el.get_attribute("text"),
                        "content_desc": menu_el.get_attribute("content-desc"),
                        "xpath": get_xpath_for_element_by_attrs(menu_el, driver.page_source),
                        "element_type": menu_el.get_attribute("class"),
                    }
                    all_locators.append(menu_locator)
                    prev_activity = driver.current_activity
                    prev_xml = driver.page_source
                    print(f"[DEBUG-TIME] Before menu_el.click() for '{menu_desc}' at {step_start:.2f}")
                    menu_el.click()
                    print(f"[DEBUG] After menu click: activity={driver.current_activity}, package={driver.current_package}")
                    robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                    print(f"[DEBUG-TIME] After click+wait for '{menu_desc}': took {time.time() - step_start:.2f} seconds")
                    context = detect_context(driver)
                    print(f"[MENU] Detected context after menu click: {context}")
                    new_xml = driver.page_source
                    new_screen_name = get_screen_name(driver)
                    new_locators = extract_locators_from_xml(new_xml, new_screen_name, driver.current_package)
                    print(f"[MENU] Collected {len(new_locators)} locators on navigated screen.")
                    all_locators.extend(new_locators)
                    save_screen_xml(f"{driver.current_package}__{new_screen_name}", new_xml)
                    # Log submenus present on this page (do not click recursively)
                    for sub_desc in menu_descs:
                        sub_xpath = f"//*[@content-desc='{sub_desc}']"
                        sub_elements = driver.find_elements("xpath", sub_xpath)
                        if sub_elements:
                            sub_class = sub_elements[0].get_attribute('class')
                            print(f"[SUBMENU] Present submenu: content_desc='{sub_desc}', class='{sub_class}', xpath='{sub_xpath}' at depth {depth+1}")
                    prev_activity = driver.current_activity
                    prev_xml = driver.page_source
                    back_start = time.time()
                    print(f"[DEBUG-TIME] Before smart_go_back for '{menu_desc}' at {back_start:.2f}")
                    smart_go_back(driver, main_package)
                    robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                    print(f"[DEBUG-TIME] After smart_go_back+wait for '{menu_desc}': took {time.time() - back_start:.2f} seconds")
                except WebDriverException as e:
                    print(f"[FATAL] Appium session lost or UiAutomator2 crashed: {e}")
                    if config and package:
                        print("[RECOVERY] Attempting to restart Appium session and continue crawling...")
                        driver.quit()
                        driver = start_appium_session(package, config['wait_timeout'])
                        print("[RECOVERY] Appium session restarted. Retrying current menu.")
                        return crawl_menus_on_page(driver, main_package, all_locators, menu_descs, depth, is_main_page, visited_texts, config, package)
                    else:
                        print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                        return
                    print("[FATAL] Exiting crawl. Please check Appium server and device logs.")
                    return
                except Exception as e:
                    print(f"[MENU] Error processing menu '{menu_desc}': {e}")
                    continue
            except Exception as e:
                print(f"[MENU] Error processing menu '{menu_desc}': {e}")
                continue
        # 2. Scroll and collect all clickable elements (deduplicate by location, size, class, content-desc, text)
        print("[PHASE] Finished all main menu clicks. Starting scroll-and-collect-clickables phase.")
        def scroll_and_collect_clickables():
            all_clickables = []
            unique_keys = set()
            last_page_source = None
            max_scrolls = 100  # Increased to ensure thorough scrolling
            scroll_count = 0
            while scroll_count < max_scrolls:
                print(f"[DEBUG] scroll-and-collect-clickables: scroll_count={scroll_count}")
                try:
                    clickables = driver.find_elements("xpath", "//*[@clickable='true']")
                except WebDriverException as e:
                    print(f"[FATAL] Appium session lost or UiAutomator2 crashed during scroll-and-collect: {e}")
                    if config and package:
                        print("[RECOVERY] Attempting to restart Appium session and continue crawling (scroll phase)...")
                        driver.quit()
                        print("[RECOVERY] Appium session restart required. Exiting scroll phase to allow outer recovery.")
                        return []
                    else:
                        print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                        return []
                for el in clickables:
                    try:
                        key = (
                            el.location['x'], el.location['y'],
                            el.size['width'], el.size['height'],
                            el.get_attribute('class'),
                            el.get_attribute('content-desc') or '',
                            el.get_attribute('text') or ''
                        )
                        if key not in unique_keys:
                            all_clickables.append(el)
                            unique_keys.add(key)
                    except Exception:
                        continue
                # Try to scroll
                try:
                    driver.swipe(300, 1000, 300, 300, 800)
                    time.sleep(1.5)
                except Exception:
                    break
                new_page_source = driver.page_source
                if new_page_source == last_page_source:
                    break
                last_page_source = new_page_source
                scroll_count += 1
            if scroll_count >= max_scrolls:
                print(f"[WARNING] Reached max_scrolls ({max_scrolls}) in scroll-and-collect-clickables. Breaking to prevent infinite loop.")
            return all_clickables

        # --- Debug logging: print all elements with text/content-desc/clickable/focusable ---
        def debug_log_all_elements():
            xml = driver.page_source
            import xml.etree.ElementTree as ET
            tree = ET.ElementTree(ET.fromstring(xml))
            root = tree.getroot()
            def recurse(node, path):
                parent = node.getparent() if hasattr(node, 'getparent') else None
                if parent is not None:
                    same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                    idx = same_tag_siblings.index(node) + 1
                else:
                    idx = 1
                this_xpath = f"{path}/{node.tag}[{idx}]"
                attrs = node.attrib.copy()
                class_name = attrs.get('class', node.tag)
                text = attrs.get('text', node.text or "")
                content_desc = attrs.get('content-desc', "")
                clickable = attrs.get('clickable', '')
                focusable = attrs.get('focusable', '')
                if (
                    (text and text.strip()) or
                    (content_desc and content_desc.strip()) or
                    (clickable == 'true') or
                    (focusable == 'true')
                ):
                    print(f"[ALL-ELEMENT] class='{class_name}', text='{text}', content_desc='{content_desc}', clickable='{clickable}', focusable='{focusable}', xpath='{this_xpath}'")
                for child in list(node):
                    recurse(child, this_xpath)
            recurse(root, '')

        print("[PHASE] Debug log all elements on this screen:")
        debug_log_all_elements()

        # --- Collect all labeled elements (not just clickable) and check visibility ---
        def collect_all_labeled_elements_and_visibility():
            xml = driver.page_source
            import xml.etree.ElementTree as ET
            tree = ET.ElementTree(ET.fromstring(xml))
            root = tree.getroot()
            all_labeled = []
            not_visible = []
            def recurse(node, path):
                parent = node.getparent() if hasattr(node, 'getparent') else None
                if parent is not None:
                    same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
                    idx = same_tag_siblings.index(node) + 1
                else:
                    idx = 1
                this_xpath = f"{path}/{node.tag}[{idx}]"
                attrs = node.attrib.copy()
                class_name = attrs.get('class', node.tag)
                text = attrs.get('text', node.text or "")
                content_desc = attrs.get('content-desc', "")
                resource_id = attrs.get('resource-id', "")
                if (
                    (text and text.strip()) or
                    (content_desc and content_desc.strip())
                ):
                    el_info = {
                        'class': class_name,
                        'text': text,
                        'content_desc': content_desc,
                        'resource_id': resource_id,
                        'xpath': this_xpath
                    }
                    # Try to find the element in the UI using xpath
                    try:
                        el = driver.find_element('xpath', this_xpath)
                        is_visible = False
                        try:
                            is_visible = el.is_displayed()
                        except Exception:
                            # Some elements may not support is_displayed
                            is_visible = False
                        el_info['visible'] = is_visible
                        if not is_visible:
                            # Try to scroll to the element
                            try:
                                driver.execute_script('mobile: scroll', {'direction': 'down', 'element': el.id})
                                is_visible = el.is_displayed()
                                el_info['visible_after_scroll'] = is_visible
                            except Exception:
                                el_info['visible_after_scroll'] = False
                            if not el_info.get('visible_after_scroll', False):
                                not_visible.append(el_info)
                    except Exception:
                        el_info['visible'] = False
                        el_info['visible_after_scroll'] = False
                        not_visible.append(el_info)
                    all_labeled.append(el_info)
                for child in list(node):
                    recurse(child, this_xpath)
            recurse(root, '')
            print(f"[ALL-LABELED] Total labeled elements found: {len(all_labeled)}")
            for el in all_labeled:
                print(f"[ALL-LABELED] {el}")
            if not_visible:
                print(f"[NOT-VISIBLE] Elements not visible after scroll attempts: {len(not_visible)}")
                for el in not_visible:
                    print(f"[NOT-VISIBLE] {el}")
            return all_labeled, not_visible

        # Call the new labeled element collector with visibility check
        all_labeled_elements, not_visible_elements = collect_all_labeled_elements_and_visibility()
        # Optionally, add these to all_locators (if you want to save them)
        # for el in all_labeled_elements:
        #     all_locators.append(el)
        print("[PHASE] Finished scroll-and-collect-clickables. Starting actionable element loop.")
        def fuzzy_match(label, candidates):
            label_norm = re.sub(r'\s+', ' ', label.strip().lower())
            for cand in candidates:
                cand_norm = re.sub(r'\s+', ' ', cand.strip().lower())
                if label_norm in cand_norm or cand_norm in label_norm:
                    return True
                # Use difflib for partial similarity
                if SequenceMatcher(None, label_norm, cand_norm).ratio() > 0.85:
                    return True
            return False

        def wait_for_element_visible(driver, xpath, timeout=10):
            try:
                return WebDriverWait(driver, timeout).until(
                    lambda d: d.find_element('xpath', xpath).is_displayed()
                )
            except Exception:
                return False

        def try_dismiss_popups(driver):
            # Try to dismiss common popups/dialogs
            popup_xpaths = [
                "//android.widget.Button[@text='OK']",
                "//android.widget.Button[@text='Tutup']",
                "//android.widget.Button[@text='Close']",
                "//android.widget.Button[@text='Cancel']",
                "//android.widget.Button[@text='Batal']",
                "//android.widget.Button[@content-desc='Tutup']",
                "//android.widget.Button[@content-desc='Close']",
                "//android.widget.Button[@content-desc='OK']",
            ]
            for xpath in popup_xpaths:
                try:
                    btns = driver.find_elements('xpath', xpath)
                    for btn in btns:
                        if btn.is_displayed():
                            print(f"[POPUP] Dismissing popup/button: {xpath}")
                            btn.click()
                            return True
                except Exception:
                    continue
            return False

        def multi_directional_scroll(driver):
            # Try vertical and horizontal scrolls
            try:
                driver.swipe(300, 1000, 300, 300, 800)  # vertical down
                driver.swipe(300, 300, 300, 1000, 800)  # vertical up
                driver.swipe(1000, 800, 300, 800, 800)  # horizontal left
                driver.swipe(300, 800, 1000, 800, 800)  # horizontal right
            except Exception:
                pass

        def scroll_and_collect_clickables():
            all_clickables = []
            unique_keys = set()
            last_page_source = None
            max_scrolls = 100
            scroll_count = 0
            while scroll_count < max_scrolls:
                print(f"[DEBUG] scroll-and-collect-clickables: scroll_count={scroll_count}")
                try:
                    clickables = driver.find_elements("xpath", "//*[@clickable='true']")
                except WebDriverException as e:
                    print(f"[FATAL] Appium session lost or UiAutomator2 crashed during scroll-and-collect: {e}")
                    if config and package:
                        print("[RECOVERY] Attempting to restart Appium session and continue crawling (scroll phase)...")
                        driver.quit()
                        print("[RECOVERY] Appium session restart required. Exiting scroll phase to allow outer recovery.")
                        return []
                    else:
                        print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                        return []
                for el in clickables:
                    try:
                        key = (
                            el.location['x'], el.location['y'],
                            el.size['width'], el.size['height'],
                            el.get_attribute('class'),
                            el.get_attribute('content-desc') or '',
                            el.get_attribute('text') or ''
                        )
                        if key not in unique_keys:
                            all_clickables.append(el)
                            unique_keys.add(key)
                    except Exception:
                        continue
                # Try multi-directional scroll
                multi_directional_scroll(driver)
                time.sleep(1.5)
                new_page_source = driver.page_source
                if new_page_source == last_page_source:
                    break
                last_page_source = new_page_source
                scroll_count += 1
            if scroll_count >= max_scrolls:
                print(f"[WARNING] Reached max_scrolls ({max_scrolls}) in scroll-and-collect-clickables. Breaking to prevent infinite loop.")
            return all_clickables

        print("[PHASE] Starting actionable element click phase.")
        clickable_elements = scroll_and_collect_clickables()
        print("[PHASE] Finished scroll-and-collect-clickables. Starting actionable element loop.")
        for el in clickable_elements:
            try:
                # Aggregate all text/content-desc from self and descendants
                def aggregate_labels(element):
                    labels = []
                    try:
                        text = element.get_attribute('text') or ''
                        desc = element.get_attribute('content-desc') or ''
                        if text.strip():
                            labels.append(text.strip())
                        if desc.strip():
                            labels.append(desc.strip())
                        children = element.find_elements('xpath', './*')
                        for child in children:
                            labels.extend(aggregate_labels(child))
                    except Exception:
                        pass
                    return labels
                all_labels = aggregate_labels(el)
                aggregated_label = ' '.join(all_labels)
                actionable = None
                for label in ACTIONABLE_LABELS:
                    if fuzzy_match(label, [aggregated_label]) and label not in visited_texts:
                        actionable = label
                        break
                if not actionable:
                    continue
                # Wait for element to be visible/clickable
                try:
                    xpath = el.get_attribute('xpath') if hasattr(el, 'get_attribute') else None
                    if xpath:
                        wait_for_element_visible(driver, xpath, timeout=10)
                except Exception:
                    pass
                # Save locator for the actionable element before clicking
                actionable_locator = {
                    "screen_name": get_screen_name(driver),
                    "package": driver.current_package,
                    "resource_id": el.get_attribute("resource-id"),
                    "class_name": el.get_attribute("class"),
                    "text": el.get_attribute("text"),
                    "content_desc": el.get_attribute("content-desc"),
                    "xpath": get_xpath_for_element_by_attrs(el, driver.page_source),
                    "element_type": el.get_attribute("class"),
                }
                all_locators.append(actionable_locator)
                el_class = el.get_attribute('class')
                print(f"[ACTIONABLE] Clicking: label='{actionable}', class='{el_class}' at depth {depth}")
                visited_texts.add(actionable)
                step_start = time.time()
                prev_activity = driver.current_activity
                prev_xml = driver.page_source
                print(f"[DEBUG-TIME] Before el.click() for '{actionable}' at {step_start:.2f}")
                el.click()
                # Try to dismiss popups/dialogs after click
                try_dismiss_popups(driver)
                robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                print(f"[DEBUG-TIME] After click+wait for '{actionable}': took {time.time() - step_start:.2f} seconds")
                context = detect_context(driver)
                print(f"[ACTIONABLE] Detected context after click: {context}")
                new_xml = driver.page_source
                new_screen_name = get_screen_name(driver)
                new_locators = extract_locators_from_xml(new_xml, new_screen_name, driver.current_package)
                print(f"[ACTIONABLE] Collected {len(new_locators)} locators on navigated screen.")
                all_locators.extend(new_locators)
                save_screen_xml(f"{driver.current_package}__{new_screen_name}", new_xml)
                back_start = time.time()
                print(f"[DEBUG-TIME] Before smart_go_back for '{actionable}' at {back_start:.2f}")
                prev_activity = driver.current_activity
                prev_xml = driver.page_source
                smart_go_back(driver, main_package)
                robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=15)
                print(f"[DEBUG-TIME] After smart_go_back+wait for '{actionable}': took {time.time() - back_start:.2f} seconds")
            except WebDriverException as e:
                print(f"[FATAL] Appium session lost or UiAutomator2 crashed: {e}")
                if config and package:
                    print("[RECOVERY] Attempting to restart Appium session and continue crawling (actionable phase)...")
                    driver.quit()
                    driver = start_appium_session(package, config['wait_timeout'])
                    print("[RECOVERY] Appium session restarted. Retrying actionable element loop.")
                    return crawl_menus_on_page(driver, main_package, all_locators, menu_descs, depth, is_main_page, visited_texts, config, package)
                else:
                    print("[RECOVERY] No config/package provided, cannot restart session. Exiting crawl.")
                    return
                print("[FATAL] Exiting crawl. Please check Appium server and device logs.")
                return
            except Exception as e:
                print(f"[ACTIONABLE] Error processing element: {e}")
                continue
        print("[PHASE] Finished actionable element loop.")
    else:
        # For submenus, only log/collect submenus present on this page (do not click recursively)
        for menu_desc in menu_descs:
            try:
                xpath = f"//*[@content-desc='{menu_desc}']"
                menu_elements = driver.find_elements("xpath", xpath)
                if not menu_elements:
                    continue
                menu_el = menu_elements[0]
                el_class = menu_el.get_attribute('class')
                print(f"[SUBMENU] Present submenu: content_desc='{menu_desc}', class='{el_class}', xpath='{xpath}' at depth {depth}")
            except Exception as e:
                print(f"[SUBMENU] Error processing submenu '{menu_desc}': {e}")
                continue

def crawl_app(driver, package, max_depth=3, max_elements=100, config=None):
    """
    Main recursive app crawler. Calls crawl_menus_on_page and handles depth/element limits.
    """
    main_package = package
    all_locators = []
    # Define main menu content-descs
    MAIN_MENUS = [
        'Ruang GTK', 'Ruang Murid', 'Ruang Sekolah', 'Ruang Bahasa',
        'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua',
        'Sumber Belajar', 'Pusat Perbukuan', 'Pengelolaan Kinerja', 'Lihat Semua',
        'Butuh Bantuan?', 'Ruang', 'Pemberitahuan', 'Akun'

    ]
    crawl_menus_on_page(driver, package, all_locators, MAIN_MENUS, depth=0, config=config, package=package)
    # Ensure crawl_app always returns a list
    if all_locators is None:
        all_locators = []
    return all_locators

def collect_all_labeled_elements(driver, xml, log_prefix=""):
    """
    Collect and log ALL elements with non-empty text or content-desc, regardless of clickable/focusable/interactivity.
    Log their class, text, content-desc, resource-id, and XPath.
    """
    from xml.etree import ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    all_labeled = []
    def recurse(node, path):
        # Count siblings of the same tag to get the index
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        if (
            (text and text.strip()) or
            (content_desc and content_desc.strip()) or
            (resource_id and resource_id.strip()) or
            (attrs.get('clickable', '') == 'true') or
            (attrs.get('focusable', '') == 'true')
        ):
            print(f"{log_prefix}[ALL-ELEMENT] class='{class_name}', text='{text}', content_desc='{content_desc}', resource_id='{resource_id}', xpath='{this_xpath}'")
            all_labeled.append({
                'class': class_name,
                'text': text,
                'content_desc': content_desc,
                'resource_id': resource_id,
                'xpath': this_xpath
            })
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"{log_prefix}[ALL-ELEMENT] Total labeled elements found: {len(all_labeled)}")
    return all_labeled

def collect_gtk_web_locators(gtk_url):
    from appium import webdriver as web_driver
    from selenium.webdriver.common.by import By
    import time
    caps = {
        "platformName": "Android",
        "deviceName": "emulator-5554",
        "browserName": "Chrome",
        # "chromedriverExecutableDir": "/path/to/chromedriver",  # Uncomment and set if needed
        "automationName": "UiAutomator2"
    }
    print("[INFO] [CHROME] Starting temporary Appium session for Chrome web collection...")
    driver = web_driver.Remote("http://localhost:4723/wd/hub", caps)
    time.sleep(3)
    print(f"[INFO] [CHROME] Navigating to {gtk_url}")
    driver.get(gtk_url)
    time.sleep(5)
    elements = driver.find_elements(By.XPATH, "//a | //button | //*[@role='button']")
    locators = []
    for el in elements:
        try:
            tag = el.tag_name
            text = el.text
            el_id = el.get_attribute('id')
            el_class = el.get_attribute('class')
            href = el.get_attribute('href')
            locators.append({
                "tag": tag,
                "text": text,
                "id": el_id,
                "class": el_class,
                "href": href
            })
        except Exception as e:
            print(f"[CHROME] Error extracting element: {e}")
    os.makedirs("locators", exist_ok=True)
    with open("locators/gtk_web_elements.json", "w") as f:
        json.dump(locators, f, indent=2)
    print(f"[INFO] [CHROME] Collected {len(locators)} web elements from GTK page. Saved to locators/gtk_web_elements.json.")
    driver.quit()

def collect_chrome_cdp_locators():
    print("[CDP] Attempting to collect web locators from Chrome via DevTools Protocol...")
    try:
        resp = requests.get("http://localhost:9222/json")
        tabs = resp.json()
        if not tabs:
            print("[CDP] No Chrome tabs found.")
            return []
        tab = tabs[0]
        ws_url = tab['webSocketDebuggerUrl']
        ws = websocket.create_connection(ws_url)
        ws.send(json.dumps({"id": 1, "method": "DOM.enable"}))
        ws.send(json.dumps({"id": 2, "method": "Runtime.enable"}))
        ws.send(json.dumps({
            "id": 3,
            "method": "Runtime.evaluate",
            "params": {
                "expression": """
                JSON.stringify(
                  [...document.querySelectorAll('a,button,[role=button]')].map(el => ({
                    tag: el.tagName,
                    text: el.innerText,
                    id: el.id,
                    class: el.className,
                    href: el.href
                  }))
                )
                """,
                "returnByValue": True
            }
        }))
        while True:
            result = ws.recv()
            if '"id":3' in result:
                break
        data = json.loads(result)
        elements = json.loads(data['result']['result']['value'])
        ws.close()
        print(f"[CDP] Collected {len(elements)} web elements from Chrome page.")
        import os
        os.makedirs("locators", exist_ok=True)
        with open("locators/gtk_web_elements.json", "w") as f:
            json.dump(elements, f, indent=2)
        return elements
    except Exception as e:
        print(f"[CDP] Error collecting web locators: {e}")
        return []

DEBUG_ACTIONABLE_ONLY = False  # Set to True to only visit actionable items for debugging
DEBUG_SCROLL_ONLY = False  # Set to True to only scroll and collect all locators, skip menu clicks

def find_clickable_by_attrs(driver, attrs):
    candidates = driver.find_elements("xpath", "//*[@clickable='true']")
    for el in candidates:
        try:
            if (
                el.get_attribute("resource-id") == attrs["resource-id"] and
                el.get_attribute("content-desc") == attrs["content-desc"] and
                el.get_attribute("text") == attrs["text"] and
                el.get_attribute("class") == attrs["class"] and
                el.get_attribute("bounds") == attrs["bounds"]
            ):
                return el
        except Exception:
            continue
    return None

# Helper to group click candidates by row and find arrow/button in each row
def group_submenu_rows(click_candidates):
    # Group by parent element (row)
    rows = defaultdict(lambda: {"labels": [], "arrows": []})
    for el in click_candidates:
        try:
            parent = el.find_element("xpath", "..")
            parent_id = parent.id if hasattr(parent, 'id') else str(parent)
            class_name = el.get_attribute("class")
            content_desc = el.get_attribute("content-desc")
            text = el.get_attribute("text")
            # Heuristic: arrow is likely an ImageView or Button with right arrow or empty text
            if (class_name in ["android.widget.ImageView", "android.widget.Button"] and
                ("arrow" in (content_desc or "").lower() or "arrow" in (text or "").lower() or (content_desc == '' and text == ''))):
                rows[parent_id]["arrows"].append(el)
            elif class_name in ["android.widget.TextView", "android.widget.Button", "android.view.View"]:
                rows[parent_id]["labels"].append(el)
            else:
                rows[parent_id]["labels"].append(el)
        except Exception:
            continue
    return rows

def replay_navigation_stack(driver, navigation_stack, main_package, config):
    print(f"[RECOVERY] Replaying navigation stack: {navigation_stack}")
    for depth, node_name in enumerate(navigation_stack[1:], 1):
        print(f"[RECOVERY] Navigating to '{node_name}' at depth {depth}")
        try:
            el = None
            try:
                el = driver.find_element("xpath", f"//*[@content-desc='{node_name}']")
            except Exception:
                pass
            if not el:
                try:
                    el = driver.find_element("xpath", f"//*[@text='{node_name}']")
                except Exception:
                    pass
            if not el:
                print(f"[RECOVERY] Could not find element for '{node_name}' during replay. Aborting recovery.")
                return False
            el.click()
            time.sleep(2)
        except Exception as e:
            print(f"[RECOVERY] Error navigating to '{node_name}': {e}")
            return False
    print(f"[RECOVERY] Navigation stack replayed successfully.")
    return True

def crawl_menu_hierarchy(driver, node_name, main_package, config, depth=0, max_depth=5, visited_hashes=None, chrome_opened_from_app=False, navigation_stack=None, retry_count=0, max_retries=3):
    import hashlib
    if visited_hashes is None:
        visited_hashes = set()
    if navigation_stack is None:
        navigation_stack = [node_name]
    if depth > max_depth:
        return None
    indent = '  ' * depth
    print(f"{indent}[ENTER] Node: '{node_name}' at depth {depth}")
    node = {
        "name": node_name,
        "locators": [],
        "children": []
    }
    try:
        robust_wait_for_new_screen(driver, driver.current_activity, driver.page_source, timeout=10)
        context = detect_context(driver)
        print(f"{indent}[CONTEXT] Detected context: {context}")
        current_package = driver.current_package
        # Strict enforcement: Only crawl main package or webview/Chrome opened from app
        if context == 'native':
            if current_package != main_package:
                print(f"{indent}[STRICT] Skipping non-main package '{current_package}'. Switching back to main app.")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            xml = driver.page_source
            screen_name = get_screen_name(driver)
            print(f"{indent}[COLLECT] Collecting native locators on '{node_name}' at depth {depth}")
            node["locators"] = extract_locators_from_xml(xml, screen_name, driver.current_package)
            if not node["locators"]:
                print(f"{indent}[INFO] No element locators found on '{node_name}' at depth {depth}. Going back to previous.")
                if depth > 0:
                    smart_go_back(driver, main_package, chrome_opened_from_app)
                    robust_wait_for_new_screen(driver, None, None, timeout=10)
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            try:
                while True:
                    # Strict enforcement: Only process if in main package or directly opened Chrome/webview
                    current_package = driver.current_package
                    context_now = detect_context(driver)
                    if context_now == 'native' and current_package != main_package:
                        print(f"{indent}[STRICT] Not in main package '{main_package}' (current: '{current_package}'). Switching back and skipping.")
                        try:
                            driver.activate_app(main_package)
                            time.sleep(2)
                        except Exception as e:
                            print(f"{indent}[STRICT] Could not return to main app: {e}")
                        break
                    if context_now in ['chrome_native', 'webview'] and not chrome_opened_from_app:
                        print(f"{indent}[STRICT] In Chrome/webview context NOT opened from app. Switching back and skipping.")
                        try:
                            driver.activate_app(main_package)
                            time.sleep(2)
                        except Exception as e:
                            print(f"{indent}[STRICT] Could not return to main app: {e}")
                        break
                    try:
                        click_candidates = driver.find_elements(
                            "xpath",
                            "//*[@clickable='true' or @focusable='true' or string-length(@content-desc)>0 or string-length(@text)>0]"
                        )
                        clickable_attrs_list = []
                        print(f"{indent}  [LOG] All click candidate elements on this page:")
                        for el in click_candidates:
                            try:
                                attrs = {
                                    "resource-id": el.get_attribute("resource-id"),
                                    "content-desc": el.get_attribute("content-desc"),
                                    "text": el.get_attribute("text"),
                                    "class": el.get_attribute("class"),
                                    "bounds": el.get_attribute("bounds"),
                                    "clickable": el.get_attribute("clickable"),
                                    "focusable": el.get_attribute("focusable"),
                                }
                                print(f"{indent}    - class='{attrs['class']}', text='{attrs['text']}', content_desc='{attrs['content-desc']}', clickable='{attrs['clickable']}', focusable='{attrs['focusable']}'")
                                el_hash = hashlib.md5(str(tuple(attrs.values())).encode()).hexdigest()
                                if el_hash in visited_hashes:
                                    print(f"{indent}  [SKIP] Already visited element with hash '{el_hash}' at depth {depth+1}")
                                    continue
                                clickable_attrs_list.append((attrs, el_hash))
                            except Exception:
                                continue
                    except StaleElementReferenceException:
                        print(f"{indent}[WARN] StaleElementReferenceException when finding clickables, re-collecting.")
                        continue
                    any_clicked = False
                    for attrs, el_hash in clickable_attrs_list:
                        try:
                            visited_hashes.add(el_hash)
                            el_label = (attrs['content-desc'] or attrs['text'] or '').strip() or f"Element"
                            print(f"{indent}[DEBUG] Visiting submenu '{el_label}' at depth {depth+1}")
                            print(f"{indent}  [LOG] Label locator: class='{attrs['class']}', text='{attrs['text']}', content_desc='{attrs['content-desc']}'")
                            print(f"{indent}  [LOG] Arrow locator: class='{attrs['class']}', text='{attrs['text']}', content_desc='{attrs['content-desc']}'")
                            prev_activity = driver.current_activity
                            prev_xml = driver.page_source
                            clicked = False
                            # Try to click the label/description first
                            if attrs.get("resource-id") or attrs.get("content-desc") or attrs.get("text"):
                                try:
                                    print(f"{indent}  [CLICK] Trying label for submenu '{el_label}' at depth {depth+1}")
                                    el = find_clickable_by_attrs(driver, attrs)
                                    if el:
                                        el.click()
                                        clicked = True
                                        clicked_type = "label"
                                        print(f"{indent}  [INFO] Clicked using: {clicked_type}")
                                    else:
                                        print(f"{indent}  [WARN] Could not re-find element for label '{el_label}' at depth {depth+1}")
                                except Exception as e:
                                    print(f"{indent}  [WARN] Could not click label for '{el_label}': {e}")
                            # If label not clickable, try the arrow as fallback
                            if not clicked and (attrs.get("resource-id") or attrs.get("content-desc") or attrs.get("text")):
                                try:
                                    print(f"{indent}  [CLICK] Trying arrow for submenu '{el_label}' at depth {depth+1}")
                                    el = find_clickable_by_attrs(driver, attrs)
                                    if el:
                                        el.click()
                                        clicked = True
                                        clicked_type = "arrow"
                                        print(f"{indent}  [INFO] Clicked using: {clicked_type}")
                                    else:
                                        print(f"{indent}  [WARN] Could not re-find element for arrow '{el_label}' at depth {depth+1}")
                                except Exception as e:
                                    print(f"{indent}  [WARN] Could not click arrow for '{el_label}': {e}")
                            if not clicked:
                                print(f"{indent}  [SKIP] Could not click label or arrow for '{el_label}' at depth {depth+1}")
                                continue
                            # Verify that click led to a new page
                            robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=10)
                            new_activity = driver.current_activity
                            new_xml = driver.page_source
                            if new_activity == prev_activity and new_xml == prev_xml:
                                print(f"{indent}  [SKIP] Click on '{el_label}' did not change screen at depth {depth+1}. Skipping recursion.")
                                continue
                            child_context = detect_context(driver)
                            print(f"{indent}  [CONTEXT] Detected context after click: {child_context}")
                            # Strict enforcement after click
                            current_package_after = driver.current_package
                            context_after = detect_context(driver)
                            if context_after == 'native' and current_package_after != main_package:
                                print(f"{indent}[STRICT] After click: Not in main package '{main_package}' (current: '{current_package_after}'). Switching back and skipping.")
                                try:
                                    driver.activate_app(main_package)
                                    time.sleep(2)
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                                continue
                            if context_after in ['chrome_native', 'webview'] and not chrome_opened_from_app:
                                print(f"{indent}[STRICT] After click: In Chrome/webview context NOT opened from app. Switching back and skipping.")
                                try:
                                    driver.activate_app(main_package)
                                    time.sleep(2)
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                                continue
                            # If context is chrome_native or webview, collect locators, kill external app, and return to main app
                            if child_context in ['chrome_native', 'webview']:
                                print(f"{indent}[STRICT] External app (Chrome/webview) detected after click on '{el_label}'. Collecting first page locators.")
                                ext_locators = collect_gtk_web_locators(driver.current_url) if hasattr(driver, 'current_url') else []
                                child_node = {
                                    "name": el_label,
                                    "locators": ext_locators,
                                    "children": [],
                                    "external": True
                                }
                                print(f"{indent}[STRICT] Collected {len(ext_locators)} locators from external app. Killing external app and returning to main app.")
                                try:
                                    subprocess.run(["adb", "shell", "am", "force-stop", driver.current_package])
                                    print(f"{indent}[STRICT] Killed external app: {driver.current_package}")
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not kill external app: {e}")
                                try:
                                    driver.activate_app(main_package)
                                    time.sleep(2)
                                except Exception as e:
                                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                                node["children"].append(child_node)
                                any_clicked = True
                                break
                            # RECURSE: Add to navigation stack
                            recursed_child = crawl_menu_hierarchy(
                                driver, el_label, main_package, config, depth+1, max_depth, visited_hashes, chrome_opened_from_app=False,
                                navigation_stack=navigation_stack + [el_label]
                            )
                            if recursed_child:
                                child_node = {
                                    "name": el_label,
                                    "locators": recursed_child.get("locators", []),
                                    "children": recursed_child.get("children", []),
                                    "locator": {
                                        "screen_name": screen_name,
                                        "package": driver.current_package,
                                        "resource_id": attrs["resource-id"],
                                        "class_name": attrs["class"],
                                        "text": attrs["text"],
                                        "content_desc": attrs["content-desc"],
                                        "bounds": attrs["bounds"],
                                        "element_type": attrs["class"],
                                    }
                                }
                                node["children"].append(child_node)
                            print(f"{indent}  [BACK] Returning from child '{el_label}' to '{node_name}' at depth {depth}")
                            smart_go_back(driver, main_package, chrome_opened_from_app)
                            robust_wait_for_new_screen(driver, prev_activity, prev_xml, timeout=10)
                            print(f"{indent}  [RETURN] Finished child: '{el_label}' at depth {depth+1}")
                            any_clicked = True
                            break  # After any navigation, break and re-collect clickables
                        except StaleElementReferenceException:
                            print(f"{indent}  [WARN] StaleElementReferenceException on element access, skipping this branch.")
                            continue
                        except WebDriverException as e:
                            print(f"{indent}  [ERROR] WebDriverException: {e}. Skipping this branch.")
                            # RECOVERY: Restart Appium, replay navigation stack, and resume crawling
                            if retry_count < max_retries:
                                print(f"{indent}[RECOVERY] Appium/WebDriverException detected. Restarting Appium and replaying navigation stack (attempt {retry_count+1}/{max_retries})...")
                                try:
                                    driver.quit()
                                except Exception:
                                    pass
                                driver_new = start_appium_session(main_package, config['wait_timeout'])
                                success = replay_navigation_stack(driver_new, navigation_stack, main_package, config)
                                if success:
                                    print(f"{indent}[RECOVERY] Successfully returned to '{node_name}' at depth {depth}. Resuming crawl...")
                                    # Resume crawl from this node with new driver and incremented retry_count
                                    recursed_child = crawl_menu_hierarchy(
                                        driver_new, node_name, main_package, config, depth, max_depth, visited_hashes, chrome_opened_from_app,
                                        navigation_stack=navigation_stack, retry_count=retry_count+1, max_retries=max_retries
                                    )
                                    if recursed_child:
                                        node.update(recursed_child)
                                    return node
                                else:
                                    print(f"{indent}[RECOVERY] Failed to return to '{node_name}' after Appium restart. Skipping this branch.")
                                    return node
                            else:
                                print(f"{indent}[RECOVERY] Max retries reached for '{node_name}'. Skipping this branch.")
                                return node
                        except Exception as e:
                            print(f"{indent}  [ERROR] Exception during element click/recursion: {e}")
                            continue
                    if not any_clicked:
                        break  # No more new clickables, exit loop
            except StaleElementReferenceException:
                print(f"{indent}[WARN] StaleElementReferenceException when finding clickables, skipping this node.")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
        elif context == 'webview':
            if not chrome_opened_from_app:
                print(f"{indent}[STRICT] Webview context NOT opened from app. Skipping and switching back to main app.")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            print(f"{indent}[COLLECT] Collecting webview locators on '{node_name}' at depth {depth}")
            node["locators"] = collect_gtk_web_locators(driver.current_url) if hasattr(driver, 'current_url') else []
            print(f"{indent}[STRICT] After collecting webview locators, killing external app and returning to main app.")
            # Kill the external webview app (Chrome)
            try:
                subprocess.run(["adb", "shell", "am", "force-stop", driver.current_package])
                print(f"{indent}[STRICT] Killed external app: {driver.current_package}")
            except Exception as e:
                print(f"{indent}[STRICT] Could not kill external app: {e}")
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"{indent}[STRICT] Could not return to main app: {e}")
            print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
            return node
        elif context == 'chrome_native':
            if chrome_opened_from_app:
                print(f"{indent}[CHROME] Detected Chrome mobile context opened from app. Collecting only the first page's element locators and returning to app.")
                node["locators"] = collect_gtk_web_locators(driver.current_url) if hasattr(driver, 'current_url') else []
                print(f"{indent}[CHROME] Collected {len(node['locators'])} locators from Chrome mobile. Killing external app and switching back to app.")
                # Kill the external Chrome app
                try:
                    subprocess.run(["adb", "shell", "am", "force-stop", driver.current_package])
                    print(f"{indent}[STRICT] Killed external app: {driver.current_package}")
                except Exception as e:
                    print(f"{indent}[STRICT] Could not kill external app: {e}")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[CHROME] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
            else:
                print(f"{indent}[STRICT] Chrome context detected but NOT opened from app. Skipping Chrome crawl and switching back to app.")
                try:
                    driver.activate_app(main_package)
                    time.sleep(2)
                except Exception as e:
                    print(f"{indent}[STRICT] Could not return to main app: {e}")
                print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
                return node
        else:
            print(f"{indent}[STRICT] Unknown or external context '{context}'. Skipping and switching back to main app.")
            try:
                driver.activate_app(main_package)
                time.sleep(2)
            except Exception as e:
                print(f"{indent}[STRICT] Could not return to main app: {e}")
            print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
            return node
        print(f"{indent}[EXIT] Node: '{node_name}' at depth {depth}")
        return node
    except WebDriverException as e:
        print(f"{indent}[RECOVERY] WebDriverException detected at node '{node_name}': {e}")
        if retry_count < max_retries:
            print(f"{indent}[RECOVERY] Restarting Appium and replaying navigation stack (attempt {retry_count+1}/{max_retries})...")
            try:
                driver.quit()
            except Exception:
                pass
            driver_new = start_appium_session(main_package, config['wait_timeout'])
            success = replay_navigation_stack(driver_new, navigation_stack, main_package, config)
            if success:
                print(f"{indent}[RECOVERY] Successfully returned to '{node_name}' at depth {depth}. Resuming crawl...")
                # Resume crawl from this node with new driver and incremented retry_count
                recursed_child = crawl_menu_hierarchy(
                    driver_new, node_name, main_package, config, depth, max_depth, visited_hashes, chrome_opened_from_app,
                    navigation_stack=navigation_stack, retry_count=retry_count+1, max_retries=max_retries
                )
                if recursed_child:
                    node.update(recursed_child)
                return node
            else:
                print(f"{indent}[RECOVERY] Failed to return to '{node_name}' after Appium restart. Skipping this branch.")
                return node
        else:
            print(f"{indent}[RECOVERY] Max retries reached for '{node_name}'. Skipping this branch.")
            return node
    except Exception as e:
        print(f"{indent}[ERROR] Exception at node '{node_name}': {e}")
        return node

# Update crawl_and_save_menu_hierarchy to call crawl_menu_hierarchy with visited_hashes

def crawl_and_save_menu_hierarchy(driver, config, main_package):
    print("[HIERARCHY] Starting dynamic crawl from main page...")
    root_node = crawl_menu_hierarchy(driver, "Main Page", main_package, config, depth=0, visited_hashes=None)
    os.makedirs(config['locators_folder'], exist_ok=True)
    out_path = os.path.join(config['locators_folder'], "menu_hierarchy.json")
    with open(out_path, "w") as f:
        json.dump(root_node, f, indent=2)
    print(f"[HIERARCHY] Saved menu hierarchy to {out_path}")

def collect_all_native_elements(driver, screen_name, package):
    xml = driver.page_source
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    all_elements = []
    def recurse(node, path):
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        if (
            (text and text.strip()) or
            (content_desc and content_desc.strip()) or
            (resource_id and resource_id.strip())
        ):
            el_info = {
                'screen_name': screen_name,
                'package': package,
                'class': class_name,
                'text': text,
                'content_desc': content_desc,
                'resource_id': resource_id,
                'xpath': this_xpath
            }
            all_elements.append(el_info)
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"[ALL-NATIVE-ELEMENTS] Total: {len(all_elements)}")
    for el in all_elements:
        print(f"[ALL-NATIVE-ELEMENTS] {el}")
    return all_elements

def collect_all_webview_elements(driver):
    print(f"[WEBVIEW] Collecting all labeled/identifiable elements in webview...")
    elements = driver.find_elements(By.XPATH, '//*')
    all_elements = []
    for el in elements:
        try:
            tag = el.tag_name
            text = el.text
            el_id = el.get_attribute('id')
            el_class = el.get_attribute('class')
            name = el.get_attribute('name')
            aria_label = el.get_attribute('aria-label')
            if (
                (text and text.strip()) or
                (el_id and el_id.strip()) or
                (el_class and el_class.strip()) or
                (name and name.strip()) or
                (aria_label and aria_label.strip())
            ):
                # Try to get XPath and CSS selector if possible
                try:
                    xpath = driver.execute_script(
                        '''
                        function absoluteXPath(element) {
                            var comp, comps = [];
                            var parent = null;
                            var xpath = '';
                            var getPos = function(element) {
                                var position = 1, curNode;
                                if (element.nodeType == Node.ATTRIBUTE_NODE) {
                                    return null;
                                }
                                for (curNode = element.previousSibling; curNode; curNode = curNode.previousSibling) {
                                    if (curNode.nodeName == element.nodeName) {
                                        ++position;
                                    }
                                }
                                return position;
                            };
                            if (element instanceof Document) {
                                return '/';
                            }
                            for (; element && !(element instanceof Document); element = element.nodeType ==Node.ATTRIBUTE_NODE ? element.ownerElement : element.parentNode) {
                                comp = comps[comps.length] = {};
                                switch (element.nodeType) {
                                    case Node.TEXT_NODE:
                                        comp.name = 'text()';
                                        break;
                                    case Node.ATTRIBUTE_NODE:
                                        comp.name = '@' + element.nodeName;
                                        break;
                                    case Node.PROCESSING_INSTRUCTION_NODE:
                                        comp.name = 'processing-instruction()';
                                        break;
                                    case Node.COMMENT_NODE:
                                        comp.name = 'comment()';
                                        break;
                                    case Node.ELEMENT_NODE:
                                        comp.name = element.nodeName;
                                        break;
                                }
                                comp.position = getPos(element);
                            }
                            for (var i = comps.length - 1; i >= 0; i--) {
                                comp = comps[i];
                                xpath += '/' + comp.name.toLowerCase();
                                if (comp.position !== null && comp.position > 1) {
                                    xpath += '[' + comp.position + ']';
                                }
                            }
                            return xpath;
                        }
                        return absoluteXPath(arguments[0]);
                        ''', el)
                except Exception:
                    xpath = None
                try:
                    css_selector = driver.execute_script(
                        '''
                        function cssPath(el) {
                            if (!(el instanceof Element)) return;
                            var path = [];
                            while (el.nodeType === Node.ELEMENT_NODE) {
                                var selector = el.nodeName.toLowerCase();
                                if (el.id) {
                                    selector += '#' + el.id;
                                    path.unshift(selector);
                                    break;
                                } else {
                                    var sib = el, nth = 1;
                                    while (sib = sib.previousElementSibling) {
                                        if (sib.nodeName.toLowerCase() == selector) nth++;
                                    }
                                    if (nth != 1) selector += ':nth-of-type(' + nth + ')';
                                }
                                path.unshift(selector);
                                el = el.parentNode;
                            }
                            return path.join(' > ');
                        }
                        return cssPath(arguments[0]);
                        ''', el)
                except Exception:
                    css_selector = None
                locator = {
                    "webview": True,
                    "tag": tag,
                    "text": text,
                    "id": el_id,
                    "class": el_class,
                    "name": name,
                    "aria-label": aria_label,
                    "xpath": xpath,
                    "css_selector": css_selector
                }
                all_elements.append(locator)
        except Exception as e:
            print(f"[WEBVIEW] Error extracting element: {e}")
    print(f"[ALL-WEBVIEW-ELEMENTS] Total: {len(all_elements)}")
    for el in all_elements:
        print(f"[ALL-WEBVIEW-ELEMENTS] {el}")
    return all_elements

def load_previous_all_elements(path):
    if os.path.exists(path):
        with open(path, 'r') as f:
            try:
                data = json.load(f)
                if isinstance(data, dict) and 'all_elements' in data:
                    return data['all_elements']
                elif isinstance(data, list):
                    return data
            except Exception:
                return []
    return []

def collect_full_xml_elements(driver, screen_name, package):
    xml = driver.page_source
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    all_elements = []
    def recurse(node, path):
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        el_info = {
            'screen_name': screen_name,
            'package': package,
            'class': class_name,
            'text': text,
            'content_desc': content_desc,
            'resource_id': resource_id,
            'xpath': this_xpath
        }
        # Add all other attributes for completeness
        for k, v in attrs.items():
            if k not in el_info:
                el_info[k] = v
        all_elements.append(el_info)
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"[FULL-XML-ELEMENTS] Total: {len(all_elements)} on screen '{screen_name}'")
    return all_elements

def hash_node_attrs(attrs):
    # Hash a node's identifying attributes for deduplication
    key = (attrs.get('class', ''), attrs.get('text', ''), attrs.get('content-desc', ''), attrs.get('resource-id', ''), attrs.get('xpath', ''))
    return hashlib.md5(str(key).encode('utf-8')).hexdigest()

def perform_scroll(driver, scroll_count=None):
    try:
        # Try to find the main scrollable container
        scrollable = None
        for class_name in ['ScrollView', 'RecyclerView', 'android.widget.ScrollView', 'androidx.recyclerview.widget.RecyclerView']:
            try:
                scrollable = driver.find_element('xpath', f"//*[contains(@class, '{class_name}')]")
                break
            except Exception:
                continue
        if scrollable:
            loc = scrollable.location
            size = scrollable.size
            start_x = loc['x'] + size['width'] // 2
            start_y = loc['y'] + int(size['height'] * 0.8)
            end_y = loc['y'] + int(size['height'] * 0.2)
            print(f"[SCROLL] Found scrollable container: class={scrollable.get_attribute('class')}, location={loc}, size={size}")
            print(f"[SCROLL] Swiping within container from ({start_x},{start_y}) to ({start_x},{end_y}) [scroll #{scroll_count}]")
        else:
            # Fallback: use whole screen
            size = driver.get_window_size()
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.8)
            end_y = int(size['height'] * 0.2)
            print(f"[SCROLL] No scrollable container found, swiping on screen from ({start_x},{start_y}) to ({start_x},{end_y}) [scroll #{scroll_count}]")
        driver.execute_script('mobile: swipeGesture', {
            'left': start_x,
            'top': start_y,
            'width': 1,
            'height': 1,
            'direction': 'up',
            'percent': 0.8
        })
        print(f"[SCROLL] Performed mobile: swipeGesture (up) [scroll #{scroll_count}]")
        screenshot_path = f"scroll_screenshot_{scroll_count}.png" if scroll_count is not None else "scroll_screenshot.png"
        driver.save_screenshot(screenshot_path)
        print(f"[SCROLL] Screenshot saved: {screenshot_path}")
    except Exception as e:
        print(f"[SCROLL] Scroll within container failed: {e}")

def collect_full_scrollable_page_elements(driver, screen_name, package):
    print(f"[SCROLL] Starting full scroll-and-collect for screen '{screen_name}'...")
    scroll_until_bottom(driver)
    seen_hashes = set()
    all_elements = []
    xml = driver.page_source
    import xml.etree.ElementTree as ET
    tree = ET.ElementTree(ET.fromstring(xml))
    root = tree.getroot()
    def recurse(node, path):
        parent = node.getparent() if hasattr(node, 'getparent') else None
        if parent is not None:
            same_tag_siblings = [sib for sib in parent if sib.tag == node.tag]
            idx = same_tag_siblings.index(node) + 1
        else:
            idx = 1
        this_xpath = f"{path}/{node.tag}[{idx}]"
        attrs = node.attrib.copy()
        class_name = attrs.get('class', node.tag)
        text = attrs.get('text', node.text or "")
        content_desc = attrs.get('content-desc', "")
        resource_id = attrs.get('resource-id', "")
        el_info = {
            'screen_name': screen_name,
            'package': package,
            'class': class_name,
            'text': text,
            'content_desc': content_desc,
            'resource_id': resource_id,
            'xpath': this_xpath
        }
        for k, v in attrs.items():
            if k not in el_info:
                el_info[k] = v
        node_hash = hash_node_attrs(el_info)
        if node_hash not in seen_hashes:
            all_elements.append(el_info)
            seen_hashes.add(node_hash)
        for child in list(node):
            recurse(child, this_xpath)
    recurse(root, '')
    print(f"[FULL-SCROLL] Collected {len(all_elements)} unique elements for screen '{screen_name}'.")
    return all_elements

def recursive_full_crawl(driver, package, visited_screens, nav_path, all_collected, nav_tree, depth=0, max_depth=10):
    screen_name = get_screen_name(driver)
    print(f"[RECURSIVE-CRAWL] Visiting screen: {screen_name}, depth={depth}")
    if screen_name in visited_screens or depth > max_depth:
        print(f"[RECURSIVE-CRAWL] Already visited or max depth reached: {screen_name}")
        return
    visited_screens.add(screen_name)
    elements = collect_full_scrollable_page_elements(driver, screen_name, package)
    all_collected.extend([el for el in elements if el not in all_collected])
    nav_tree[screen_name] = {'elements': elements, 'children': {}}
    # Find clickable elements with content-desc
    nav_targets = [el for el in elements if el.get('clickable') == 'true' and el.get('content_desc')]
    for el in nav_targets:
        print(f"[RECURSIVE-CRAWL] Clicking element: content-desc='{el.get('content_desc')}', xpath='{el.get('xpath')}'")
        try:
            # Find element by xpath and click
            found_els = driver.find_elements('xpath', el['xpath'])
            if found_els:
                found_els[0].click()
                time.sleep(2)
                recursive_full_crawl(driver, package, visited_screens, nav_path + [el.get('content_desc')], all_collected, nav_tree[screen_name]['children'], depth+1, max_depth)
                driver.back()
                time.sleep(1)
            else:
                print(f"[RECURSIVE-CRAWL] Could not find element by xpath: {el['xpath']}")
        except Exception as e:
            print(f"[RECURSIVE-CRAWL] Error clicking element: {e}")

def scroll_to_section(driver, marker_text=None, marker_desc=None):
    print(f"[SCROLL] scroll_to_section called with marker_text={marker_text}, marker_desc={marker_desc}")
    try:
        if marker_text:
            print(f"[SCROLL] Attempting UiScrollable scroll to text: {marker_text}")
            driver.find_element(
                f"android=new UiScrollable(new UiSelector().scrollable(true)).scrollIntoView(new UiSelector().textContains(\"{marker_text}\"))"
            )
            print(f"[SCROLL] UiScrollable scrolled to text: {marker_text}")
        elif marker_desc:
            print(f"[SCROLL] Attempting UiScrollable scroll to content-desc: {marker_desc}")
            driver.find_element(
                f"android=new UiScrollable(new UiSelector().scrollable(true)).scrollIntoView(new UiSelector().descriptionContains(\"{marker_desc}\"))"
            )
            print(f"[SCROLL] UiScrollable scrolled to content-desc: {marker_desc}")
    except Exception as e:
        print(f"[SCROLL] UiScrollable failed to scroll to marker (text='{marker_text}', desc='{marker_desc}'): {e}")

def perform_swipe(driver, direction='down', duration=1000):
    """Universal swipe function that works on any Android view"""
    window = driver.get_window_size()
    width, height = window['width'], window['height']
    if direction.lower() == 'down':
        start_x, start_y = width//2, int(height*0.7)
        end_x, end_y = width//2, int(height*0.3)
    elif direction.lower() == 'up':
        start_x, start_y = width//2, int(height*0.3)
        end_x, end_y = width//2, int(height*0.7)
    else:
        raise ValueError("Direction must be 'up' or 'down'")
    print(f"[SCROLL] Swiping {direction} from ({start_x},{start_y}) to ({end_x},{end_y})")
    driver.swipe(start_x, start_y, end_x, end_y, duration)
    time.sleep(1.5)

def find_scrollable_element(driver):
    """Locates the main scrollable view if one exists"""
    scrollables = driver.find_elements(AppiumBy.ANDROID_UIAUTOMATOR,
        'new UiSelector().scrollable(true)')
    if scrollables:
        return max(scrollables, key=lambda e: e.size['height'] * e.size['width'])
    return None

def smart_scroll(driver, direction='down'):
    scrollable = find_scrollable_element(driver)
    if scrollable:
        loc = scrollable.location
        size = scrollable.size
        center_x = loc['x'] + size['width']//2
        if direction == 'down':
            start_y = loc['y'] + int(size['height'] * 0.8)
            end_y = loc['y'] + int(size['height'] * 0.2)
        else:
            start_y = loc['y'] + int(size['height'] * 0.2)
            end_y = loc['y'] + int(size['height'] * 0.8)
        print(f"[SCROLL] Smart scroll {direction} in container from ({center_x},{start_y}) to ({center_x},{end_y})")
        driver.swipe(center_x, int(start_y), center_x, int(end_y), 1000)
        time.sleep(1.5)
    else:
        print("[SCROLL] No scrollable container found, falling back to full screen swipe.")
        perform_swipe(driver, direction)

def is_same_content(driver, previous_source, similarity_threshold=0.95):
    current = driver.page_source
    if previous_source == current:
        return True
    matcher = difflib.SequenceMatcher(None, previous_source, current)
    return matcher.ratio() > similarity_threshold

def scroll_until_end(driver, max_attempts=10):
    previous_source = driver.page_source
    attempt = 0
    while attempt < max_attempts:
        for method in [smart_scroll, perform_swipe]:
            try:
                method(driver, 'down')
                driver.save_screenshot(f"scroll_debug_{attempt}_{method.__name__}.png")
                time.sleep(2)
                if is_same_content(driver, previous_source):
                    print("[SCROLL] Reached end of content.")
                    return True
                previous_source = driver.page_source
                break
            except Exception as e:
                print(f"[SCROLL] Scroll failed: {str(e)}")
                continue
        attempt += 1
    print("[SCROLL] Max scroll attempts reached.")
    return False

def extract_current_elements(driver):
    elements = []
    for el in driver.find_elements(AppiumBy.XPATH, '//*'):
        try:
            elements.append({
                'text': el.text,
                'class': el.get_attribute('class'),
                'content-desc': el.get_attribute('content-desc'),
                'resource-id': el.get_attribute('resource-id'),
                'xpath': el.get_attribute('xpath') if hasattr(el, 'get_attribute') else '',
                'bounds': el.rect
            })
        except StaleElementReferenceException:
            continue
    return elements

def collect_elements_with_scroll(driver, max_attempts=20):
    print("[COLLECT] Starting element collection with scrolling...")
    collected_elements = []
    seen_xpaths = set()
    previous_source = driver.page_source
    attempt = 0

    while attempt < max_attempts:
        # Extract and merge elements after each scroll
        current_elements = extract_current_elements(driver)
        new_elements = [e for e in current_elements if e['xpath'] not in seen_xpaths]
        for e in new_elements:
            collected_elements.append(e)
            seen_xpaths.add(e['xpath'])
        print(f"[COLLECT] After scroll {attempt}: {len(new_elements)} new elements, {len(collected_elements)} total.")

        # Save screenshot for debugging
        driver.save_screenshot(f"scroll_debug_{attempt}.png")

        # Try to scroll
        smart_scroll(driver, 'down')
        time.sleep(1.5)  # Wait for content to load

        # Check if new content appeared
        current_source = driver.page_source
        if current_source == previous_source:
            print("[COLLECT] No new content after scroll, stopping.")
            break
        previous_source = current_source
        attempt += 1

    print(f"[COLLECT] Finished. Total unique elements collected: {len(collected_elements)}")
    return collected_elements

def debug_scroll(driver):
    for i in range(3):
        driver.save_screenshot(f"scroll_debug_{i}_before.png")
        perform_swipe(driver)
        time.sleep(1)
        driver.save_screenshot(f"scroll_debug_{i}_after.png")

def scroll_and_collect_interactive(driver, log_prefix="", max_scrolls=20):
    def get_interactive_elements(current_xml):
        elements = driver.find_elements("xpath", '//*[((@clickable="true" or @focusable="true") and @enabled="true") or (string-length(@content-desc)>0 or string-length(@text)>0)]')
        interactive_info = []
        for idx, el in enumerate(elements):
            try:
                xpath = get_xpath_for_element_by_attrs(el, current_xml)
                el_desc = el.get_attribute('content-desc')
                el_text = el.get_attribute('text')
                el_class = el.get_attribute('class')
                clickable = el.get_attribute('clickable') == 'true'
                focusable = el.get_attribute('focusable') == 'true'
                child_labels = []
                children = []
                try:
                    children = el.find_elements('xpath', './*')
                    for child in children:
                        child_text = child.get_attribute('text')
                        child_desc = child.get_attribute('content-desc')
                        if child_text:
                            child_labels.append(child_text)
                        if child_desc:
                            child_labels.append(child_desc)
                except Exception:
                    pass
                combined_label = ' | '.join(filter(None, [el_text, el_desc] + child_labels))
                unique_key = (xpath, combined_label, el_class)
                interactive_info.append({'xpath': xpath, 'desc': el_desc, 'text': el_text, 'class': el_class, 'label': combined_label, 'unique_key': unique_key})
            except Exception:
                continue
        return interactive_info

    all_interactive = []
    unique_keys = set()
    last_page_source = None
    max_scrolls = 10
    scroll_count = 0
    current_xml = driver.page_source
    while scroll_count < max_scrolls:
        interactives = get_interactive_elements(current_xml)
        for c in interactives:
            if c['unique_key'] not in unique_keys:
                all_interactive.append(c)
                unique_keys.add(c['unique_key'])
        scrollables = driver.find_elements('xpath', '//*[@scrollable="true"]')
        if not scrollables:
            print(f"{log_prefix}  [SCROLL] No scrollable elements found. Stopping scroll.")
            break
        try:
            print(f"{log_prefix}  [SCROLL] Swiping to reveal more elements (scroll {scroll_count+1}/{max_scrolls})...")
            driver.swipe(300, 1000, 300, 300, 800)
            time.sleep(1.5)
        except Exception as e:
            print(f"{log_prefix}    Could not swipe: {e}")
            break
        new_page_source = driver.page_source
        if new_page_source == last_page_source:
            print(f"{log_prefix}  [SCROLL] No new elements after scroll. Stopping scroll.")
            break
        last_page_source = new_page_source
        current_xml = new_page_source
        scroll_count += 1
    print(f"{log_prefix}  [SCROLL] Total unique interactive elements collected: {len(all_interactive)}")
    for c in all_interactive:
        print(f"{log_prefix}  [SCROLL] Element: text='{c['text']}', content_desc='{c['desc']}', class='{c['class']}', xpath='{c['xpath']}'")
    return all_interactive

def enhanced_main():
    """Enhanced main function using the new comprehensive crawling system"""
    try:
        print("=" * 80)
        print("ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION")
        print("=" * 80)

        # Load configuration
        config = load_config()
        config['maximum_coverage'] = config.get('maximum_coverage', True)

        # Setup emulator and app
        print("[SETUP] Starting emulator...")
        start_emulator(config['emulator']['avd_name'], config['emulator']['headless'])

        print("[SETUP] Preparing APK...")
        apk_path = get_apk_path(config['apk_folder'])
        package = get_package_name(apk_path, config)
        print(f"[SETUP] Target package: {package}")

        # Handle app installation
        if is_app_installed(package):
            if prompt_user(f"{package} is already installed. Run with current app?"):
                print("[SETUP] Using current installed app.")
            else:
                print("[SETUP] Uninstalling and reinstalling app...")
                uninstall_app(package)
                install_app(apk_path)
        else:
            print("[SETUP] Installing app...")
            install_app(apk_path)

        # Start Appium session
        print("[SETUP] Connecting to Appium...")
        driver = start_appium_session(package, config['wait_timeout'])

        if not driver:
            print("[FATAL] Could not establish Appium session")
            return

        try:
            # Initialize enhanced crawler
            print("[ENHANCED] Initializing comprehensive crawler...")
            crawler = EnhancedAppCrawler(driver, package, config)

            # Start comprehensive crawling
            print("[ENHANCED] Starting comprehensive crawling process...")
            crawl_results = crawler.start_comprehensive_crawl()

            # Save comprehensive results
            print("[ENHANCED] Saving comprehensive results...")
            os.makedirs(config['locators_folder'], exist_ok=True)

            # Save main results file
            results_path = os.path.join(config['locators_folder'], f"{package}_comprehensive.json")
            with open(results_path, 'w') as f:
                json.dump(crawl_results, f, indent=2)
            print(f"[ENHANCED] Saved comprehensive results to {results_path}")

            # Generate Gherkin scenarios
            print("[ENHANCED] Generating Gherkin test scenarios...")
            gherkin_engine = GherkinGenerationEngine(config)
            gherkin_content = gherkin_engine.generate_gherkin_scenarios(crawl_results, package)

            # Save Gherkin file
            os.makedirs(config.get('features_folder', './features'), exist_ok=True)
            gherkin_path = os.path.join(config.get('features_folder', './features'), f"{package}.feature")
            with open(gherkin_path, 'w') as f:
                f.write(gherkin_content)
            print(f"[ENHANCED] Saved Gherkin scenarios to {gherkin_path}")

            # Generate summary report
            summary = crawl_results.get('crawl_summary', {})
            print("\n" + "=" * 80)
            print("COMPREHENSIVE CRAWL SUMMARY")
            print("=" * 80)
            print(f"Total elements collected: {summary.get('total_elements_collected', 0)}")
            print(f"Total pages visited: {summary.get('total_pages_visited', 0)}")
            print(f"Crash recovery attempts: {summary.get('crash_count', 0)}")
            print(f"Crawl completed successfully: {summary.get('crawl_completed', False)}")
            print(f"Last successful action: {summary.get('last_successful_action', 'N/A')}")

            # Show navigation summary
            nav_summary = crawl_results.get('navigation_path', {})
            print(f"\nFinal navigation path: {' -> '.join(nav_summary.get('final_path', []))}")
            print(f"Maximum depth reached: {nav_summary.get('final_depth', 0)}")

            print("\n" + "=" * 80)
            print("ANALYSIS COMPLETE - Enhanced comprehensive crawling finished successfully!")
            print("=" * 80)

        finally:
            # Always quit driver
            try:
                driver.quit()
                print("[CLEANUP] Appium session closed")
            except:
                pass

    except WebDriverException as e:
        print(f"[FATAL] Appium session lost or UiAutomator2 crashed: {e}")
        print("[FATAL] This may be due to device/emulator issues. Check Appium server and device logs.")
    except Exception as e:
        print(f"[FATAL] Unexpected error in enhanced main: {e}")
        import traceback
        traceback.print_exc()

def scroll_until_bottom(driver, max_attempts=10):
    """Helper function for backward compatibility"""
    try:
        previous_source = driver.page_source
        attempt = 0
        while attempt < max_attempts:
            # Perform scroll
            size = driver.get_window_size()
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.8)
            end_y = int(size['height'] * 0.2)

            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1.5)

            # Check if content changed
            current_source = driver.page_source
            if current_source == previous_source:
                print(f"[SCROLL] Reached bottom after {attempt} attempts")
                return True

            previous_source = current_source
            attempt += 1

        print(f"[SCROLL] Max attempts ({max_attempts}) reached")
        return False
    except Exception as e:
        print(f"[SCROLL] Error in scroll_until_bottom: {e}")
        return False

def main():
    """Legacy main function - now calls enhanced version"""
    enhanced_main()

if __name__ == "__main__":
    main()