#!/usr/bin/env python3

"""
SIMPLIFIED Android App Analyzer - Based on analyze_proper_flow.py pattern
Removes complex classes that cause UiAutomator2 crashes
Implements hierarchical navigation: Main → Menu → Submenu → Sub-submenu → Back
"""

import os
import sys
import time
import yaml
import json
from datetime import datetime
from appium import webdriver
from appium.options.android import UiA<PERSON>mator2Options

def load_config():
    """Load configuration from YAML file"""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def start_simple_appium_session(package):
    """Start Appium session with simple, reliable settings - SAME AS analyze_proper_flow.py"""
    print(f"[SIMPLE] Starting Appium session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    # Prevent app restart - EXACT SAME as analyze_proper_flow.py
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    print("[SIMPLE] Connecting to Appium server...")
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)

    # Activate the app - THIS WAS MISSING!
    print("[SIMPLE] Activating app...")
    driver.activate_app(package)
    time.sleep(3)  # Wait for app to load

    print("✅ Simple Appium session started and app activated!")
    return driver

def handle_login_popup(driver):
    """Handle 'Anda Belum Login' popup by clicking close button - YOUR REQUIREMENT"""
    try:
        print("[POPUP] 🔍 Checking for login popup...")

        # Check for login popup text
        popup_indicators = [
            "anda belum login",
            "you haven't logged in",
            "belum login",
            "mohon gunakan email",
            "belajar.id",
            "madrasah.kemenag.go.id"
        ]

        # Find popup elements
        all_elements = driver.find_elements("xpath", "//*")
        popup_found = False

        for element in all_elements:
            try:
                text = (element.text or '').lower()
                desc = (element.get_attribute('content-desc') or '').lower()
                content = text + ' ' + desc

                if any(indicator in content for indicator in popup_indicators):
                    popup_found = True
                    print(f"[POPUP] ❌ Login popup detected: {text[:50]}...")
                    break
            except:
                continue

        if popup_found:
            # Try to find and click close button
            close_patterns = [
                "//*[@text='×' or @content-desc='×']",
                "//*[@text='X' or @content-desc='X']",
                "//*[@text='Close' or @content-desc='Close']",
                "//*[@text='Tutup' or @content-desc='Tutup']",
                "//*[contains(@class, 'close')]",
                "//*[contains(@resource-id, 'close')]",
                "//android.widget.ImageButton",
                "//android.widget.Button[position()=1]"  # Often first button is close
            ]

            for pattern in close_patterns:
                try:
                    close_button = driver.find_element("xpath", pattern)
                    close_button.click()
                    print(f"[POPUP] ✅ Closed login popup using pattern: {pattern}")
                    time.sleep(2)  # Wait for popup to close
                    return True
                except:
                    continue

            # If no close button found, try back button
            try:
                driver.back()
                print("[POPUP] ✅ Closed popup using back button")
                time.sleep(2)
                return True
            except:
                pass

            print("[POPUP] ⚠️ Could not close login popup")
            return False
        else:
            print("[POPUP] ✅ No login popup detected")
            return True

    except Exception as e:
        print(f"[POPUP] Error handling popup: {e}")
        return False

def simple_wait_for_page_load(driver, timeout=10):
    """Simple page load wait - SAME AS analyze_proper_flow.py"""
    print(f"[WAIT] Waiting for page to load (max {timeout}s)...")
    
    start_time = time.time()
    stable_count = 0
    
    while time.time() - start_time < timeout:
        try:
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            element_count = len(elements)
            
            if element_count > 5:
                stable_count += 1
                if stable_count >= 2:
                    print(f"[WAIT] ✅ Page loaded with {element_count} elements")

                    # YOUR REQUIREMENT: Check and handle login popup after page load
                    handle_login_popup(driver)
                    return True
            else:
                stable_count = 0
            
            time.sleep(1)
            
        except Exception as e:
            print(f"[WAIT] Error during wait: {e}")
            time.sleep(1)
    
    print(f"[WAIT] ⚠️ Timeout reached, proceeding...")
    return False

def collect_all_elements_simple(driver, page_name="Unknown"):
    """Collect ALL elements on current page with scrolling - EXACT SAME as analyze_proper_flow.py"""
    print(f"\n[COLLECT] 📋 Starting element collection for: {page_name}")

    # YOUR REQUIREMENT: Handle popup before starting collection
    handle_login_popup(driver)

    all_elements = []
    seen_elements = set()
    scroll_count = 0
    max_scrolls = 5
    menu_locations = {}  # Track menu locations - YOUR REQUIREMENT
    
    while scroll_count <= max_scrolls:
        try:
            print(f"[COLLECT] Collecting elements (scroll position {scroll_count}/{max_scrolls})...")
            
            # Get all meaningful elements
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='' or @clickable='true']")
            
            new_elements_found = 0
            for element in elements:
                try:
                    # Get element info
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    clickable = element.get_attribute('clickable') == 'true'
                    class_name = element.get_attribute('class') or ''
                    bounds = element.get_attribute('bounds') or ''

                    # Get location info - YOUR REQUIREMENT
                    location = element.location
                    size = element.size

                    # Create unique identifier - PREVENT DUPLICATES
                    identifier = f"{text}|{desc}|{bounds}|{scroll_count}"

                    # Filter out null/empty elements and duplicates - YOUR REQUIREMENT
                    has_valid_text = text and text != 'null' and text.strip() != ''
                    has_valid_desc = desc and desc != 'null' and desc.strip() != ''

                    # STRICT duplicate prevention
                    if (identifier not in seen_elements and
                        (has_valid_text or has_valid_desc) and
                        f"{text}|{desc}" not in [f"{e['text']}|{e['content_desc']}" for e in all_elements]):
                        seen_elements.add(identifier)
                        
                        element_info = {
                            'text': text,
                            'content_desc': desc,
                            'clickable': clickable,
                            'class': class_name,
                            'bounds': bounds,
                            'page': page_name,
                            'scroll_position': scroll_count,
                            # YOUR REQUIREMENT: Menu location tracking
                            'location': {
                                'x': location['x'],
                                'y': location['y'],
                                'width': size['width'],
                                'height': size['height'],
                                'center_x': location['x'] + size['width'] // 2,
                                'center_y': location['y'] + size['height'] // 2
                            }
                        }
                        
                        all_elements.append(element_info)
                        new_elements_found += 1

                        # Track menu locations - YOUR REQUIREMENT
                        if is_menu_item(text, desc):
                            menu_name = text or desc
                            menu_locations[menu_name] = {
                                'x': location['x'],
                                'y': location['y'],
                                'center_x': location['x'] + size['width'] // 2,
                                'center_y': location['y'] + size['height'] // 2,
                                'width': size['width'],
                                'height': size['height'],
                                'scroll_position': scroll_count,
                                'visible': True
                            }
                            print(f"[LOCATION] 📍 Menu '{menu_name}' found at y={location['y']}, center=({location['x'] + size['width'] // 2}, {location['y'] + size['height'] // 2})")

                        click_indicator = "🔘" if clickable else "⚪"
                        display_text = text or desc or 'No text'
                        print(f"[COLLECT]   {len(all_elements):3d}. {click_indicator} '{display_text[:50]}'")

                except Exception as e:
                    print(f"[COLLECT] Error getting element info: {e}")
                    continue
            
            print(f"[COLLECT] Found {new_elements_found} new elements at scroll position {scroll_count}")
            
            # If no new elements and we've scrolled, we're done
            if new_elements_found == 0 and scroll_count > 0:
                print(f"[COLLECT] ✅ No new elements found, collection complete")
                break
            
            # Scroll down to reveal more elements (if not at max)
            if scroll_count < max_scrolls:
                print(f"[COLLECT] Scrolling down to reveal more elements...")
                simple_safe_scroll_down(driver)
                time.sleep(1)  # Wait for scroll to complete
            
            scroll_count += 1
            
        except Exception as e:
            print(f"[COLLECT] Error during collection: {e}")
            break
    
    print(f"[COLLECT] ✅ Collection complete for {page_name}: {len(all_elements)} total elements")
    print(f"[LOCATION] 📍 Found {len(menu_locations)} menu locations")

    # Return both elements and menu locations - YOUR REQUIREMENT
    return all_elements, menu_locations

def simple_safe_scroll_down(driver):
    """Safe scroll down that avoids pull-to-refresh - SAME AS analyze_proper_flow.py"""
    try:
        size = driver.get_window_size()
        start_x = size['width'] // 2
        start_y = int(size['height'] * 0.6)  # Start from 60% down
        end_y = int(size['height'] * 0.3)    # End at 30% down
        
        driver.swipe(start_x, start_y, start_x, end_y, 1000)
        
    except Exception as e:
        print(f"[SCROLL] Error in safe scroll: {e}")

def is_menu_item(text: str, desc: str) -> bool:
    """Check if element is a menu item - YOUR REQUIREMENT"""
    content = (text + ' ' + desc).lower()

    # Main menu items
    main_menus = [
        'ruang gtk', 'ruang murid', 'ruang sekolah', 'ruang bahasa',
        'ruang pemerintah', 'ruang mitra', 'ruang publik', 'ruang orang tua',
        'sumber belajar', 'pusat perbukuan', 'pengelolaan kinerja'
    ]

    return any(menu in content for menu in main_menus)

def get_current_page_position(driver) -> int:
    """Get current scroll position by analyzing visible elements - YOUR REQUIREMENT"""
    try:
        print(f"[POSITION_CHECK] 🔍 Analyzing current page position...")

        # Get all visible elements
        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")

        # Look for specific indicators to determine position
        position_indicators = {
            0: ["jelajahi beragam", "temukan ruang pendidikan", "ruang gtk", "ruang murid"],  # Top of page
            1: ["ruang gtk", "ruang murid", "ruang sekolah", "ruang bahasa"],  # Main menus visible
            2: ["ruang pemerintah", "ruang mitra", "ruang publik", "ruang orang tua"],  # Secondary menus
            3: ["sumber belajar", "portal pembelajaran", "layanan paling"],  # Services section
            4: ["pusat perbukuan", "portal buku", "siswa, guru"],  # Books section
            5: ["pengelolaan kinerja", "dokumen rujukan", "butuh bantuan"]  # Performance section
        }

        # Check which indicators are visible
        visible_texts = []
        element_positions = []

        for element in elements:
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                location = element.location

                if text and text != 'null':
                    visible_texts.append(text.lower())
                    element_positions.append((text.lower(), location['y']))
                if desc and desc != 'null':
                    visible_texts.append(desc.lower())
                    element_positions.append((desc.lower(), location['y']))
            except:
                continue

        print(f"[POSITION_CHECK] Found {len(visible_texts)} text elements")

        # Determine position based on visible indicators with priority
        position_scores = {}

        for position, indicators in position_indicators.items():
            score = 0
            found_indicators = []

            for indicator in indicators:
                for text in visible_texts:
                    if indicator in text:
                        score += 1
                        found_indicators.append(indicator)
                        break

            if score > 0:
                position_scores[position] = (score, found_indicators)
                print(f"[POSITION_CHECK] Position {position}: score={score}, found={found_indicators}")

        if position_scores:
            # Return position with highest score
            best_position = max(position_scores.keys(), key=lambda p: position_scores[p][0])
            score, indicators = position_scores[best_position]
            print(f"[POSITION_CHECK] ✅ Current position: {best_position} (score={score}, indicators={indicators})")
            return best_position

        # Fallback: analyze Y positions of elements
        if element_positions:
            avg_y = sum(y for _, y in element_positions) / len(element_positions)
            print(f"[POSITION_CHECK] Average Y position: {avg_y}")

            # Estimate position based on Y coordinate
            if avg_y < 800:
                estimated_pos = 0
            elif avg_y < 1200:
                estimated_pos = 1
            elif avg_y < 1600:
                estimated_pos = 2
            elif avg_y < 2000:
                estimated_pos = 3
            else:
                estimated_pos = 4

            print(f"[POSITION_CHECK] ✅ Estimated position from Y coords: {estimated_pos}")
            return estimated_pos

        print(f"[POSITION_CHECK] ⚠️ Could not determine position, assuming 0")
        return 0

    except Exception as e:
        print(f"[POSITION_CHECK] Error checking position: {e}")
        return 0

def dynamic_scroll_to_menu(driver, menu_name: str, menu_locations: dict) -> bool:
    """DYNAMIC scrolling to menu position - YOUR EXACT REQUIREMENT"""
    try:
        print(f"\n[DYNAMIC_SCROLL] 🎯 DYNAMIC SCROLL TO MENU: {menu_name}")
        print(f"[DYNAMIC_SCROLL] Your requirement: 'scroll must be dynamic depend what code needed to click'")

        if menu_name not in menu_locations:
            print(f"[DYNAMIC_SCROLL] ❌ No location data for menu: {menu_name}")
            return False

        menu_location = menu_locations[menu_name]
        target_position = menu_location['scroll_position']

        print(f"[DYNAMIC_SCROLL] 📍 Menu '{menu_name}' should be at scroll position: {target_position}")

        # Step 1: Check current position - YOUR REQUIREMENT
        current_position = get_current_page_position(driver)
        print(f"[DYNAMIC_SCROLL] 📍 Current page position: {current_position}")
        print(f"[DYNAMIC_SCROLL] 📍 Target position: {target_position}")

        # Step 2: Calculate if we need to scroll and in which direction - YOUR REQUIREMENT
        if current_position == target_position:
            print(f"[DYNAMIC_SCROLL] ✅ Already at target position {target_position}")
            return True

        if current_position < target_position:
            # Need to scroll DOWN to reach target
            scroll_direction = "down"
            scroll_steps = target_position - current_position
            print(f"[DYNAMIC_SCROLL] 📍 Need to scroll DOWN {scroll_steps} positions")
        else:
            # Need to scroll UP to reach target
            scroll_direction = "up"
            scroll_steps = current_position - target_position
            print(f"[DYNAMIC_SCROLL] 📍 Need to scroll UP {scroll_steps} positions")

        # Step 3: Perform dynamic scrolling - YOUR REQUIREMENT
        print(f"[DYNAMIC_SCROLL] 🔄 Starting DYNAMIC {scroll_direction.upper()} scroll ({scroll_steps} steps)")

        size = driver.get_window_size()

        for step in range(scroll_steps):
            print(f"[DYNAMIC_SCROLL] Step {step+1}/{scroll_steps}: Scrolling {scroll_direction}...")

            if scroll_direction == "down":
                # Scroll down to reveal lower content
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.6)  # Start from 60%
                end_y = int(size['height'] * 0.4)    # End at 40% (downward swipe)
                driver.swipe(start_x, start_y, start_x, end_y, 800)
            else:
                # Scroll up to reveal upper content
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.4)  # Start from 40%
                end_y = int(size['height'] * 0.6)    # End at 60% (upward swipe)
                driver.swipe(start_x, start_y, start_x, end_y, 800)

            time.sleep(1.5)  # Wait for scroll to complete

            # Check if we've reached the target position
            new_position = get_current_page_position(driver)
            print(f"[DYNAMIC_SCROLL] After step {step+1}: Position {current_position} → {new_position}")

            if new_position == target_position:
                print(f"[DYNAMIC_SCROLL] ✅ Reached target position {target_position} after {step+1} steps!")
                return True

            current_position = new_position

        print(f"[DYNAMIC_SCROLL] ✅ Completed {scroll_steps} dynamic scroll steps")

        # Step 4: Verify menu is now visible - YOUR REQUIREMENT
        try:
            menu_elements = driver.find_elements("xpath", f"//*[@text='{menu_name}' or @content-desc='{menu_name}']")
            if menu_elements:
                print(f"[DYNAMIC_SCROLL] ✅ Menu '{menu_name}' is now visible!")
                return True
            else:
                print(f"[DYNAMIC_SCROLL] ⚠️ Menu '{menu_name}' still not visible, but position reached")
                return True
        except Exception as e:
            print(f"[DYNAMIC_SCROLL] Could not verify menu visibility: {e}")
            return True

    except Exception as e:
        print(f"[DYNAMIC_SCROLL] Error in dynamic scroll: {e}")
        return False

def is_at_top_of_page(driver) -> bool:
    """Check if we're at the top of the page - YOUR REQUIREMENT"""
    try:
        print(f"[TOP_CHECK] 🔍 Checking if at top of page...")

        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
        top_indicators = ["jelajahi beragam", "temukan ruang pendidikan"]

        for element in elements:
            try:
                text = (element.get_attribute('text') or '').lower()
                desc = (element.get_attribute('content-desc') or '').lower()

                if any(indicator in text or indicator in desc for indicator in top_indicators):
                    location = element.location
                    if location['y'] < 600:  # Top indicator is near top of screen
                        print(f"[TOP_CHECK] ✅ At top - found '{text or desc}' at y={location['y']}")
                        return True
            except:
                continue

        print(f"[TOP_CHECK] ❌ Not at top - no top indicators found near top")
        return False

    except Exception as e:
        print(f"[TOP_CHECK] Error checking top: {e}")
        return False

def scroll_to_top_smart(driver) -> bool:
    """Smart scroll to top - PREVENTS PULL-TO-REFRESH - YOUR REQUIREMENT"""
    try:
        print(f"[SMART_TOP] 📍 Smart scroll to top...")

        # Step 1: Check if already at top - YOUR REQUIREMENT
        if is_at_top_of_page(driver):
            print(f"[SMART_TOP] ✅ Already at top - no scroll needed (prevents pull-to-refresh)")
            return True

        print(f"[SMART_TOP] Not at top, need to scroll up...")
        size = driver.get_window_size()

        # Step 2: Scroll to top only if needed - YOUR REQUIREMENT
        for attempt in range(5):  # Up to 5 attempts
            print(f"[SMART_TOP] Scroll to top attempt {attempt+1}/5")

            # Gentle upward swipe - avoid pull-to-refresh zone
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.6)  # Start lower to avoid pull-to-refresh
            end_y = int(size['height'] * 0.9)    # Large upward movement
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(2)

            # Check if we've reached top
            if is_at_top_of_page(driver):
                print(f"[SMART_TOP] ✅ Reached top after {attempt+1} attempts")
                return True

        print(f"[SMART_TOP] ⚠️ Could not reach top after 5 attempts")
        return False

    except Exception as e:
        print(f"[SMART_TOP] Error in smart scroll to top: {e}")
        return False

def scroll_to_position_gentle(driver, target_scroll_position: int) -> bool:
    """Gentle scroll to specific position - PREVENTS CRASHES"""
    try:
        print(f"[GENTLE_SCROLL] 📍 Gently scrolling to position {target_scroll_position}")

        if target_scroll_position == 0:
            return scroll_to_top_smart(driver)

        # For other positions, gentle scroll down step by step
        print(f"[GENTLE_SCROLL] Gentle scroll down {target_scroll_position} positions...")
        try:
            size = driver.get_window_size()

            # First gentle scroll to top
            for i in range(2):  # Reduced scrolls
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.5)
                end_y = int(size['height'] * 0.8)
                driver.swipe(start_x, start_y, start_x, end_y, 800)
                time.sleep(2)

            # Then gentle scroll down to target position
            for i in range(target_scroll_position):
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.6)  # Less aggressive
                end_y = int(size['height'] * 0.4)    # Smaller scroll distance
                driver.swipe(start_x, start_y, start_x, end_y, 800)  # Slower
                time.sleep(2)  # Longer wait between scrolls
                print(f"[GENTLE_SCROLL] Gentle scroll down {i+1}/{target_scroll_position}")

            print(f"[GENTLE_SCROLL] ✅ Reached scroll position {target_scroll_position}")
            return True

        except Exception as e:
            print(f"[GENTLE_SCROLL] Error in gentle position scroll: {e}")
            return False

    except Exception as e:
        print(f"[GENTLE_SCROLL] Error in gentle scroll: {e}")
        return False

def check_server_health(driver) -> bool:
    """Check if UiAutomator2 server is healthy - PREVENTS CRASHES"""
    try:
        # Simple health check
        driver.find_elements("xpath", "//*")
        return True
    except Exception as e:
        print(f"[HEALTH] ❌ Server unhealthy: {e}")
        return False

def detect_context_and_app(driver) -> tuple:
    """Detect current context and app - PREVENTS CHROME OPENING"""
    try:
        # Check if we're in Chrome or external browser
        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")

        chrome_indicators = ["welcome to chrome", "sign in to browse", "add account to device", "use without an account"]
        browser_indicators = ["browser", "chrome", "firefox", "safari"]

        for element in elements:
            try:
                text = (element.get_attribute('text') or '').lower()
                desc = (element.get_attribute('content-desc') or '').lower()

                if any(indicator in text or indicator in desc for indicator in chrome_indicators):
                    print(f"[CONTEXT] ❌ Detected Chrome browser - external navigation")
                    return ("CHROME", "com.android.chrome")

                if any(indicator in text or indicator in desc for indicator in browser_indicators):
                    print(f"[CONTEXT] ❌ Detected external browser")
                    return ("BROWSER", "external")

            except:
                continue

        # Check current package
        try:
            current_activity = driver.current_activity
            if "chrome" in current_activity.lower():
                print(f"[CONTEXT] ❌ In Chrome activity: {current_activity}")
                return ("CHROME", "com.android.chrome")
        except:
            pass

        print(f"[CONTEXT] ✅ In main app context")
        return ("APP", "com.kemendikdasmen.rumahpendidikan")

    except Exception as e:
        print(f"[CONTEXT] Error detecting context: {e}")
        return ("UNKNOWN", "unknown")

def simple_smart_click_menu(driver, menu_name, menu_locations=None):
    """Simple smart click menu with crash prevention - YOUR REQUIREMENT"""
    print(f"\n[CLICK] 🎯 Attempting to click menu: {menu_name}")

    # Step 0: Health check - PREVENT CRASHES
    if not check_server_health(driver):
        print(f"[CLICK] ❌ Server unhealthy, skipping menu: {menu_name}")
        return False

    # Step 1: Scroll to menu position if location is known - YOUR REQUIREMENT
    if menu_locations and menu_name in menu_locations:
        menu_location = menu_locations[menu_name]
        scroll_position = menu_location.get('scroll_position', 0)

        print(f"[CLICK] 📍 Using known location for {menu_name}")
        print(f"[CLICK] Menu found at scroll position {scroll_position}, y={menu_location['y']}")

        # Method 1: Smart scroll to top if needed - YOUR REQUIREMENT
        if scroll_position == 0:
            print(f"[CLICK] 🔄 Smart scrolling to top for position 0...")
            if not scroll_to_top_smart(driver):
                print(f"[CLICK] ⚠️ Could not scroll to top")
            time.sleep(2)
        else:
            # Method 2: DYNAMIC scroll to menu position - YOUR EXACT REQUIREMENT
            print(f"[CLICK] 🔄 Using DYNAMIC scrolling to reach menu...")
            try:
                if dynamic_scroll_to_menu(driver, menu_name, menu_locations):
                    print(f"[CLICK] ✅ Dynamic scroll successful - menu should be visible")
                    time.sleep(2)  # Wait for scroll to settle
                else:
                    print(f"[CLICK] ⚠️ Dynamic scroll failed, trying gentle fallback...")
                    # Fallback to gentle scrolling
                    if scroll_to_position_gentle(driver, scroll_position):
                        print(f"[CLICK] ✅ Gentle fallback successful")
                        time.sleep(2)
                    else:
                        print(f"[CLICK] ⚠️ All scroll methods failed, trying direct click...")
            except Exception as e:
                print(f"[CLICK] ⚠️ Scroll failed: {e}, trying direct click...")
    else:
        print(f"[CLICK] ⚠️ No location data for {menu_name}, trying direct click...")
    
    # Try multiple patterns and methods
    patterns = [
        f"//*[@text='{menu_name}' or @content-desc='{menu_name}']",
        f"//*[contains(@text, '{menu_name}') or contains(@content-desc, '{menu_name}')]",
        f"//android.widget.ImageView[@content-desc='{menu_name}']",
        f"//android.widget.TextView[@text='{menu_name}']",
        f"//*[@clickable='true' and (@text='{menu_name}' or @content-desc='{menu_name}')]"
    ]
    
    for i, pattern in enumerate(patterns, 1):
        try:
            print(f"[CLICK] Trying pattern {i}: {pattern[:60]}...")
            elements = driver.find_elements("xpath", pattern)
            
            if elements:
                element = elements[0]
                print(f"[CLICK] Found element, attempting click...")
                
                # Method 1: Regular click
                try:
                    element.click()
                    time.sleep(2)  # Wait for navigation
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (regular click)")
                    return True
                except:
                    pass
                
                # Method 2: Tap using coordinates
                try:
                    location = element.location
                    size = element.size
                    x = location['x'] + size['width'] // 2
                    y = location['y'] + size['height'] // 2
                    
                    driver.tap([(x, y)])
                    time.sleep(2)
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (tap)")
                    return True
                except:
                    pass
                
        except Exception as e:
            print(f"[CLICK] Pattern {i} failed: {e}")
            continue
    
    print(f"[CLICK] ❌ Could not click {menu_name}")
    return False

def simple_go_back(driver):
    """Go back to previous page - SAME AS analyze_proper_flow.py"""
    try:
        print("\n[BACK] 🔙 Going back to previous page...")
        
        # Method 1: Back button
        try:
            driver.back()
            time.sleep(2)
            print("[BACK] ✅ Used back button")
            return True
        except:
            pass
        
        # Method 2: Home navigation
        try:
            # Look for home/main indicators
            home_patterns = [
                "//*[@content-desc='Home' or @text='Home']",
                "//*[contains(@content-desc, 'Beranda') or contains(@text, 'Beranda')]",
                "//*[@content-desc='Navigate up' or @text='Navigate up']"
            ]
            
            for pattern in home_patterns:
                elements = driver.find_elements("xpath", pattern)
                if elements:
                    elements[0].click()
                    time.sleep(2)
                    print("[BACK] ✅ Used home navigation")
                    return True
        except:
            pass
        
        print("[BACK] ⚠️ Could not navigate back")
        return False
        
    except Exception as e:
        print(f"[BACK] Error going back: {e}")
        return False

def find_submenu_items_simple(driver):
    """Find potential submenu items on current page - YOUR REQUIREMENT"""
    print(f"\n[SUBMENU] 🔍 Finding submenu items...")

    submenu_items = []

    try:
        # Get all clickable elements
        clickable_elements = driver.find_elements("xpath", "//*[@clickable='true']")

        for element in clickable_elements:
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                class_name = element.get_attribute('class') or ''

                # Filter out navigation elements and main menus
                if is_potential_submenu_simple(text, desc, class_name):
                    submenu_items.append({
                        'element': element,
                        'text': text,
                        'content_desc': desc,
                        'class': class_name
                    })

            except Exception as e:
                continue

        print(f"[SUBMENU] Found {len(submenu_items)} potential submenu items")
        return submenu_items[:10]  # Limit to first 10 to avoid too many

    except Exception as e:
        print(f"[SUBMENU] Error finding submenu items: {e}")
        return []

def is_potential_submenu_simple(text: str, desc: str, class_name: str) -> bool:
    """Check if element is a potential submenu item - YOUR REQUIREMENT"""
    # Skip empty elements
    if not text.strip() and not desc.strip():
        return False

    # Skip null elements - YOUR REQUIREMENT
    if text == 'null' or desc == 'null':
        return False

    # Skip navigation elements
    nav_keywords = ['beranda', 'home', 'back', 'kembali', 'tab', 'navigation']
    content = (text + ' ' + desc).lower()
    if any(keyword in content for keyword in nav_keywords):
        return False

    # Skip main menu items (they should be on main page)
    main_menus = ['ruang gtk', 'ruang murid', 'ruang sekolah', 'ruang bahasa']
    if any(menu in content for menu in main_menus):
        return False

    # Include if has meaningful content
    if len(text.strip()) > 2 or len(desc.strip()) > 2:
        return True

    return False

def crawl_submenus_simple(driver, parent_menu: str, submenu_locations=None, depth: int = 1):
    """Crawl through submenus hierarchically with location awareness - YOUR REQUIREMENT"""
    try:
        print(f"\n[SUBMENU_CRAWL] 📂 Starting submenu crawl for {parent_menu} at depth {depth}")

        # YOUR REQUIREMENT 1: At depth 3+ (sub-submenus), ONLY COLLECT - NO CLICKING
        if depth >= 3:
            print(f"[SUBMENU_CRAWL] 📋 At sub-submenu level (depth {depth}) - COLLECT ONLY, NO CLICKING")
            # Just collect elements, don't click any menus
            elements, locations = collect_all_elements_simple(driver, f"{parent_menu}_FINAL_COLLECTION")
            print(f"[SUBMENU_CRAWL] ✅ Final collection: {len(elements)} elements from {parent_menu}")
            return

        if depth > 3:  # Limit depth to prevent infinite recursion
            print(f"[SUBMENU_CRAWL] Max depth reached for {parent_menu}")
            return

        # YOUR REQUIREMENT 2: Scroll back to top before processing submenus
        print(f"[SUBMENU_CRAWL] 🔄 Ensuring at top before processing submenus...")
        scroll_to_top_smart(driver)

        # Find all potential submenu items
        submenus = find_submenu_items_simple(driver)

        # If we have location data, prioritize submenus with known locations - YOUR REQUIREMENT
        if submenu_locations:
            print(f"[SUBMENU_CRAWL] 📍 Using location data for {len(submenu_locations)} potential submenus")
            for submenu_name, location in submenu_locations.items():
                print(f"[SUBMENU_CRAWL]   - {submenu_name}: y={location['y']}")

        if not submenus:
            print(f"[SUBMENU_CRAWL] No submenus found in {parent_menu}")
            return

        print(f"[SUBMENU_CRAWL] Found {len(submenus)} potential submenus in {parent_menu}")

        # Navigate through each submenu
        for i, submenu_info in enumerate(submenus, 1):
            try:
                # Extract clean submenu name - FIXES EMPTY NAMES
                submenu_name = submenu_info.get('text', submenu_info.get('content_desc', ''))
                if not submenu_name or submenu_name.strip() == "":
                    submenu_name = f'Submenu_{i}'

                # Truncate long names
                if len(submenu_name) > 50:
                    submenu_name = submenu_name[:47] + "..."

                print(f"\n[SUBMENU_CRAWL] Clicking submenu {i}/{len(submenus)}: {submenu_name}")

                # Click the submenu
                if click_submenu_item_simple(driver, submenu_info):
                    # Wait for page to load
                    simple_wait_for_page_load(driver, timeout=5)

                    # Check context after navigation - PREVENT CHROME COLLECTION
                    context, app = detect_context_and_app(driver)
                    if context == "CHROME" or context == "BROWSER":
                        print(f"[SUBMENU_CRAWL] ❌ In external browser, skipping collection and going back...")
                        simple_go_back(driver)
                        simple_wait_for_page_load(driver, timeout=5)
                        continue

                    # Collect elements in submenu
                    submenu_elements, sub_submenu_locations = collect_all_elements_simple(driver, f"{parent_menu} > {submenu_name}")
                    print(f"[SUBMENU_CRAWL] Collected {len(submenu_elements)} elements from {submenu_name}")

                    # YOUR REQUIREMENT 2: Scroll back to top after collection in submenu
                    print(f"[SUBMENU_CRAWL] 🔄 Scrolling back to top after collection...")
                    scroll_to_top_smart(driver)

                    # Recursively crawl sub-submenus - YOUR REQUIREMENT
                    crawl_submenus_simple(driver, f"{parent_menu} > {submenu_name}", sub_submenu_locations, depth + 1)

                    # Go back to parent menu
                    simple_go_back(driver)
                    simple_wait_for_page_load(driver, timeout=5)

                else:
                    print(f"[SUBMENU_CRAWL] Could not click submenu: {submenu_name}")

            except Exception as e:
                print(f"[SUBMENU_CRAWL] Error processing submenu {i}: {e}")
                # Try to recover by going back
                simple_go_back(driver)
                time.sleep(1)
                continue

        print(f"[SUBMENU_CRAWL] ✅ Completed submenu crawl for {parent_menu}")

    except Exception as e:
        print(f"[SUBMENU_CRAWL] Error in submenu crawl for {parent_menu}: {e}")

def click_submenu_item_simple(driver, submenu_info: dict) -> bool:
    """Click a submenu item with context detection - YOUR REQUIREMENT"""
    try:
        element = submenu_info['element']
        name = submenu_info.get('text', submenu_info.get('content_desc', 'Unknown'))

        # Fix empty names
        if not name or name.strip() == "" or name == "Unknown":
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                name = text if text and text != 'null' else desc
                if not name or name == 'null':
                    name = f"Element_{element.location['x']}_{element.location['y']}"
            except:
                name = "UnknownElement"

        print(f"[CLICK_SUBMENU] Attempting to click: {name}")

        # Method 1: Regular click
        try:
            element.click()
            time.sleep(3)  # Longer wait for page load

            # YOUR REQUIREMENT: Handle popup after click
            handle_login_popup(driver)

            # Check context after click - PREVENT CHROME OPENING
            context, app = detect_context_and_app(driver)
            if context == "CHROME" or context == "BROWSER":
                print(f"[CLICK_SUBMENU] ❌ Opened external browser, going back...")
                simple_go_back(driver)
                time.sleep(2)
                return False

            print(f"[CLICK_SUBMENU] ✅ Successfully clicked submenu: {name}")
            return True
        except:
            pass

        # Method 2: Tap using coordinates
        try:
            location = element.location
            size = element.size
            x = location['x'] + size['width'] // 2
            y = location['y'] + size['height'] // 2

            driver.tap([(x, y)])
            time.sleep(3)  # Longer wait for page load

            # Check context after tap - PREVENT CHROME OPENING
            context, app = detect_context_and_app(driver)
            if context == "CHROME" or context == "BROWSER":
                print(f"[CLICK_SUBMENU] ❌ Opened external browser, going back...")
                simple_go_back(driver)
                time.sleep(2)
                return False

            print(f"[CLICK_SUBMENU] ✅ Successfully clicked submenu: {name} (tap)")
            return True
        except:
            pass

        print(f"[CLICK_SUBMENU] ❌ Failed to click submenu: {name}")
        return False

    except Exception as e:
        print(f"[CLICK_SUBMENU] Error clicking submenu: {e}")
        return False

def go_back_to_main_simple(driver):
    """Navigate back to main page from any submenu - YOUR REQUIREMENT"""
    try:
        print("\n[NAVIGATE_MAIN] 🏠 Navigating back to main page...")

        max_attempts = 3
        for attempt in range(max_attempts):
            # Try to go back
            if simple_go_back(driver):
                time.sleep(2)

                # Check if we're on main page by looking for main menu items
                main_indicators = [
                    "//*[@text='Ruang GTK']",
                    "//*[@text='Ruang Murid']",
                    "//*[@text='Ruang Sekolah']"
                ]

                main_elements_found = 0
                for indicator in main_indicators:
                    try:
                        elements = driver.find_elements("xpath", indicator)
                        if elements:
                            main_elements_found += 1
                    except:
                        continue

                if main_elements_found >= 2:
                    print(f"[NAVIGATE_MAIN] ✅ Successfully reached main page (attempt {attempt + 1})")

                    # YOUR REQUIREMENT: Go back to top after completing menu
                    print(f"[NAVIGATE_MAIN] 🔄 Ensuring we're at top for next menu...")
                    scroll_to_top_smart(driver)

                    return True
                else:
                    print(f"[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt {attempt + 1})")
            else:
                print(f"[NAVIGATE_MAIN] Back navigation failed (attempt {attempt + 1})")

        print("[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts")
        return False

    except Exception as e:
        print(f"[NAVIGATE_MAIN] Error navigating to main: {e}")
        return False

def run_hierarchical_crawl_simple(driver):
    """Run the complete hierarchical crawl - YOUR EXACT REQUIREMENT"""
    print("\n" + "="*80)
    print("🎯 STARTING HIERARCHICAL CRAWL - YOUR EXACT FLOW")
    print("="*80)
    print("Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu")
    print("="*80)

    all_collected_data = {}

    try:
        # Step 1: Collect all elements on main page first
        print("\n📋 STEP 1: Collecting all elements on MAIN PAGE")
        main_page_elements, main_menu_locations = collect_all_elements_simple(driver, "Main Page")
        all_collected_data["Main Page"] = main_page_elements

        # Store menu locations for smart clicking - YOUR REQUIREMENT
        print(f"[LOCATION] 📍 Stored locations for {len(main_menu_locations)} menus")
        for menu_name, location in main_menu_locations.items():
            print(f"[LOCATION]   - {menu_name}: y={location['y']}, center=({location['center_x']}, {location['center_y']})")

        # Step 2: Find all main menus
        main_menus = [
            "Ruang GTK", "Ruang Murid", "Ruang Sekolah", "Ruang Bahasa",
            "Ruang Pemerintah", "Ruang Mitra", "Ruang Publik", "Ruang Orang Tua"
        ]

        print(f"\n🎯 STEP 2: Found {len(main_menus)} main menus to crawl")

        # YOUR REQUIREMENT 3: Menu progress tracking system
        menu_progress = {
            'total_menus': len(main_menus),
            'completed_menus': [],
            'failed_menus': [],
            'current_menu': None,
            'remaining_menus': main_menus.copy()
        }

        print(f"\n📊 MENU PROGRESS TRACKING INITIALIZED:")
        print(f"  - Total menus to process: {menu_progress['total_menus']}")
        print(f"  - Remaining menus: {menu_progress['remaining_menus']}")

        # Step 3: Click each menu and follow your exact flow - WITH PROGRESS TRACKING
        for menu_index, menu_name in enumerate(main_menus, 1):
            try:
                # YOUR REQUIREMENT 3: Update progress tracking
                menu_progress['current_menu'] = menu_name
                print(f"\n" + "="*60)
                print(f"🎯 MENU {menu_index}/{len(main_menus)}: {menu_name}")
                print(f"📊 PROGRESS: Completed={len(menu_progress['completed_menus'])}, Failed={len(menu_progress['failed_menus'])}, Remaining={len(menu_progress['remaining_menus'])}")
                print("="*60)

                # Health check before each menu - PREVENT CRASHES
                if not check_server_health(driver):
                    print(f"❌ Server unhealthy before menu {menu_name}, skipping...")
                    menu_progress['failed_menus'].append(menu_name)
                    menu_progress['remaining_menus'].remove(menu_name)
                    continue

                # YOUR REQUIREMENT: "when click menu 1 in main page will directly into another page or submenu"
                # Use menu location for smart clicking - YOUR REQUIREMENT
                click_success = simple_smart_click_menu(driver, menu_name, main_menu_locations)

                if click_success:
                    # Wait for page to load
                    simple_wait_for_page_load(driver, timeout=10)

                    # Check context after main menu click - PREVENT CHROME
                    context, app = detect_context_and_app(driver)
                    if context == "CHROME" or context == "BROWSER":
                        print(f"❌ Main menu {menu_name} opened external browser, going back...")
                        simple_go_back(driver)
                        menu_progress['failed_menus'].append(menu_name)
                        menu_progress['remaining_menus'].remove(menu_name)
                        continue

                    # Health check after navigation
                    if not check_server_health(driver):
                        print(f"❌ Server unhealthy after clicking {menu_name}, going back...")
                        simple_go_back(driver)
                        menu_progress['failed_menus'].append(menu_name)
                        menu_progress['remaining_menus'].remove(menu_name)
                        continue

                    # YOUR REQUIREMENT: "after that collect all element locators in the other page"
                    # YOUR REQUIREMENT: "and also you need to scroll down step by step to make element visible"
                    try:
                        menu_elements, submenu_locations = collect_all_elements_simple(driver, f"Menu: {menu_name}")
                        all_collected_data[f"Menu: {menu_name}"] = menu_elements

                        # Store submenu locations for hierarchical navigation - YOUR REQUIREMENT
                        print(f"[LOCATION] 📍 Found {len(submenu_locations)} submenu locations in {menu_name}")

                        # YOUR REQUIREMENT: "when all element locators for submenu already collected"
                        # YOUR REQUIREMENT: "the code need to scroll to top and start click the submenu 1, 2, 3, n"
                        print(f"\n🔍 Starting submenu crawl for {menu_name}...")
                        crawl_submenus_simple(driver, menu_name, submenu_locations)

                    except Exception as collection_error:
                        print(f"❌ Error collecting elements from {menu_name}: {collection_error}")

                    # YOUR REQUIREMENT: "after this you need to go back to main page"
                    print(f"\n🔙 Going back to main page from {menu_name}...")
                    go_back_to_main_simple(driver)

                    # Health check after going back
                    if not check_server_health(driver):
                        print(f"❌ Server unhealthy after going back from {menu_name}")
                        break  # Stop processing if server is unstable

                    # YOUR REQUIREMENT 3: Mark menu as completed
                    menu_progress['completed_menus'].append(menu_name)
                    menu_progress['remaining_menus'].remove(menu_name)
                    print(f"✅ Completed crawling menu: {menu_name}")
                    print(f"📊 UPDATED PROGRESS: {len(menu_progress['completed_menus'])}/{menu_progress['total_menus']} menus completed")

                else:
                    print(f"❌ Could not click menu: {menu_name}")
                    menu_progress['failed_menus'].append(menu_name)
                    menu_progress['remaining_menus'].remove(menu_name)

            except Exception as e:
                print(f"❌ Error processing menu {menu_name}: {e}")
                menu_progress['failed_menus'].append(menu_name)
                if menu_name in menu_progress['remaining_menus']:
                    menu_progress['remaining_menus'].remove(menu_name)
                # Try to recover by going back to main
                try:
                    go_back_to_main_simple(driver)
                except:
                    print(f"❌ Could not recover from error in {menu_name}")
                    break  # Stop if we can't recover
                continue

        # YOUR REQUIREMENT 3: Final progress summary
        print(f"\n📊 FINAL MENU PROGRESS SUMMARY:")
        print(f"  - Total menus: {menu_progress['total_menus']}")
        print(f"  - Successfully completed: {len(menu_progress['completed_menus'])}")
        print(f"  - Failed: {len(menu_progress['failed_menus'])}")
        print(f"  - Remaining: {len(menu_progress['remaining_menus'])}")
        print(f"  - Completed menus: {menu_progress['completed_menus']}")
        print(f"  - Failed menus: {menu_progress['failed_menus']}")
        print(f"  - Remaining menus: {menu_progress['remaining_menus']}")

        # Step 4: Save all collected data
        save_collected_data_simple(all_collected_data)

        print("\n" + "="*80)
        print("🎉 HIERARCHICAL CRAWL COMPLETE!")
        print("="*80)
        print(f"✅ Crawled {len(main_menus)} main menus")
        print(f"✅ Collected data from {len(all_collected_data)} pages")
        print(f"✅ Followed your exact flow: Main → Menu → Submenu → Sub-submenu → Back")
        print("="*80)

        return all_collected_data

    except Exception as e:
        print(f"❌ Error in hierarchical crawl: {e}")
        return all_collected_data

def save_collected_data_simple(all_data):
    """Save collected data to JSON file"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"locators/simplified_hierarchical_collection_{timestamp}.json"

        # Create locators directory if it doesn't exist
        os.makedirs("locators", exist_ok=True)

        # Prepare data for saving
        save_data = {
            "timestamp": timestamp,
            "collection_type": "hierarchical_simplified_with_locations",
            "total_pages": len(all_data),
            "pages": {},
            "features": [
                "Menu location tracking",
                "Smart scroll to menu position",
                "Hierarchical navigation",
                "Null element filtering",
                "Step-by-step scrolling"
            ]
        }

        total_elements = 0
        for page_name, elements in all_data.items():
            save_data["pages"][page_name] = {
                "element_count": len(elements),
                "elements": elements
            }
            total_elements += len(elements)

        save_data["total_elements"] = total_elements

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Data saved to: {filename}")
        print(f"📊 Total elements collected: {total_elements}")

    except Exception as e:
        print(f"❌ Error saving data: {e}")

def main():
    """Main function - SIMPLIFIED VERSION WITH CRASH PREVENTION"""
    print("🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT")
    print("Based on analyze_proper_flow.py pattern")
    print("Implements your exact hierarchical navigation flow")
    print("Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown")
    print("="*70)

    driver = None
    try:
        # Load config
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"

        print("\n📱 Starting Appium session...")
        # Start simple Appium session
        driver = start_simple_appium_session(package)

        print("⏳ Waiting for app to load...")
        # Wait for app to load
        simple_wait_for_page_load(driver, timeout=15)

        print("🎯 Starting hierarchical crawl...")
        # Run hierarchical crawl with your exact flow
        collected_data = run_hierarchical_crawl_simple(driver)

        print("\n🎉 SIMPLIFIED ANALYSIS COMPLETE!")
        print(f"✅ Successfully collected data from {len(collected_data)} pages")

        # Save final results
        save_collected_data_simple(collected_data)

        print("\n✅ ALL PROCESSING COMPLETE - NO CRASHES!")
        print("Press Enter to close session...")
        input()

    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"❌ Error in main: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Always try to close driver safely
        if driver:
            try:
                print("\n🔄 Closing Appium session...")
                driver.quit()
                print("✅ Session closed successfully")
            except Exception as close_error:
                print(f"⚠️ Error closing session: {close_error}")

        print("\n🎯 ANALYSIS FINISHED")
        print("Check the logs for any issues that need attention")

if __name__ == "__main__":
    main()
