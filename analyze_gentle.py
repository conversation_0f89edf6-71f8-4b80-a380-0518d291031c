#!/usr/bin/env python3

"""
Gentle Android App Analyzer - Conservative approach to prevent crashes
"""

import os
import sys
import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def gentle_appium_session(package):
    """Start Appium session with very conservative settings"""
    print(f"[GENTLE] Starting conservative Appium session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    # Ultra-conservative settings to prevent crashes
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    options.set_capability('disableWindowAnimation', True)  # Reduce animations
    options.set_capability('waitForIdleTimeout', 1000)  # Shorter idle wait
    options.set_capability('waitForSelectorTimeout', 5000)  # Shorter selector wait
    
    print("[GENTLE] Connecting to Appium server...")
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(5)  # Shorter implicit wait
    
    print("✅ Conservative Appium session started!")
    return driver

def gentle_wait_for_stability(driver, timeout=30):
    """Conservative wait that doesn't stress the app"""
    print(f"[GENTLE_WAIT] Waiting for app stability (max {timeout}s)...")
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            # Very simple stability check
            elements = driver.find_elements("xpath", "//*[@content-desc!='' or @text!='']")
            element_count = len(elements)
            
            print(f"[GENTLE_WAIT] Found {element_count} elements")
            
            if element_count > 5:  # Minimal threshold
                print(f"[GENTLE_WAIT] ✅ App appears stable with {element_count} elements")
                return True
            
            time.sleep(3)  # Longer pause between checks
            
        except Exception as e:
            print(f"[GENTLE_WAIT] Error during gentle wait: {e}")
            time.sleep(3)
            continue
    
    print(f"[GENTLE_WAIT] ⚠️ Timeout reached, proceeding anyway...")
    return False

def gentle_element_collection(driver):
    """Very conservative element collection"""
    print("[GENTLE] Starting conservative element collection...")
    
    try:
        # Wait for stability first
        gentle_wait_for_stability(driver, timeout=30)
        
        # Get basic page info
        try:
            current_activity = driver.current_activity
            print(f"[GENTLE] Current activity: {current_activity}")
        except Exception as e:
            print(f"[GENTLE] Could not get activity: {e}")
        
        # Collect elements very conservatively
        print("[GENTLE] Collecting elements...")
        
        # Method 1: Get all elements with text or content-desc
        try:
            text_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"[GENTLE] Found {len(text_elements)} elements with text/desc")
            
            # Show first 10 elements
            print("[GENTLE] First 10 elements:")
            for i, element in enumerate(text_elements[:10]):
                try:
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    clickable = element.get_attribute('clickable') == 'true'
                    
                    click_indicator = "🔘" if clickable else "⚪"
                    display_text = text or desc or 'No text'
                    print(f"  {i+1:2d}. {click_indicator} '{display_text[:40]}'")
                    
                except Exception as e:
                    print(f"  {i+1:2d}. Error getting element info: {e}")
                    
        except Exception as e:
            print(f"[GENTLE] Error collecting text elements: {e}")
        
        # Method 2: Look for specific menu items (very gently)
        print("\n[GENTLE] Looking for menu items...")
        
        menu_patterns = [
            "Ruang GTK",
            "Ruang Murid", 
            "Ruang Sekolah",
            "Ruang Bahasa"
        ]
        
        found_menus = []
        for menu in menu_patterns:
            try:
                elements = driver.find_elements("xpath", f"//*[@content-desc='{menu}' or @text='{menu}']")
                if elements:
                    found_menus.append(menu)
                    print(f"[GENTLE]   ✅ Found: {menu}")
                else:
                    print(f"[GENTLE]   ❌ Not found: {menu}")
                    
                time.sleep(1)  # Gentle pause between searches
                
            except Exception as e:
                print(f"[GENTLE]   Error searching for {menu}: {e}")
        
        print(f"\n[GENTLE] ✅ Collection complete!")
        print(f"[GENTLE] Found {len(found_menus)} menu items: {', '.join(found_menus)}")
        
        return {
            'total_elements': len(text_elements) if 'text_elements' in locals() else 0,
            'found_menus': found_menus,
            'status': 'success'
        }
        
    except Exception as e:
        print(f"[GENTLE] ❌ Collection failed: {e}")
        return {
            'total_elements': 0,
            'found_menus': [],
            'status': 'failed',
            'error': str(e)
        }

def main():
    """Gentle main function that avoids aggressive operations"""
    print("=" * 60)
    print("GENTLE ANDROID APP ANALYZER")
    print("Conservative approach to prevent crashes")
    print("=" * 60)
    
    try:
        # Load config
        print("[GENTLE] Loading configuration...")
        config = load_config()
        
        # Use known package
        package = "com.kemendikdasmen.rumahpendidikan"
        print(f"[GENTLE] Using package: {package}")
        
        # Start conservative Appium session
        driver = gentle_appium_session(package)
        
        # Do gentle element collection
        results = gentle_element_collection(driver)
        
        print(f"\n[GENTLE] ✅ Analysis complete!")
        print(f"[GENTLE] Results: {results}")
        
        # Keep session open for inspection
        print("\n[GENTLE] Session ready for manual inspection...")
        print("Press Enter to close session...")
        input()
        
        # Close session
        driver.quit()
        print("[GENTLE] Session closed gracefully.")
        
    except Exception as e:
        print(f"[GENTLE] ❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
