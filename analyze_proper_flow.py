#!/usr/bin/env python3

"""
Proper Flow Android App Analyzer - Follows the correct collection flow:
1. Collect ALL element locators on main page first
2. Then click menus systematically one by one
3. For each menu: collect elements, scroll, collect submenus, navigate back
"""

import os
import sys
import time
import yaml
import json
from datetime import datetime
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def start_proper_appium_session(package):
    """Start Appium session with proper settings"""
    print(f"[PROPER] Starting Appium session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    # Prevent app restart
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    print("[PROPER] Connecting to Appium server...")
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)
    
    print("✅ Proper Appium session started!")
    return driver

def wait_for_page_load(driver, timeout=15):
    """Wait for page to load completely"""
    print(f"[WAIT] Waiting for page to load (max {timeout}s)...")
    
    start_time = time.time()
    stable_count = 0
    
    while time.time() - start_time < timeout:
        try:
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            element_count = len(elements)
            
            if element_count > 5:
                stable_count += 1
                if stable_count >= 2:  # 2 consecutive stable checks
                    print(f"[WAIT] ✅ Page loaded with {element_count} elements")
                    return True
            else:
                stable_count = 0
            
            time.sleep(2)
            
        except Exception as e:
            print(f"[WAIT] Error during wait: {e}")
            time.sleep(2)
    
    print(f"[WAIT] ⚠️ Timeout reached, proceeding...")
    return False

def collect_all_elements_on_page(driver, page_name="Unknown"):
    """Collect ALL elements on current page with scrolling"""
    print(f"\n[COLLECT] 📋 Starting element collection for: {page_name}")
    
    all_elements = []
    seen_elements = set()
    scroll_count = 0
    max_scrolls = 5
    
    while scroll_count <= max_scrolls:
        try:
            print(f"[COLLECT] Collecting elements (scroll position {scroll_count}/{max_scrolls})...")
            
            # Get all meaningful elements
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='' or @clickable='true']")
            
            new_elements_found = 0
            for element in elements:
                try:
                    # Get element info
                    text = element.get_attribute('text') or ''
                    desc = element.get_attribute('content-desc') or ''
                    clickable = element.get_attribute('clickable') == 'true'
                    class_name = element.get_attribute('class') or ''
                    bounds = element.get_attribute('bounds') or ''
                    
                    # Create unique identifier
                    identifier = f"{text}|{desc}|{bounds}"

                    # Filter out null/empty elements and duplicates
                    has_valid_text = text and text != 'null' and text.strip() != ''
                    has_valid_desc = desc and desc != 'null' and desc.strip() != ''

                    if (identifier not in seen_elements and
                        (has_valid_text or has_valid_desc)):
                        seen_elements.add(identifier)
                        
                        element_info = {
                            'text': text,
                            'content_desc': desc,
                            'clickable': clickable,
                            'class': class_name,
                            'bounds': bounds,
                            'page': page_name,
                            'scroll_position': scroll_count
                        }
                        
                        all_elements.append(element_info)
                        new_elements_found += 1
                        
                        click_indicator = "🔘" if clickable else "⚪"
                        display_text = text or desc or 'No text'
                        print(f"[COLLECT]   {len(all_elements):3d}. {click_indicator} '{display_text[:50]}'")
                        
                except Exception as e:
                    print(f"[COLLECT] Error getting element info: {e}")
                    continue
            
            print(f"[COLLECT] Found {new_elements_found} new elements at scroll position {scroll_count}")
            
            # If no new elements and we've scrolled, we're done
            if new_elements_found == 0 and scroll_count > 0:
                print(f"[COLLECT] ✅ No new elements found, collection complete")
                break
            
            # Scroll down to reveal more elements (if not at max)
            if scroll_count < max_scrolls:
                print(f"[COLLECT] Scrolling down to reveal more elements...")
                safe_scroll_down(driver)
                # Mark that we've scrolled down
                page_tracker.mark_scrolled_down()
                time.sleep(2)  # Wait for scroll to complete
            
            scroll_count += 1
            
        except Exception as e:
            print(f"[COLLECT] Error during collection: {e}")
            break
    
    print(f"[COLLECT] ✅ Collection complete for {page_name}: {len(all_elements)} total elements")
    return all_elements

def safe_scroll_down(driver):
    """Safe scroll down that avoids pull-to-refresh"""
    try:
        size = driver.get_window_size()
        start_x = size['width'] // 2
        start_y = int(size['height'] * 0.6)  # Start from 60% down
        end_y = int(size['height'] * 0.3)    # End at 30% down
        
        driver.swipe(start_x, start_y, start_x, end_y, 1000)
        
    except Exception as e:
        print(f"[SCROLL] Error in safe scroll: {e}")

class PageTracker:
    """Track page navigation and scroll state"""
    def __init__(self):
        self.has_scrolled_down = False
        self.current_page = "MAIN"
        self.page_just_opened = True

    def mark_page_opened(self, page_name):
        """Mark that a new page was just opened"""
        self.current_page = page_name
        self.page_just_opened = True
        self.has_scrolled_down = False
        print(f"[TRACKER] New page opened: {page_name} - At top by default")

    def mark_scrolled_down(self):
        """Mark that user has scrolled down"""
        self.has_scrolled_down = True
        self.page_just_opened = False
        print(f"[TRACKER] Scrolled down on {self.current_page}")

    def is_at_top(self):
        """Determine if we're at top based on navigation logic"""
        if self.page_just_opened:
            print(f"[TRACKER] ✅ At top - {self.current_page} just opened")
            return True
        elif not self.has_scrolled_down:
            print(f"[TRACKER] ✅ At top - No scrolling done on {self.current_page}")
            return True
        else:
            print(f"[TRACKER] ⚠️ Not at top - Has scrolled down on {self.current_page}")
            return False

# Global page tracker
page_tracker = PageTracker()

def detect_page_position(driver):
    """Intelligently detect if we're at the top of the page using navigation logic"""
    try:
        print("[POSITION] Analyzing current page position...")

        # Use navigation-based logic (your insight!)
        if page_tracker.is_at_top():
            return "TOP"

        # Fallback: Check if we can see top elements
        top_indicators = [
            "//*[contains(@text, 'Jelajahi') or contains(@content-desc, 'Jelajahi')]",
            "//*[contains(@text, 'Temukan') or contains(@content-desc, 'Temukan')]",
            "//*[@text='Ruang GTK' or @content-desc='Ruang GTK']",
            "//*[contains(@text, 'Beranda') or contains(@content-desc, 'Beranda')]"
        ]

        top_elements_found = 0
        for indicator in top_indicators:
            try:
                elements = driver.find_elements("xpath", indicator)
                if elements:
                    element = elements[0]
                    location = element.location
                    if location['y'] < 400:
                        top_elements_found += 1
                        print(f"[POSITION] Top indicator found at y={location['y']}")
            except:
                continue

        if top_elements_found >= 2:
            print(f"[POSITION] ✅ At top - found {top_elements_found} top indicators")
            return "TOP"
        else:
            print("[POSITION] ⚠️ Not at top - need to scroll up")
            return "NOT_TOP"

    except Exception as e:
        print(f"[POSITION] Error detecting position: {e}")
        return "TOP"  # Safe default

def smart_scroll_to_top(driver):
    """Smart scroll that only scrolls when necessary and safe"""
    try:
        print("[SMART_SCROLL] Checking if scroll to top is needed...")

        # Use navigation-based logic
        position = detect_page_position(driver)

        if position == "TOP":
            print("[SMART_SCROLL] ✅ Already at top - no scroll needed")
            return True

        print("[SMART_SCROLL] Not at top, performing safe scroll...")

        # Perform safe scroll to top
        size = driver.get_window_size()
        start_x = size['width'] // 2

        max_scrolls = 3
        for i in range(max_scrolls):
            # Safe scroll up (avoid pull-to-refresh)
            start_y = int(size['height'] * 0.6)  # Start from middle
            end_y = int(size['height'] * 0.8)    # End at bottom (scroll up)

            print(f"[SMART_SCROLL] Safe scroll up {i+1}/{max_scrolls}...")
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1.5)

            # Check if we've reached top
            current_position = detect_page_position(driver)
            if current_position == "TOP":
                print(f"[SMART_SCROLL] ✅ Reached top after {i+1} scrolls")
                # Reset tracker since we're back at top
                page_tracker.has_scrolled_down = False
                page_tracker.page_just_opened = False
                return True

        print("[SMART_SCROLL] ✅ Scroll complete")
        # Reset tracker
        page_tracker.has_scrolled_down = False
        page_tracker.page_just_opened = False
        return True

    except Exception as e:
        print(f"[SMART_SCROLL] Error in smart scroll: {e}")
        return True

def smart_click_menu(driver, menu_name):
    """Smart click menu with multiple methods"""
    print(f"\n[CLICK] 🎯 Attempting to click menu: {menu_name}")
    
    # Try multiple patterns and methods
    patterns = [
        f"//*[@text='{menu_name}' or @content-desc='{menu_name}']",
        f"//*[contains(@text, '{menu_name}') or contains(@content-desc, '{menu_name}')]",
        f"//android.widget.ImageView[@content-desc='{menu_name}']",
        f"//android.widget.TextView[@text='{menu_name}']",
        f"//*[@clickable='true' and (@text='{menu_name}' or @content-desc='{menu_name}')]"
    ]
    
    for i, pattern in enumerate(patterns, 1):
        try:
            print(f"[CLICK] Trying pattern {i}: {pattern[:60]}...")
            elements = driver.find_elements("xpath", pattern)
            
            if elements:
                element = elements[0]
                print(f"[CLICK] Found element, attempting click...")
                
                # Method 1: Regular click
                try:
                    element.click()
                    time.sleep(3)  # Wait for navigation
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (regular click)")
                    return True
                except:
                    pass
                
                # Method 2: Tap using coordinates
                try:
                    location = element.location
                    size = element.size
                    x = location['x'] + size['width'] // 2
                    y = location['y'] + size['height'] // 2
                    
                    driver.tap([(x, y)])
                    time.sleep(3)
                    print(f"[CLICK] ✅ Successfully clicked {menu_name} (tap)")
                    return True
                except:
                    pass
                
        except Exception as e:
            print(f"[CLICK] Pattern {i} failed: {e}")
            continue
    
    print(f"[CLICK] ❌ Could not click {menu_name}")
    return False

def go_back_to_main_page(driver):
    """Go back to main page"""
    try:
        print("\n[BACK] 🔙 Going back to main page...")
        
        # Method 1: Back button
        try:
            driver.back()
            time.sleep(2)
            print("[BACK] ✅ Used back button")
            return True
        except:
            pass
        
        # Method 2: Home navigation
        try:
            # Look for home/main indicators
            home_patterns = [
                "//*[@content-desc='Home' or @text='Home']",
                "//*[contains(@content-desc, 'Beranda') or contains(@text, 'Beranda')]",
                "//*[@content-desc='Navigate up' or @text='Navigate up']"
            ]
            
            for pattern in home_patterns:
                elements = driver.find_elements("xpath", pattern)
                if elements:
                    elements[0].click()
                    time.sleep(2)
                    print("[BACK] ✅ Used home navigation")
                    return True
        except:
            pass
        
        print("[BACK] ⚠️ Could not navigate back")
        return False

    except Exception as e:
        print(f"[BACK] Error going back: {e}")
        return False

def main():
    """Main function implementing the proper collection flow"""
    print("=" * 80)
    print("PROPER FLOW ANDROID APP ANALYZER")
    print("Systematic Element Collection with Hierarchical Navigation")
    print("=" * 80)

    try:
        # Load config
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"

        # Start Appium session
        driver = start_proper_appium_session(package)

        # Launch the app first
        print("\n[LAUNCH] Launching Rumah Pendidikan app...")
        try:
            driver.activate_app(package)
            time.sleep(5)
            print("[LAUNCH] ✅ App launched successfully")
        except Exception as e:
            print(f"[LAUNCH] Error launching app: {e}")
            # Try clicking the app icon if visible
            try:
                rumah_pendidikan_element = driver.find_element("xpath", "//*[@text='Rumah Pendidikan' or @content-desc='Rumah Pendidikan']")
                rumah_pendidikan_element.click()
                time.sleep(5)
                print("[LAUNCH] ✅ App launched by clicking icon")
            except:
                print("[LAUNCH] ❌ Could not launch app")

        # Wait for main page to load
        wait_for_page_load(driver, timeout=20)

        # Mark main page as opened
        page_tracker.mark_page_opened("MAIN")

        # STEP 1: Collect ALL elements on main page first
        print("\n" + "="*60)
        print("STEP 1: COLLECTING ALL MAIN PAGE ELEMENTS")
        print("="*60)

        main_page_elements = collect_all_elements_on_page(driver, "Main Page")

        # STEP 2: Identify clickable menu items
        print("\n" + "="*60)
        print("STEP 2: IDENTIFYING MENU ITEMS")
        print("="*60)

        target_menus = [
            "Ruang GTK", "Ruang Murid", "Ruang Sekolah", "Ruang Bahasa",
            "Ruang Pemerintah", "Ruang Mitra", "Ruang Publik", "Ruang Orang Tua",
            "Sumber Belajar", "Pusat Perbukuan", "Pengelolaan Kinerja", "Lihat Semua",
            "Butuh Bantuan", "Ruang", "Pemberitahuan", "Akun"
        ]

        found_menus = []
        for element in main_page_elements:
            if element['clickable']:
                text = element['text'] or element['content_desc']
                if any(menu in text for menu in target_menus):
                    found_menus.append(text)
                    print(f"[MENU] ✅ Found clickable menu: {text}")

        print(f"\n[MENU] Total menus found: {len(found_menus)}")

        # STEP 3: Navigate through each menu systematically
        print("\n" + "="*60)
        print("STEP 3: SYSTEMATIC MENU NAVIGATION")
        print("="*60)

        all_collected_data = {
            'main_page': main_page_elements,
            'menus': {}
        }

        for menu_index, menu_name in enumerate(found_menus, 1):
            print(f"\n{'='*40}")
            print(f"MENU {menu_index}/{len(found_menus)}: {menu_name}")
            print(f"{'='*40}")

            # Ensure we're on main page with smart positioning
            smart_scroll_to_top(driver)
            time.sleep(2)

            # Click the menu
            if smart_click_menu(driver, menu_name):
                # Wait for submenu page to load
                wait_for_page_load(driver, timeout=15)

                # Mark new page as opened
                page_tracker.mark_page_opened(f"Submenu: {menu_name}")

                # Collect all elements in submenu
                submenu_elements = collect_all_elements_on_page(driver, f"Submenu: {menu_name}")

                # Store submenu data
                all_collected_data['menus'][menu_name] = {
                    'elements': submenu_elements,
                    'submenus': {}
                }

                # Look for clickable submenus
                submenu_items = []
                for element in submenu_elements:
                    if element['clickable'] and element['text']:
                        submenu_items.append(element['text'])

                print(f"[SUBMENU] Found {len(submenu_items)} potential submenus in {menu_name}")

                # Navigate through submenus
                for submenu_index, submenu_name in enumerate(submenu_items[:5], 1):  # Limit to 5 submenus
                    print(f"\n  SUBMENU {submenu_index}: {submenu_name}")

                    if smart_click_menu(driver, submenu_name):
                        wait_for_page_load(driver, timeout=10)

                        # Mark new sub-submenu page as opened
                        page_tracker.mark_page_opened(f"Sub-submenu: {submenu_name}")

                        # Collect elements in sub-submenu
                        subsubmenu_elements = collect_all_elements_on_page(driver, f"Sub-submenu: {submenu_name}")

                        # Store sub-submenu data
                        all_collected_data['menus'][menu_name]['submenus'][submenu_name] = subsubmenu_elements

                        # Go back to submenu
                        go_back_to_main_page(driver)
                        time.sleep(2)

                        # Mark back to submenu
                        page_tracker.mark_page_opened(f"Submenu: {menu_name}")

                # Go back to main page
                go_back_to_main_page(driver)
                time.sleep(2)

                # Mark back to main page
                page_tracker.mark_page_opened("MAIN")

                # Check position after returning to main page
                position_after_back = detect_page_position(driver)
                print(f"[NAVIGATION] Position after back: {position_after_back}")

            else:
                print(f"[ERROR] Could not click menu: {menu_name}")

        # STEP 4: Save all collected data
        print("\n" + "="*60)
        print("STEP 4: SAVING COLLECTED DATA")
        print("="*60)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"./locators/proper_flow_collection_{timestamp}.json"

        os.makedirs("./locators", exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_collected_data, f, indent=2, ensure_ascii=False)

        # Print summary
        total_elements = len(main_page_elements)
        total_menus = len(all_collected_data['menus'])
        total_submenus = sum(len(menu_data['submenus']) for menu_data in all_collected_data['menus'].values())

        print(f"\n✅ COLLECTION COMPLETE!")
        print(f"📊 Summary:")
        print(f"  - Main page elements: {total_elements}")
        print(f"  - Menus explored: {total_menus}")
        print(f"  - Submenus explored: {total_submenus}")
        print(f"  - Data saved to: {output_file}")

        # Keep session open for inspection
        print("\nPress Enter to close session...")
        input()

        driver.quit()
        print("✅ Session closed successfully!")

    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
