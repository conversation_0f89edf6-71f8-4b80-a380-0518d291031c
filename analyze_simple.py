#!/usr/bin/env python3

"""
Simplified analyze.py that bypasses problematic ADB commands
and focuses on getting the automation working
"""

import os
import sys
import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def smart_wait_for_app_ready(driver, timeout=60):
    """Smart wait system that waits until all elements are fully loaded"""
    print(f"[SMART_WAIT] Waiting for application to fully load (max {timeout}s)...")

    start_time = time.time()
    stable_count = 0
    required_stable_checks = 3  # Number of consecutive stable checks needed
    check_interval = 2  # Seconds between checks

    previous_element_count = 0
    previous_page_source_length = 0

    while time.time() - start_time < timeout:
        try:
            current_time = time.time() - start_time
            print(f"[SMART_WAIT] Check {int(current_time/check_interval)+1} - Analyzing page stability...")

            # Get current page state
            page_source = driver.page_source
            current_page_source_length = len(page_source)

            # Count meaningful elements
            meaningful_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='' or @clickable='true']")
            current_element_count = len(meaningful_elements)

            # Check for loading indicators
            loading_indicators = driver.find_elements("xpath", "//*[contains(@text, 'Loading') or contains(@text, 'loading') or contains(@content-desc, 'Loading') or contains(@content-desc, 'loading')]")
            progress_bars = driver.find_elements("xpath", "//android.widget.ProgressBar")
            spinners = driver.find_elements("xpath", "//*[contains(@class, 'ProgressBar') or contains(@class, 'Spinner')]")

            has_loading_indicators = len(loading_indicators) > 0 or len(progress_bars) > 0 or len(spinners) > 0

            print(f"[SMART_WAIT]   Elements: {current_element_count}, Page size: {current_page_source_length}, Loading indicators: {len(loading_indicators + progress_bars + spinners)}")

            # Check if page is stable (no loading indicators and element count is stable)
            if not has_loading_indicators:
                element_count_stable = abs(current_element_count - previous_element_count) <= 2  # Allow small variations
                page_size_stable = abs(current_page_source_length - previous_page_source_length) <= 100  # Allow small variations

                if element_count_stable and page_size_stable and current_element_count > 5:  # Must have meaningful content
                    stable_count += 1
                    print(f"[SMART_WAIT]   ✅ Page appears stable ({stable_count}/{required_stable_checks})")

                    if stable_count >= required_stable_checks:
                        total_time = time.time() - start_time
                        print(f"[SMART_WAIT] ✅ Application fully loaded! ({total_time:.1f}s)")
                        print(f"[SMART_WAIT]   Final state: {current_element_count} elements, {current_page_source_length} chars")
                        return True
                else:
                    stable_count = 0
                    print(f"[SMART_WAIT]   ⏳ Page still changing (elements: {previous_element_count}→{current_element_count}, size: {previous_page_source_length}→{current_page_source_length})")
            else:
                stable_count = 0
                print(f"[SMART_WAIT]   ⏳ Loading indicators detected, waiting...")

            # Update previous values
            previous_element_count = current_element_count
            previous_page_source_length = current_page_source_length

            # Wait before next check
            time.sleep(check_interval)

        except Exception as e:
            print(f"[SMART_WAIT] Error during stability check: {e}")
            time.sleep(check_interval)
            continue

    # Timeout reached
    total_time = time.time() - start_time
    print(f"[SMART_WAIT] ⚠️ Timeout reached ({total_time:.1f}s), proceeding anyway...")
    print(f"[SMART_WAIT]   Final state: {current_element_count} elements detected")
    return False

def wait_for_specific_elements(driver, timeout=30):
    """Wait for specific key elements that indicate the app is ready"""
    print(f"[ELEMENT_WAIT] Waiting for key elements to appear...")

    start_time = time.time()

    # Key elements that indicate the app is ready
    key_element_patterns = [
        "//*[@text='Ruang GTK' or @content-desc='Ruang GTK']",
        "//*[@text='Ruang Murid' or @content-desc='Ruang Murid']",
        "//*[@text='Ruang Sekolah' or @content-desc='Ruang Sekolah']",
        "//*[contains(@text, 'Ruang') or contains(@content-desc, 'Ruang')]",  # Any "Ruang" element
        "//android.widget.TextView[@text!='' and string-length(@text)>3]",  # Any meaningful text
        "//*[@clickable='true' and (@text!='' or @content-desc!='')]"  # Any clickable element with text
    ]

    while time.time() - start_time < timeout:
        try:
            current_time = time.time() - start_time
            print(f"[ELEMENT_WAIT] Check {int(current_time/2)+1} - Looking for key elements...")

            found_elements = []
            for pattern in key_element_patterns:
                try:
                    elements = driver.find_elements("xpath", pattern)
                    if elements:
                        found_elements.append(f"{len(elements)} elements matching '{pattern[:50]}...'")
                except:
                    continue

            if found_elements:
                print(f"[ELEMENT_WAIT] ✅ Key elements found:")
                for element_info in found_elements[:3]:  # Show first 3
                    print(f"[ELEMENT_WAIT]   - {element_info}")

                total_time = time.time() - start_time
                print(f"[ELEMENT_WAIT] ✅ App appears ready! ({total_time:.1f}s)")
                return True
            else:
                print(f"[ELEMENT_WAIT]   ⏳ Key elements not yet visible...")

            time.sleep(2)

        except Exception as e:
            print(f"[ELEMENT_WAIT] Error checking elements: {e}")
            time.sleep(2)
            continue

    # Timeout reached
    total_time = time.time() - start_time
    print(f"[ELEMENT_WAIT] ⚠️ Timeout reached ({total_time:.1f}s), proceeding anyway...")
    return False

def start_simple_appium_session(package):
    """Start Appium session with minimal setup and smart wait"""
    print(f"[SIMPLE] Starting Appium session for: {package}")

    # Use known main activity for the target app
    main_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"

    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = main_activity
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    options.set_capability('adbExecTimeout', 60000)
    options.set_capability('uiautomator2ServerLaunchTimeout', 60000)

    print("[SIMPLE] Connecting to Appium server...")
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(15)

    print("✅ Appium session started successfully!")

    # Smart wait for app to be fully loaded
    print("\n" + "="*60)
    print("SMART WAIT SYSTEM - ENSURING APP IS FULLY LOADED")
    print("="*60)

    # Method 1: Wait for page stability
    stability_result = smart_wait_for_app_ready(driver, timeout=60)

    # Method 2: Wait for specific key elements
    elements_result = wait_for_specific_elements(driver, timeout=30)

    if stability_result or elements_result:
        print("\n✅ SMART WAIT COMPLETE - Application is ready for automation!")
    else:
        print("\n⚠️ SMART WAIT TIMEOUT - Proceeding with caution...")

    print("="*60 + "\n")

    return driver

def simple_element_collection(driver):
    """Simple element collection with smart wait verification"""
    print("[SIMPLE] Starting basic element collection...")

    try:
        # Additional verification that app is ready
        print("[SIMPLE] Performing final readiness check...")
        final_check = wait_for_specific_elements(driver, timeout=10)
        if not final_check:
            print("[SIMPLE] ⚠️ App may not be fully ready, but proceeding...")

        # Brief pause to ensure stability
        time.sleep(3)
        
        # Get page source
        page_source = driver.page_source
        print(f"[SIMPLE] Page source length: {len(page_source)} characters")
        
        # Find all elements with text or content-desc
        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
        print(f"[SIMPLE] Found {len(elements)} elements with text/content-desc")
        
        # Print first 10 elements for verification
        print("[SIMPLE] First 10 elements:")
        for i, element in enumerate(elements[:10]):
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                class_name = element.get_attribute('class') or ''
                clickable = element.get_attribute('clickable') == 'true'
                
                click_indicator = "🔘" if clickable else "⚪"
                print(f"  {i+1:2d}. {click_indicator} text='{text[:30]}' desc='{desc[:30]}' class='{class_name.split('.')[-1]}'")
                
            except Exception as e:
                print(f"  {i+1:2d}. Error getting element info: {e}")
        
        return elements
        
    except Exception as e:
        print(f"[ERROR] Element collection failed: {e}")
        return []

def main():
    """Simple main function that focuses on core functionality"""
    print("=" * 80)
    print("SIMPLIFIED ANDROID APP ANALYZER")
    print("=" * 80)
    
    try:
        # Load config
        print("[SIMPLE] Loading configuration...")
        config = load_config()
        
        # Use known package (skip all ADB detection)
        package = "com.kemendikdasmen.rumahpendidikan"
        print(f"[SIMPLE] Using package: {package}")
        
        # Skip app installation check - assume app is ready
        print("[SIMPLE] Skipping app installation check (assuming app is ready)")
        
        # Start Appium session directly
        print("[SIMPLE] Starting Appium session...")
        driver = start_simple_appium_session(package)
        
        # Do basic element collection
        elements = simple_element_collection(driver)
        
        print(f"\n[SIMPLE] ✅ SUCCESS! Found {len(elements)} elements")
        print("[SIMPLE] Basic automation is working!")
        
        # Keep session open for manual testing
        print("\n[SIMPLE] Session is ready. Press Enter to close...")
        input()
        
        # Close session
        driver.quit()
        print("[SIMPLE] Session closed.")
        
    except Exception as e:
        print(f"[ERROR] Simple automation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
