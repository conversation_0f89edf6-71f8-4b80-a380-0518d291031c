#!/usr/bin/env python3

"""
Simplified analyze.py that bypasses problematic ADB commands
and focuses on getting the automation working
"""

import os
import sys
import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def start_simple_appium_session(package):
    """Start Appium session with minimal setup"""
    print(f"[SIMPLE] Starting Appium session for: {package}")
    
    # Use known main activity for the target app
    main_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = main_activity
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    options.set_capability('adbExecTimeout', 60000)
    options.set_capability('uiautomator2ServerLaunchTimeout', 60000)
    
    print("[SIMPLE] Connecting to Appium server...")
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(15)
    
    print("✅ Appium session started successfully!")
    return driver

def simple_element_collection(driver):
    """Simple element collection without complex crawling"""
    print("[SIMPLE] Starting basic element collection...")
    
    try:
        # Wait for app to load
        time.sleep(5)
        
        # Get page source
        page_source = driver.page_source
        print(f"[SIMPLE] Page source length: {len(page_source)} characters")
        
        # Find all elements with text or content-desc
        elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
        print(f"[SIMPLE] Found {len(elements)} elements with text/content-desc")
        
        # Print first 10 elements for verification
        print("[SIMPLE] First 10 elements:")
        for i, element in enumerate(elements[:10]):
            try:
                text = element.get_attribute('text') or ''
                desc = element.get_attribute('content-desc') or ''
                class_name = element.get_attribute('class') or ''
                clickable = element.get_attribute('clickable') == 'true'
                
                click_indicator = "🔘" if clickable else "⚪"
                print(f"  {i+1:2d}. {click_indicator} text='{text[:30]}' desc='{desc[:30]}' class='{class_name.split('.')[-1]}'")
                
            except Exception as e:
                print(f"  {i+1:2d}. Error getting element info: {e}")
        
        return elements
        
    except Exception as e:
        print(f"[ERROR] Element collection failed: {e}")
        return []

def main():
    """Simple main function that focuses on core functionality"""
    print("=" * 80)
    print("SIMPLIFIED ANDROID APP ANALYZER")
    print("=" * 80)
    
    try:
        # Load config
        print("[SIMPLE] Loading configuration...")
        config = load_config()
        
        # Use known package (skip all ADB detection)
        package = "com.kemendikdasmen.rumahpendidikan"
        print(f"[SIMPLE] Using package: {package}")
        
        # Skip app installation check - assume app is ready
        print("[SIMPLE] Skipping app installation check (assuming app is ready)")
        
        # Start Appium session directly
        print("[SIMPLE] Starting Appium session...")
        driver = start_simple_appium_session(package)
        
        # Do basic element collection
        elements = simple_element_collection(driver)
        
        print(f"\n[SIMPLE] ✅ SUCCESS! Found {len(elements)} elements")
        print("[SIMPLE] Basic automation is working!")
        
        # Keep session open for manual testing
        print("\n[SIMPLE] Session is ready. Press Enter to close...")
        input()
        
        # Close session
        driver.quit()
        print("[SIMPLE] Session closed.")
        
    except Exception as e:
        print(f"[ERROR] Simple automation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
