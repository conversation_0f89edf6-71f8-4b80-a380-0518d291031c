#!/usr/bin/env python3

"""
Demonstration of the comprehensive logging system
Shows all available logging methods and their outputs
"""

import os
import sys
import time
import datetime
from pathlib import Path

def demo_all_logging_methods():
    """Demonstrate all logging methods"""
    
    print("🎬 COMPREHENSIVE LOGGING SYSTEM DEMONSTRATION")
    print("=" * 60)
    print(f"📅 Demo Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("📋 Available Logging Methods:")
    print("1. 🔧 Built-in Logging (analyze.py & main.py)")
    print("2. 🎯 Wrapper Scripts (enhanced logging)")
    print("3. 🚀 Universal Runner (maximum control)")
    print()
    
    # Show current log structure
    print("📁 Current Log Directory Structure:")
    execution_logs = Path("execution_logs")
    if execution_logs.exists():
        log_files = list(execution_logs.rglob("*.txt"))
        if log_files:
            print(f"   Found {len(log_files)} existing log files:")
            for log_file in sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]:
                rel_path = log_file.relative_to(execution_logs)
                size = log_file.stat().st_size / 1024  # KB
                mtime = datetime.datetime.fromtimestamp(log_file.stat().st_mtime)
                print(f"   📄 {rel_path} ({size:.1f} KB) - {mtime.strftime('%H:%M:%S')}")
            if len(log_files) > 5:
                print(f"   ... and {len(log_files) - 5} more files")
        else:
            print("   No log files found yet")
    else:
        print("   No execution_logs directory found yet")
    
    print()
    
    # Show usage examples
    print("💡 Usage Examples:")
    print()
    
    print("🔧 Method 1: Built-in Logging (Automatic)")
    print("   python analyze.py")
    print("   python main.py")
    print("   ✅ Pros: No setup required, automatic activation")
    print("   ⚠️  Note: Graceful fallback if logging system unavailable")
    print()
    
    print("🎯 Method 2: Wrapper Scripts (Enhanced)")
    print("   python run_analyze_with_logging.py")
    print("   python run_main_with_logging.py")
    print("   ✅ Pros: Enhanced error handling, better crash recovery")
    print("   ✅ Features: Automatic summary generation, metadata logging")
    print()
    
    print("🚀 Method 3: Universal Runner (Maximum Control)")
    print("   python run_with_logging.py analyze.py")
    print("   python run_with_logging.py main.py")
    print("   python run_with_logging.py --log-dir custom_logs analyze.py")
    print("   ✅ Pros: Works with any script, custom directories")
    print("   ✅ Features: Process isolation, real-time capture")
    print()
    
    # Show log file naming convention
    print("📝 Log File Naming Convention:")
    now = datetime.datetime.now()
    example_timestamp = now.strftime("%Y%m%d_%H%M%S")
    example_path = f"execution_logs/{now.year}/{now.month:02d}/{now.day:02d}/"
    
    print(f"   📁 Directory: {example_path}")
    print(f"   📄 analyze.py → analyze_{example_timestamp}.txt")
    print(f"   📄 main.py → main_{example_timestamp}.txt")
    print()
    
    # Show log file contents
    print("📋 Log File Contents Include:")
    contents = [
        "📊 Execution metadata (start time, duration, exit status)",
        "🖥️  All terminal output (stdout and stderr)",
        "⚠️  Error messages and stack traces",
        "🔄 Progress indicators and status updates",
        "👤 User interactions (y/n prompts, inputs)",
        "📱 Appium session logs and device interactions",
        "🎯 Element collection details and statistics",
        "🖱️  Menu clicking attempts and results",
        "🧭 Navigation logs and page transitions",
        "🐛 Debug information and troubleshooting data"
    ]
    
    for item in contents:
        print(f"   {item}")
    
    print()
    
    # Show management commands
    print("🛠️  Log Management Commands:")
    commands = [
        ("View latest log", "ls -t execution_logs/*/*/*/*.txt | head -1 | xargs cat"),
        ("Count executions", "find execution_logs/ -name '*.txt' | wc -l"),
        ("Find failed runs", "grep -l 'ERROR' execution_logs/*/*/*/*.txt"),
        ("Check disk usage", "du -sh execution_logs/"),
        ("Clean old logs", "find execution_logs/ -name '*.txt' -mtime +30 -delete")
    ]
    
    for desc, cmd in commands:
        print(f"   {desc}:")
        print(f"     {cmd}")
        print()
    
    print("🎯 Ready to Use!")
    print("The comprehensive logging system is now fully operational.")
    print("All executions of analyze.py and main.py will be automatically logged.")
    print()
    print("🚀 Try it now:")
    print("   python analyze.py")
    print("   # Check: execution_logs/YYYY/MM/DD/analyze_YYYYMMDD_HHMMSS.txt")

def show_real_time_demo():
    """Show a real-time logging demonstration"""
    print("\n🎬 Real-Time Logging Demo")
    print("=" * 40)
    
    try:
        from logger_system import TerminalLogger
        
        logger = TerminalLogger("demo", "demo_logs")
        logger.start_logging()
        
        print("📝 Demonstrating real-time logging...")
        print("🔄 Processing some data...")
        
        for i in range(5):
            print(f"   Step {i+1}/5: Processing item {i+1}")
            time.sleep(0.3)
        
        print("✅ Demo processing completed!")
        print("⚠️  This is a warning message")
        print("❌ This is an error message (simulated)")
        
        # Test stderr
        import sys
        print("📢 This goes to stderr", file=sys.stderr)
        
        logger.stop_logging()
        
        print(f"\n📄 Demo log saved to: {logger.log_file_path}")
        
        # Show log content
        if logger.log_file_path.exists():
            print("\n📋 Log file content preview:")
            with open(logger.log_file_path, 'r') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[-10:], 1):  # Last 10 lines
                    print(f"   {i:2d}: {line.rstrip()}")
        
    except ImportError:
        print("⚠️  logger_system not available for demo")
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def main():
    """Run the logging system demonstration"""
    demo_all_logging_methods()
    show_real_time_demo()
    
    print("\n" + "=" * 60)
    print("🎉 Logging System Demonstration Complete!")
    print("📚 See LOGGING_SYSTEM_README.md for detailed documentation")

if __name__ == "__main__":
    main()
