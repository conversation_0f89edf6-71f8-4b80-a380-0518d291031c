#!/usr/bin/env python3

"""
Diagnostic script to identify why analyze.py hangs at package detection
"""

import os
import sys
import subprocess
import yaml
from pathlib import Path

def load_config():
    """Load configuration"""
    config_path = os.path.join("config", "config.yaml")
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"❌ Failed to load config: {e}")
        return None

def check_apk_folder(config):
    """Check APK folder and contents"""
    print("🔍 Checking APK folder...")
    
    if not config:
        print("❌ No config available")
        return None
    
    apk_folder = config.get('apk_folder', 'apk')
    print(f"📁 APK folder: {apk_folder}")
    
    if not os.path.exists(apk_folder):
        print(f"❌ APK folder does not exist: {apk_folder}")
        return None
    
    print(f"✅ APK folder exists")
    
    # List all files in APK folder
    try:
        files = os.listdir(apk_folder)
        print(f"📄 Files in APK folder ({len(files)} total):")
        for file in files:
            file_path = os.path.join(apk_folder, file)
            size = os.path.getsize(file_path) if os.path.isfile(file_path) else 0
            print(f"   {file} ({size} bytes)")
        
        # Find APK files
        apks = [f for f in files if f.endswith('.apk')]
        print(f"🎯 APK files found: {len(apks)}")
        
        if apks:
            apk_path = os.path.join(apk_folder, apks[0])
            print(f"📱 Using APK: {apk_path}")
            return apk_path
        else:
            print("❌ No APK files found")
            return None
            
    except Exception as e:
        print(f"❌ Error reading APK folder: {e}")
        return None

def check_aapt_tool(config):
    """Check aapt tool availability"""
    print("\n🔧 Checking aapt tool...")
    
    if not config:
        print("❌ No config available")
        return False
    
    aapt_path = config.get('aapt_path', 'aapt')
    print(f"🛠️  aapt path: {aapt_path}")
    
    # Test aapt availability
    try:
        result = subprocess.run([aapt_path, 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ aapt is working")
            print(f"   Version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ aapt failed with return code: {result.returncode}")
            print(f"   stderr: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ aapt command timed out")
        return False
    except FileNotFoundError:
        print(f"❌ aapt not found at: {aapt_path}")
        return False
    except Exception as e:
        print(f"❌ aapt test failed: {e}")
        return False

def test_aapt_on_apk(apk_path, config):
    """Test aapt on the APK file"""
    print(f"\n📱 Testing aapt on APK: {apk_path}")
    
    if not apk_path or not os.path.exists(apk_path):
        print("❌ APK file not available")
        return None
    
    aapt_path = config.get('aapt_path', 'aapt')
    
    try:
        print("🔄 Running aapt dump badging (with 15 second timeout)...")
        result = subprocess.run(
            [aapt_path, 'dump', 'badging', apk_path], 
            capture_output=True, 
            text=True, 
            timeout=15
        )
        
        print(f"📊 Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ aapt command successful")
            
            # Look for package name
            for line in result.stdout.splitlines():
                if line.startswith('package:'):
                    print(f"📦 Package line: {line}")
                    for part in line.split():
                        if part.startswith('name='):
                            package_name = part.split('=')[1].strip("'\"")
                            print(f"🎯 Package name: {package_name}")
                            return package_name
            
            print("⚠️  Package name not found in output")
            print("📄 First 10 lines of output:")
            for i, line in enumerate(result.stdout.splitlines()[:10]):
                print(f"   {i+1}: {line}")
        else:
            print(f"❌ aapt failed with return code: {result.returncode}")
            if result.stderr:
                print(f"📄 stderr: {result.stderr}")
            if result.stdout:
                print(f"📄 stdout: {result.stdout[:500]}...")
        
    except subprocess.TimeoutExpired:
        print("❌ aapt command timed out after 15 seconds")
        print("🔍 This is likely the cause of the hang in analyze.py")
        return None
    except Exception as e:
        print(f"❌ aapt test failed: {e}")
        return None

def check_alternative_methods():
    """Check alternative methods to get package name"""
    print("\n🔄 Checking alternative methods...")
    
    # Method 1: Check if app is already installed
    try:
        result = subprocess.run(['adb', 'shell', 'pm', 'list', 'packages'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            packages = result.stdout.splitlines()
            target_packages = [p for p in packages if 'kemendikdasmen' in p or 'rumahpendidikan' in p]
            if target_packages:
                print("✅ Found target app already installed:")
                for pkg in target_packages:
                    print(f"   {pkg}")
                return True
        else:
            print("❌ adb pm list packages failed")
    except Exception as e:
        print(f"❌ adb check failed: {e}")
    
    # Method 2: Check if we can use known package name
    known_package = "com.kemendikdasmen.rumahpendidikan"
    print(f"💡 Suggested solution: Use known package name: {known_package}")
    return False

def main():
    """Run comprehensive diagnosis"""
    print("🔍 DIAGNOSING APK PACKAGE DETECTION ISSUE")
    print("=" * 60)
    
    # Load config
    config = load_config()
    if not config:
        print("❌ Cannot proceed without config")
        return
    
    # Check APK folder
    apk_path = check_apk_folder(config)
    
    # Check aapt tool
    aapt_working = check_aapt_tool(config)
    
    # Test aapt on APK if both are available
    package_name = None
    if apk_path and aapt_working:
        package_name = test_aapt_on_apk(apk_path, config)
    
    # Check alternatives
    check_alternative_methods()
    
    # Summary and recommendations
    print("\n" + "=" * 60)
    print("📋 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    if package_name:
        print(f"✅ Successfully detected package: {package_name}")
        print("💡 The issue might be intermittent or resolved")
    else:
        print("❌ Package detection failed")
        print("\n🔧 RECOMMENDED SOLUTIONS:")
        print("1. Use the updated analyze.py with bypass option")
        print("2. When prompted, choose 'y' to use known package name")
        print("3. This will skip the hanging aapt command")
        print("\n🚀 Try running: python analyze.py")
        print("   When asked 'Use known package...?', answer 'y'")

if __name__ == "__main__":
    main()
