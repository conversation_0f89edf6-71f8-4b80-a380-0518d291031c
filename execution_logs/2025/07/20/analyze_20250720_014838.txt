
================================================================================
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
================================================================================
Script: analyze
Start Time: 2025-07-20 01:48:38
Log File: execution_logs/2025/07/20/analyze_20250720_014838.txt
Python Version: 3.9.7 (default, Aug  3 2023, 19:02:34) 
[Clang 14.0.3 (clang-1403.*********)]
Working Directory: /Users/<USER>/code/android_automation
================================================================================

📝 Logging started: execution_logs/2025/07/20/analyze_20250720_014838.txt
🕐 Start time: 2025-07-20 01:48:38
================================================================================
================================================================================
ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION
================================================================================
[SETUP] Starting emulator...
Emulator already running.
[SETUP] Preparing APK...
[DEBUG] Looking for APK in folder: ./apk
[DEBUG] Found 1 APK files: ['app-prod-debug (2).apk']
[DEBUG] Using APK: ./apk/app-prod-debug (2).apk
[DEBUG] APK path: ./apk/app-prod-debug (2).apk
[SETUP] Using known package: com.kemendikdasmen.rumahpendidikan
[SETUP] Checking if app is installed on emulator...
[DEBUG] Checking if com.kemendikdasmen.rumahpendidikan is installed...
[WARNING] adb pm list packages timed out, assuming app not installed
❌ com.kemendikdasmen.rumahpendidikan is not installed on emulator.
[SETUP] Installing app from APK...
[DEBUG] Installing ./apk/app-prod-debug (2).apk...
[WARNING] Install of ./apk/app-prod-debug (2).apk timed out
[SETUP] Launching com.kemendikdasmen.rumahpendidikan...
[DEBUG] Launching app: com.kemendikdasmen.rumahpendidikan
[WARNING] App launch timed out for com.kemendikdasmen.rumahpendidikan
[SETUP] Connecting to Appium...
✅ Appium server is running
[DEBUG] Starting Appium session for package: com.kemendikdasmen.rumahpendidikan
[DEBUG] Attempting to resolve main activity...
[WARNING] resolve-activity command timed out
[FALLBACK] Using known main activity: com.kemendikdasmen.rumahpendidikan.MainActivity
[DEBUG] Creating Appium session with activity: com.kemendikdasmen.rumahpendidikan.MainActivity
[DEBUG] Connecting to Appium server at http://localhost:4723...
