
================================================================================
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
================================================================================
Script: analyze
Start Time: 2025-07-20 01:52:31
Log File: execution_logs/2025/07/20/analyze_20250720_015231.txt
Python Version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 10:07:17) [Clang 14.0.6 ]
Working Directory: /Users/<USER>/code/android_automation
================================================================================

📝 Logging started: execution_logs/2025/07/20/analyze_20250720_015231.txt
🕐 Start time: 2025-07-20 01:52:31
================================================================================
================================================================================
ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION
================================================================================
[SETUP] Starting emulator...
Emulator already running.
[SETUP] Preparing APK...
[DEBUG] Looking for APK in folder: ./apk
[DEBUG] Found 1 APK files: ['app-prod-debug (2).apk']
[DEBUG] Using APK: ./apk/app-prod-debug (2).apk
[DEBUG] APK path: ./apk/app-prod-debug (2).apk
[SETUP] Using known package: com.kemendikdasmen.rumahpendidikan
[SETUP] Checking if app is installed on emulator...
[DEBUG] Checking if com.kemendikdasmen.rumahpendidikan is installed...
[WARNING] adb pm list packages timed out, assuming app not installed
❌ com.kemendikdasmen.rumahpendidikan is not installed on emulator.
[SETUP] Installing app from APK...
[DEBUG] Installing ./apk/app-prod-debug (2).apk...
[WARNING] Install of ./apk/app-prod-debug (2).apk timed out
[SETUP] Launching com.kemendikdasmen.rumahpendidikan...
[DEBUG] Launching app: com.kemendikdasmen.rumahpendidikan
[WARNING] App launch timed out for com.kemendikdasmen.rumahpendidikan
[SETUP] Connecting to Appium...
✅ Appium server is running
[DEBUG] Starting Appium session for package: com.kemendikdasmen.rumahpendidikan
[DEBUG] Attempting to resolve main activity...
[WARNING] resolve-activity command timed out
[FALLBACK] Using known main activity: com.kemendikdasmen.rumahpendidikan.MainActivity
[DEBUG] Creating Appium session with activity: com.kemendikdasmen.rumahpendidikan.MainActivity
[DEBUG] Connecting to Appium server at http://localhost:4723...
❌ Failed to start Appium session: Message: An unknown server-side error occurred while processing the command. Original error: Error getting device API level. Original error: Error executing adbExec. Original error: 'Command '/Users/<USER>/Library/Android/sdk/platform-tools/adb -P 5037 -s emulator-5554 shell getprop ro.build.version.sdk' timed out after 60000ms'. Try to increase the 60000ms adb execution timeout represented by 'adbExecTimeout' capability
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Error getting device API level. Original error: Error executing adbExec. Original error: 'Command '/Users/<USER>/Library/Android/sdk/platform-tools/adb -P 5037 -s emulator-5554 shell getprop ro.build.version.sdk' timed out after 60000ms'. Try to increase the 60000ms adb execution timeout represented by 'adbExecTimeout' capability
    at getResponseForW3CError (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[FATAL] Appium session lost or UiAutomator2 crashed: Message: An unknown server-side error occurred while processing the command. Original error: Error getting device API level. Original error: Error executing adbExec. Original error: 'Command '/Users/<USER>/Library/Android/sdk/platform-tools/adb -P 5037 -s emulator-5554 shell getprop ro.build.version.sdk' timed out after 60000ms'. Try to increase the 60000ms adb execution timeout represented by 'adbExecTimeout' capability
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Error getting device API level. Original error: Error executing adbExec. Original error: 'Command '/Users/<USER>/Library/Android/sdk/platform-tools/adb -P 5037 -s emulator-5554 shell getprop ro.build.version.sdk' timed out after 60000ms'. Try to increase the 60000ms adb execution timeout represented by 'adbExecTimeout' capability
    at getResponseForW3CError (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[FATAL] This may be due to device/emulator issues. Check Appium server and device logs.
📊 Log summary created: execution_logs/execution_summary.txt
================================================================================
🕐 End time: 2025-07-20 01:57:22
⏱️  Total duration: 0:04:50.744553
📝 Log saved to: execution_logs/2025/07/20/analyze_20250720_015231.txt


================================================================================
EXECUTION COMPLETED
================================================================================
End Time: 2025-07-20 01:57:22
Total Duration: 0:04:50.744553
Exit Status: SUCCESS
================================================================================
