
================================================================================
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
================================================================================
Script: analyze
Start Time: 2025-07-20 02:03:56
Log File: execution_logs/2025/07/20/analyze_20250720_020356.txt
Python Version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 10:07:17) [Clang 14.0.6 ]
Working Directory: /Users/<USER>/code/android_automation
================================================================================

📝 Logging started: execution_logs/2025/07/20/analyze_20250720_020356.txt
🕐 Start time: 2025-07-20 02:03:56
================================================================================
================================================================================
ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION
================================================================================
[SETUP] Starting emulator...
Emulator already running.
[SETUP] Preparing APK...
[DEBUG] Looking for APK in folder: ./apk
[DEBUG] Found 1 APK files: ['app-prod-debug (2).apk']
[DEBUG] Using APK: ./apk/app-prod-debug (2).apk
[DEBUG] APK path: ./apk/app-prod-debug (2).apk
[SETUP] Using known package: com.kemendikdasmen.rumahpendidikan
[SETUP] Checking if app is installed on emulator...
[DEBUG] Checking if com.kemendikdasmen.rumahpendidikan is installed...
[DEBUG] App installed: True
✅ com.kemendikdasmen.rumahpendidikan is already installed on emulator.
[SETUP] Uninstalling existing app...
[DEBUG] Uninstalling com.kemendikdasmen.rumahpendidikan...
Uninstalled com.kemendikdasmen.rumahpendidikan
[SETUP] Installing fresh app from APK...
[DEBUG] Installing ./apk/app-prod-debug (2).apk...
✅ Successfully installed ./apk/app-prod-debug (2).apk
[SETUP] Launching com.kemendikdasmen.rumahpendidikan...
[DEBUG] Launching app: com.kemendikdasmen.rumahpendidikan
✅ Successfully launched com.kemendikdasmen.rumahpendidikan
[SETUP] Connecting to Appium...
✅ Appium server is running
[DEBUG] Starting Appium session for package: com.kemendikdasmen.rumahpendidikan
[DEBUG] Attempting to resolve main activity...
[DEBUG] Found main activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Creating Appium session with activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Connecting to Appium server at http://localhost:4723...
✅ Appium session started successfully!
[ENHANCED] Initializing comprehensive crawler...
[ENHANCED] Starting comprehensive crawling process...
[ENHANCED_CRAWL] Starting comprehensive app crawling...
[ENHANCED_CRAWL] Starting fresh crawl...
[ENHANCED_CRAWL] Step 1: Collecting all elements on main page...
[ENHANCED_CRAWL] Collecting complete page: Main Page
[COLLECT] Starting step-by-step scroll collection (max 8 scrolls)
[COLLECT] Collected 27 elements after 8 scrolls
[STATE] Saved state to state_com.kemendikdasmen.rumahpendidikan.pkl
[ENHANCED_CRAWL] Collected 28 elements from Main Page
[ENHANCED_CRAWL] Step 2: Navigating through main menus...
[ENHANCED_CRAWL] Starting to crawl 16 main menus

[ENHANCED_CRAWL] ===== Processing main menu 1/16: Ruang GTK =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang GTK: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang GTK

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 2/16: Ruang Murid =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Murid: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang Murid

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 3/16: Ruang Sekolah =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Sekolah: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang Sekolah

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 4/16: Ruang Bahasa =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Bahasa: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang Bahasa

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 5/16: Ruang Pemerintah =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Pemerintah: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang Pemerintah

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 6/16: Ruang Mitra =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Mitra: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang Mitra

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 7/16: Ruang Publik =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Publik: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang Publik

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 8/16: Ruang Orang Tua =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Orang Tua: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang Orang Tua

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 9/16: Sumber Belajar =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Sumber Belajar: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Sumber Belajar

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 10/16: Pusat Perbukuan =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Pusat Perbukuan: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Pusat Perbukuan

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 11/16: Pengelolaan Kinerja =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Pengelolaan Kinerja: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Pengelolaan Kinerja

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 12/16: Lihat Semua =====
[ENHANCED_CRAWL] Error in enhanced menu finding for Lihat Semua: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Lihat Semua

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Found 16 elements with text/content-desc
[DEBUG]  1. ⚪ text='' desc='Jelajahi Beragam Layanan Pendi' class='View' id=''
[DEBUG]  2. ⚪ text='' desc='Temukan Ruang Pendidikan Anda' class='View' id=''
[DEBUG]  3. 🔘 text='' desc='Ruang GTK' class='ImageView' id=''
[DEBUG]  4. 🔘 text='' desc='Ruang Murid' class='ImageView' id=''
[DEBUG]  5. 🔘 text='' desc='Ruang Sekolah' class='ImageView' id=''
[DEBUG]  6. 🔘 text='' desc='Ruang Bahasa' class='ImageView' id=''
[DEBUG]  7. 🔘 text='' desc='Ruang Pemerintah' class='ImageView' id=''
[DEBUG]  8. 🔘 text='' desc='Ruang Mitra' class='ImageView' id=''
[DEBUG]  9. 🔘 text='' desc='Ruang Publik' class='ImageView' id=''
[DEBUG] 10. 🔘 text='' desc='Ruang Orang Tua' class='ImageView' id=''
[DEBUG] 11. ⚪ text='' desc='Layanan Paling Banyak Diakses' class='View' id=''
[DEBUG] 12. 🔘 text='' desc='Sumber Belajar
Portal pembelaj' class='Button' id=''
[DEBUG] 13. 🔘 text='' desc='Beranda
Beranda
Tab 1 of 4' class='Button' id=''
[DEBUG] 14. 🔘 text='' desc='Ruang
Ruang
Tab 2 of 4' class='Button' id=''
[DEBUG] 15. 🔘 text='' desc='Pemberitahuan
Pemberitahuan
Ta' class='Button' id=''
[DEBUG] 16. 🔘 text='' desc='Akun
Akun
Tab 4 of 4' class='Button' id=''
[DEBUG] ===== END PAGE ELEMENTS =====


[ENHANCED_CRAWL] ===== Processing main menu 13/16: Butuh Bantuan? =====
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: Cannot invoke method boolean androidx.test.uiautomator.InteractionController.injectEventSync(android.view.InputEvent) on object androidx.test.uiautomator.InteractionController@e680f34 with parameters [MotionEvent { action=ACTION_MOVE, actionButton=0, id[0]=0, x[0]=720.0, y[0]=1579.1025, toolType[0]=TOOL_TYPE_FINGER, buttonState=0, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=513859, downTime=513354, deviceId=0, source=0x1002, displayId=0, eventId=-102426774 }]
Stacktrace:
io.appium.uiautomator2.common.exceptions.UiAutomator2Exception: Cannot invoke method boolean androidx.test.uiautomator.InteractionController.injectEventSync(android.view.InputEvent) on object androidx.test.uiautomator.InteractionController@e680f34 with parameters [MotionEvent { action=ACTION_MOVE, actionButton=0, id[0]=0, x[0]=720.0, y[0]=1579.1025, toolType[0]=TOOL_TYPE_FINGER, buttonState=0, classification=NONE, metaState=0, flags=0x0, edgeFlags=0x0, pointerCount=1, historySize=0, eventTime=513859, downTime=513354, deviceId=0, source=0x1002, displayId=0, eventId=-102426774 }]
	at io.appium.uiautomator2.utils.ReflectionUtils.invoke(ReflectionUtils.java:85)
	at io.appium.uiautomator2.core.InteractionController.injectEventSync(InteractionController.java:36)
	at io.appium.uiautomator2.utils.w3c.ActionsExecutor.executeMotionEvents(ActionsExecutor.java:270)
	at io.appium.uiautomator2.utils.w3c.ActionsExecutor.execute(ActionsExecutor.java:315)
	at io.appium.uiautomator2.handler.W3CActions.safeHandle(W3CActions.java:79)
	at io.appium.uiautomator2.handler.request.SafeRequestHandler.handle(SafeRequestHandler.java:59)
	at io.appium.uiautomator2.server.AppiumServlet.handleRequest(AppiumServlet.java:259)
	at io.appium.uiautomator2.server.AppiumServlet.handleHttpRequest(AppiumServlet.java:253)
	at io.appium.uiautomator2.http.ServerHandler.channelRead(ServerHandler.java:77)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:374)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:360)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:352)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:102)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:374)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:360)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:352)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:438)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:328)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:302)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:253)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:374)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:360)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:352)
	at io.netty.handler.timeout.IdleStateHandler.channelRead(IdleStateHandler.java:287)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:374)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:360)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:352)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1422)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:374)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:360)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:931)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:700)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:635)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:552)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:514)
	at io.netty.util.concurrent.SingleThreadEventExecutor$6.run(SingleThreadEventExecutor.java:1044)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:1012)
Caused by: java.lang.reflect.InvocationTargetException
	at java.lang.reflect.Method.invoke(Native Method)
	at io.appium.uiautomator2.utils.ReflectionUtils.invoke(ReflectionUtils.java:83)
	... 39 more
Caused by: java.lang.IllegalStateException: Connection shutdown!
	at android.os.Parcel.createExceptionOrNull(Parcel.java:3250)
	at android.os.Parcel.createException(Parcel.java:3226)
	at android.os.Parcel.readException(Parcel.java:3209)
	at android.os.Parcel.readException(Parcel.java:3151)
	at android.app.IUiAutomationConnection$Stub$Proxy.connect(IUiAutomationConnection.java:527)
	at android.app.UiAutomation.connectWithTimeout(UiAutomation.java:385)
	at android.app.Instrumentation.getUiAutomation(Instrumentation.java:2558)
	at androidx.test.uiautomator.UiDevice$Api24Impl.getUiAutomationWithRetry(UiDevice.java:1538)
	at androidx.test.uiautomator.UiDevice.getUiAutomation(UiDevice.java:1463)
	at androidx.test.uiautomator.InteractionController.getUiAutomation(InteractionController.java:620)
	at androidx.test.uiautomator.InteractionController.injectEventSync(InteractionController.java:494)
	... 41 more

[ENHANCED_CRAWL] Error in enhanced menu finding for Butuh Bantuan?: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Butuh Bantuan?

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Error in debug_page_elements: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)

[ENHANCED_CRAWL] ===== Processing main menu 14/16: Ruang =====
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Ruang

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Error in debug_page_elements: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)

[ENHANCED_CRAWL] ===== Processing main menu 15/16: Pemberitahuan =====
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Pemberitahuan: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Pemberitahuan

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Error in debug_page_elements: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)

[ENHANCED_CRAWL] ===== Processing main menu 16/16: Akun =====
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Akun: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Could not find or click menu: Akun

[DEBUG] ===== CURRENT PAGE ELEMENTS (showing first 20) =====
[DEBUG] Error in debug_page_elements: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)

[ENHANCED_CRAWL] Completed crawling all main menus
[ENHANCED_CRAWL] Generated final results: {'total_elements_collected': 28, 'total_pages_visited': 1, 'crash_count': 0, 'last_successful_action': 'Processing main menu: Akun', 'crawl_completed': True}
[ENHANCED] Saving comprehensive results...
[ENHANCED] Saved comprehensive results to ./locators/com.kemendikdasmen.rumahpendidikan_comprehensive.json
[ENHANCED] Generating Gherkin test scenarios...
[GHERKIN] Starting Gherkin scenario generation...
