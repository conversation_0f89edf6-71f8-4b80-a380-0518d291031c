
================================================================================
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
================================================================================
Script: analyze
Start Time: 2025-07-20 02:30:37
Log File: execution_logs/2025/07/20/analyze_20250720_023037.txt
Python Version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 10:07:17) [Clang 14.0.6 ]
Working Directory: /Users/<USER>/code/android_automation
================================================================================

📝 Logging started: execution_logs/2025/07/20/analyze_20250720_023037.txt
🕐 Start time: 2025-07-20 02:30:37
================================================================================
================================================================================
ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION
================================================================================
[SETUP] Starting emulator...
Emulator already running.
[SETUP] Preparing APK...
[DEBUG] Looking for APK in folder: ./apk
[DEBUG] Found 1 APK files: ['app-prod-debug (2).apk']
[DEBUG] Using APK: ./apk/app-prod-debug (2).apk
[DEBUG] APK path: ./apk/app-prod-debug (2).apk
[SETUP] Using known package: com.kemendikdasmen.rumahpendidikan
[SETUP] Checking if app is installed on emulator...
[DEBUG] Checking if com.kemendikdasmen.rumahpendidikan is installed...
[DEBUG] App installed: True
✅ com.kemendikdasmen.rumahpendidikan is already installed on emulator.
[SETUP] Using existing app installation.
[SETUP] Launching com.kemendikdasmen.rumahpendidikan...
[DEBUG] Launching app: com.kemendikdasmen.rumahpendidikan
✅ Monkey launch successful for com.kemendikdasmen.rumahpendidikan
[VERIFY] Checking if com.kemendikdasmen.rumahpendidikan is running...
[VERIFY] ✅ com.kemendikdasmen.rumahpendidikan process found
[SETUP] Connecting to Appium...
✅ Appium server is running
[DEBUG] Starting Appium session for package: com.kemendikdasmen.rumahpendidikan
[DEBUG] Attempting to resolve main activity...
[DEBUG] Found main activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Creating Appium session with activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Connecting to Appium server at http://localhost:4723...
✅ Appium session started successfully!

============================================================
SMART WAIT SYSTEM - ENSURING APP IS FULLY LOADED
============================================================
[SMART_WAIT] Waiting for application to fully load (max 60s)...
[SMART_WAIT] Check 1 - Analyzing page stability...
[SMART_WAIT]   Elements: 17, Page size: 21682, Loading indicators: 0
[SMART_WAIT]   ⏳ Page still changing (elements: 0→17, size: 0→21682)
[SMART_WAIT] Check 25 - Analyzing page stability...
[SMART_WAIT]   Elements: 17, Page size: 21682, Loading indicators: 0
[SMART_WAIT]   ✅ Page appears stable (1/3)
[SMART_WAIT] ⚠️ Timeout reached (96.9s), proceeding anyway...
[SMART_WAIT]   Final state: 17 elements detected
[ELEMENT_WAIT] Waiting for key elements to appear...
[ELEMENT_WAIT] Check 1 - Looking for key elements...
[ELEMENT_WAIT] ✅ Key elements found:
[ELEMENT_WAIT]   - 1 elements matching '//*[@text='Ruang GTK' or @content-desc='Ruang GTK'...'
[ELEMENT_WAIT]   - 1 elements matching '//*[@text='Ruang Murid' or @content-desc='Ruang Mu...'
[ELEMENT_WAIT]   - 1 elements matching '//*[@text='Ruang Sekolah' or @content-desc='Ruang ...'
[ELEMENT_WAIT] ✅ App appears ready! (16.0s)

✅ SMART WAIT COMPLETE - Application is ready for automation!
============================================================

[ENHANCED] Initializing comprehensive crawler...
[ENHANCED] Starting comprehensive crawling process...
[ENHANCED_CRAWL] Starting comprehensive app crawling...
[STATE] Loaded state from state_com.kemendikdasmen.rumahpendidikan.pkl
[ENHANCED_CRAWL] Resuming from previous state...
[ENHANCED_CRAWL] Resuming crawl from saved state...
[ENHANCED_CRAWL] Continuing from menu index 0
[ENHANCED_CRAWL] Continuing with menu 1/16: Ruang GTK
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang GTK: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 2/16: Ruang Murid
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Murid: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 3/16: Ruang Sekolah
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Sekolah: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 4/16: Ruang Bahasa
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Bahasa: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 5/16: Ruang Pemerintah
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Pemerintah: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 6/16: Ruang Mitra
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Mitra: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 7/16: Ruang Publik
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Publik: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 8/16: Ruang Orang Tua
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang Orang Tua: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 9/16: Sumber Belajar
[ENHANCED_CRAWL] Error in enhanced menu finding for Sumber Belajar: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 10/16: Pusat Perbukuan
[ENHANCED_CRAWL] Error in enhanced menu finding for Pusat Perbukuan: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 11/16: Pengelolaan Kinerja
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Pengelolaan Kinerja: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 12/16: Lihat Semua
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Lihat Semua: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 13/16: Butuh Bantuan?
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Butuh Bantuan?: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 14/16: Ruang
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Ruang: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 15/16: Pemberitahuan
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Pemberitahuan: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Continuing with menu 16/16: Akun
[ENHANCED_CRAWL] Error scrolling to top: Message: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'GET /window/current/size' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.getWindowSize (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:90:5)
    at AndroidUiautomator2Driver.getWindowRect (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/viewport.js:61:27)
[ENHANCED_CRAWL] Error in enhanced menu finding for Akun: 'EnhancedAppCrawler' object has no attribute '_click_menu_item'
[ENHANCED_CRAWL] Generated final results: {'total_elements_collected': 28, 'total_pages_visited': 1, 'crash_count': 0, 'last_successful_action': 'Continuing with menu: Akun', 'crawl_completed': True}
[ENHANCED] Saving comprehensive results...
[ENHANCED] Saved comprehensive results to ./locators/com.kemendikdasmen.rumahpendidikan_comprehensive.json
[ENHANCED] Generating Gherkin test scenarios...
[GHERKIN] Starting Gherkin scenario generation...
[GHERKIN] Gherkin generation completed
[ENHANCED] Saved Gherkin scenarios to ./features/com.kemendikdasmen.rumahpendidikan.feature

================================================================================
COMPREHENSIVE CRAWL SUMMARY
================================================================================
Total elements collected: 28
Total pages visited: 1
Crash recovery attempts: 0
Crawl completed successfully: True
Last successful action: Continuing with menu: Akun

Final navigation path: Main Page
Maximum depth reached: 0

================================================================================
ANALYSIS COMPLETE - Enhanced comprehensive crawling finished successfully!
================================================================================
[CLEANUP] Appium session closed
📊 Log summary created: execution_logs/execution_summary.txt
================================================================================
🕐 End time: 2025-07-20 02:38:00
⏱️  Total duration: 0:07:22.620502
📝 Log saved to: execution_logs/2025/07/20/analyze_20250720_023037.txt


================================================================================
EXECUTION COMPLETED
================================================================================
End Time: 2025-07-20 02:38:00
Total Duration: 0:07:22.620502
Exit Status: SUCCESS
================================================================================
