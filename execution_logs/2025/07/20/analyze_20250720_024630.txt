
================================================================================
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
================================================================================
Script: analyze
Start Time: 2025-07-20 02:46:30
Log File: execution_logs/2025/07/20/analyze_20250720_024630.txt
Python Version: 3.12.4 | packaged by Anaconda, Inc. | (main, Jun 18 2024, 10:07:17) [Clang 14.0.6 ]
Working Directory: /Users/<USER>/code/android_automation
================================================================================

📝 Logging started: execution_logs/2025/07/20/analyze_20250720_024630.txt
🕐 Start time: 2025-07-20 02:46:30
================================================================================
================================================================================
ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION
================================================================================
[SETUP] Starting emulator...
Emulator already running.
[SETUP] Preparing APK...
[DEBUG] Looking for APK in folder: ./apk
[DEBUG] Found 1 APK files: ['app-prod-debug (2).apk']
[DEBUG] Using APK: ./apk/app-prod-debug (2).apk
[DEBUG] APK path: ./apk/app-prod-debug (2).apk
[SETUP] Using known package: com.kemendikdasmen.rumahpendidikan
[SETUP] Checking if app is installed on emulator...
[DEBUG] Checking if com.kemendikdasmen.rumahpendidikan is installed...
[DEBUG] App installed: True
✅ com.kemendikdasmen.rumahpendidikan is already installed on emulator.
[SETUP] Using existing app installation.
[SETUP] Launching com.kemendikdasmen.rumahpendidikan...
[DEBUG] Launching app: com.kemendikdasmen.rumahpendidikan
✅ Monkey launch successful for com.kemendikdasmen.rumahpendidikan
[VERIFY] Checking if com.kemendikdasmen.rumahpendidikan is running...
[VERIFY] ✅ com.kemendikdasmen.rumahpendidikan process found
[SETUP] Connecting to Appium...
✅ Appium server is running
[DEBUG] Starting Appium session for package: com.kemendikdasmen.rumahpendidikan
[DEBUG] Attempting to resolve main activity...
[DEBUG] Found main activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Creating Appium session with activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Connecting to Appium server at http://localhost:4723...
✅ Appium session started successfully!

============================================================
SMART WAIT SYSTEM - ENSURING APP IS FULLY LOADED
============================================================
[SMART_WAIT] Waiting for application to fully load (max 60s)...
[SMART_WAIT] Check 1 - Analyzing page stability...
[SMART_WAIT]   Elements: 17, Page size: 21682, Loading indicators: 0
[SMART_WAIT]   ⏳ Page still changing (elements: 0→17, size: 0→21682)
[SMART_WAIT] Check 25 - Analyzing page stability...
[SMART_WAIT]   Elements: 17, Page size: 21682, Loading indicators: 0
[SMART_WAIT]   ✅ Page appears stable (1/3)
[SMART_WAIT] ⚠️ Timeout reached (96.4s), proceeding anyway...
[SMART_WAIT]   Final state: 17 elements detected
[ELEMENT_WAIT] Waiting for key elements to appear...
[ELEMENT_WAIT] Check 1 - Looking for key elements...
[ELEMENT_WAIT] ✅ Key elements found:
[ELEMENT_WAIT]   - 1 elements matching '//*[@text='Ruang GTK' or @content-desc='Ruang GTK'...'
[ELEMENT_WAIT]   - 1 elements matching '//*[@text='Ruang Murid' or @content-desc='Ruang Mu...'
[ELEMENT_WAIT]   - 1 elements matching '//*[@text='Ruang Sekolah' or @content-desc='Ruang ...'
[ELEMENT_WAIT] ✅ App appears ready! (16.0s)

✅ SMART WAIT COMPLETE - Application is ready for automation!
============================================================

[ENHANCED] Initializing comprehensive crawler...
[ENHANCED] Starting comprehensive crawling process...
[ENHANCED_CRAWL] Starting comprehensive app crawling...
[STATE] Loaded state from state_com.kemendikdasmen.rumahpendidikan.pkl
[ENHANCED_CRAWL] Resuming from previous state...
[ENHANCED_CRAWL] Resuming crawl from saved state...
[ENHANCED_CRAWL] Continuing from menu index 0
[ENHANCED_CRAWL] Continuing with menu 1/16: Ruang GTK
[SCROLL_TO_TOP] Checking if scroll is needed...
[SCROLL_CHECK] ✅ Content unchanged - likely at top
[SCROLL_TO_TOP] ✅ Already at top of page - skipping scroll to prevent refresh
[CLICK_MENU] Attempting to click menu: Ruang GTK
[CLICK_MENU] Found Ruang GTK using pattern: //*[@text='Ruang GTK' or @content-desc='Ruang GTK'...
[CLICK_MENU] Error with pattern //*[@text='Ruang GTK' or @cont...: 'SmartActionClicker' object has no attribute 'smart_click'
[CLICK_MENU] Found Ruang GTK using pattern: //*[contains(@text, 'Ruang GTK') or contains(@cont...
[CLICK_MENU] Error with pattern //*[contains(@text, 'Ruang GTK...: 'SmartActionClicker' object has no attribute 'smart_click'
[CLICK_MENU] Found Ruang GTK using pattern: //android.widget.ImageView[@content-desc='Ruang GT...
[CLICK_MENU] Error with pattern //android.widget.ImageView[@co...: 'SmartActionClicker' object has no attribute 'smart_click'
[CLICK_MENU] ❌ Could not find or click Ruang GTK
[ENHANCED_CRAWL] Menu Ruang GTK not found, trying with scrolling...
[CLICK_MENU] Attempting to click menu: Ruang GTK
[CLICK_MENU] Found Ruang GTK using pattern: //*[contains(@text, 'Ruang GTK') or contains(@cont...
[CLICK_MENU] Error with pattern //*[contains(@text, 'Ruang GTK...: 'SmartActionClicker' object has no attribute 'smart_click'
[CLICK_MENU] ❌ Could not find or click Ruang GTK
[CLICK_MENU] Attempting to click menu: Ruang GTK
[CLICK_MENU] ❌ Could not find or click Ruang GTK
[CLICK_MENU] Attempting to click menu: Ruang GTK
[CLICK_MENU] ❌ Could not find or click Ruang GTK
[CLICK_MENU] Attempting to click menu: Ruang GTK
[CLICK_MENU] Found Ruang GTK using pattern: //*[contains(@text, 'Ruang GTK') or contains(@cont...
[CLICK_MENU] Error with pattern //*[contains(@text, 'Ruang GTK...: 'SmartActionClicker' object has no attribute 'smart_click'
[CLICK_MENU] ❌ Could not find or click Ruang GTK
[CLICK_MENU] Attempting to click menu: Ruang GTK
[CLICK_MENU] ❌ Could not find or click Ruang GTK
[ENHANCED_CRAWL] Could not find Ruang GTK even after scrolling
[ENHANCED_CRAWL] Continuing with menu 2/16: Ruang Murid
[SCROLL_TO_TOP] Checking if scroll is needed...
[SCROLL_CHECK] ✅ Content unchanged - likely at top
[SCROLL_TO_TOP] ✅ Already at top of page - skipping scroll to prevent refresh
[CLICK_MENU] Attempting to click menu: Ruang Murid
[CLICK_MENU] Found Ruang Murid using pattern: //*[contains(@text, 'Ruang Murid') or contains(@co...
[CLICK_MENU] Error with pattern //*[contains(@text, 'Ruang Mur...: 'SmartActionClicker' object has no attribute 'smart_click'
[CLICK_MENU] ❌ Could not find or click Ruang Murid
[ENHANCED_CRAWL] Menu Ruang Murid not found, trying with scrolling...
[CLICK_MENU] Attempting to click menu: Ruang Murid
[CLICK_MENU] ❌ Could not find or click Ruang Murid
[CLICK_MENU] Attempting to click menu: Ruang Murid
[CLICK_MENU] Found Ruang Murid using pattern: //*[contains(@text, 'Ruang Murid') or contains(@co...
[CLICK_MENU] Error with pattern //*[contains(@text, 'Ruang Mur...: 'SmartActionClicker' object has no attribute 'smart_click'
[CLICK_MENU] ❌ Could not find or click Ruang Murid
[CLICK_MENU] Attempting to click menu: Ruang Murid
