
================================================================================
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
================================================================================
Script: analyze
Start Time: 2025-07-20 18:58:02
Log File: execution_logs/2025/07/20/analyze_20250720_185802.txt
Python Version: 3.9.7 (default, Aug  3 2023, 19:02:34) 
[Clang 14.0.3 (clang-1403.*********)]
Working Directory: /Users/<USER>/code/android_automation
================================================================================

📝 Logging started: execution_logs/2025/07/20/analyze_20250720_185802.txt
🕐 Start time: 2025-07-20 18:58:02
================================================================================
================================================================================
ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION
================================================================================
[SETUP] Starting emulator...
Emulator already running.
[SETUP] Preparing APK...
[DEBUG] Looking for APK in folder: ./apk
[DEBUG] Found 1 APK files: ['app-prod-debug (2).apk']
[DEBUG] Using APK: ./apk/app-prod-debug (2).apk
[DEBUG] APK path: ./apk/app-prod-debug (2).apk
[SETUP] Using known package: com.kemendikdasmen.rumahpendidikan
[SETUP] Checking if app is installed on emulator...
[DEBUG] Checking if com.kemendikdasmen.rumahpendidikan is installed...
[DEBUG] App installed: True
✅ com.kemendikdasmen.rumahpendidikan is already installed on emulator.
[SETUP] Using existing app installation.
[SETUP] Launching com.kemendikdasmen.rumahpendidikan...
[DEBUG] Launching app: com.kemendikdasmen.rumahpendidikan
✅ Monkey launch successful for com.kemendikdasmen.rumahpendidikan
[VERIFY] Checking if com.kemendikdasmen.rumahpendidikan is running...
[VERIFY] ✅ com.kemendikdasmen.rumahpendidikan process found
[SETUP] Connecting to Appium...
✅ Appium server is running
[DEBUG] Starting Appium session for package: com.kemendikdasmen.rumahpendidikan
[DEBUG] Attempting to resolve main activity...
[DEBUG] Found main activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Creating Appium session with activity: com.kemendikdasmen.rumahpendidikan/.MainActivity
[DEBUG] Connecting to Appium server at http://localhost:4723...
✅ Appium session started successfully!

============================================================
SMART WAIT SYSTEM - ENSURING APP IS FULLY LOADED
============================================================
[SMART_WAIT] Waiting for application to fully load (max 60s)...
[SMART_WAIT] Check 1 - Analyzing page stability...
