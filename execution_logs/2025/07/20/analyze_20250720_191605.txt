
================================================================================
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
================================================================================
Script: analyze
Start Time: 2025-07-20 19:16:05
Log File: execution_logs/2025/07/20/analyze_20250720_191605.txt
Python Version: 3.9.7 (default, Aug  3 2023, 19:02:34) 
[Clang 14.0.3 (clang-1403.*********)]
Working Directory: /Users/<USER>/code/android_automation
================================================================================

📝 Logging started: execution_logs/2025/07/20/analyze_20250720_191605.txt
🕐 Start time: 2025-07-20 19:16:05
================================================================================
================================================================================
ENHANCED ANDROID APP ANALYZER - COMPREHENSIVE ELEMENT COLLECTION
================================================================================
[SETUP] Starting emulator...
Emulator already running.
[SETUP] Preparing APK...
[DEBUG] Looking for APK in folder: ./apk
[DEBUG] Found 1 APK files: ['app-prod-debug (2).apk']
[DEBUG] Using APK: ./apk/app-prod-debug (2).apk
[DEBUG] APK path: ./apk/app-prod-debug (2).apk
[SETUP] Using known package: com.kemendikdasmen.rumahpendidikan
[SETUP] Checking if app is installed on emulator...
[DEBUG] Checking if com.kemendikdasmen.rumahpendidikan is installed...
[DEBUG] App installed: True
✅ com.kemendikdasmen.rumahpendidikan is already installed on emulator.
[SETUP] Using existing app installation.
[SETUP] Launching com.kemendikdasmen.rumahpendidikan...
[DEBUG] Launching app: com.kemendikdasmen.rumahpendidikan
