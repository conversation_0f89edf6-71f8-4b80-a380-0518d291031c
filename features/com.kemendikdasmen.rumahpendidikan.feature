Feature: Application Navigation
As an app user
I want to navigate through the main menu and submenus
So that I can access all features of the application
Scenario Outline: Navigate from Main Page to Submenu <submenu>
Given I am on the main page
When I tap on "<Submenu>" tab
Then I should see the "<submenu>" page
And I should be able to navigate through its features/options
Examples:
| submenu                          |
| Ruang                            |
| Pemberitahuan                     |
| Akun                             |
| Portal pembelajaran digital       |
| Pengelolaan Kinerja               |
| Ruang GTK                        |
| Ruang Murid                      |
| Ruang Sekolah                    |
| Ruang Bahasa                     |
| Ruang Pemerintah                 |
| Ruang Mitra                      |
| Ruang Publik                     |
| Ruang Orang Tua                  |
Feature: Clickable Elements Interaction
As a user of the application
I want to interact with clickable elements
So that I can perform specific actions within the app
Scenario Outline: Perform an action on <ClickableElement>
Given I am on the <submenu> page
When I tap on "<ClickableElement>"
Then the expected outcome should occur (describe the result/action)
Examples:
| submenu          | ClickableElement     | ExpectedOutcome                                |
| Ruang            | Akun                  | User is taken to their account settings page   |
| Portal pembelajaran digital | Pusat Perbukuan   | Access to the official textbooks portal is granted |
Feature: Content Verification
As a user of the application
I want to verify important information/text on pages
So that I can ensure accuracy and relevance of content
Scenario Outline: Verify <Content> on <Page> page
Given I am on the "<page>" page
When I read "<content>"
Then "<description of expected outcome>"
Feature: Cross-Platform Scenarios
As an app user
I want to ensure cross-platform compatibility
So that all users can have a consistent experience across devices and platforms
Scenario Outline: Verify page loading on different platform contexts <Page>
Given I am using the application on <context>
When I navigate to "<page>"
Then it should load correctly with no discrepancies in layout or functionality
Feature: Error Handling and Edge Cases
As a user of the application
I want to handle potential errors gracefully
So that I can have a seamless experience even when unexpected issues arise
Scenario Outline: Handle an error on <Page> page due to <ErrorType>
Given I am on "<page>" page
When <error causing action> occurs
Then the application should display a user-friendly message or guide me through troubleshooting steps