Feature: Main Page Navigation
As a user of the application com.kemendikdasmen.rumahpendidikan
I want to navigate through different sections using the main navigation menus and tabs
So that I can access all available features and information within the app
Scenario Outline: Navigate Through Menus on Main Page
Given I am on the main page
When I tap on "<Menu>" menu item
Then I should see the corresponding section's screen with all relevant content displayed properly.
Examples:
| Menu                            | Expected Content                                             |
| Ruang GTK                       | Teacher workspace, performance management options             |
| Portal pembelajaran digital      | Access to digital learning resources for various subjects     |
| Pengelolaan Kinerja              | Performance management documentation and guidelines           |
| Sudah Tahu Informasi Ini?        | Confirmation message about the already presented information |
Scenario Outline: Interact with Clickable Elements on Main Page
Given I am on the main page
When I tap on "<Element>"
Then I should see an expected action or be redirected to a new screen.
Examples:
| Element                        | Expected Action/Outcome                                     |
| Beranda                         | Home page displays all services and tabs available           |
| Akun                            | User account information, settings management                |
| Pengelolaan Kinerja              | Performance documentation, guidelines displayed properly     |
| Sudah Tahu Informasi Ini?        | Confirmation message about the already presented information |
Scenario Outline: Verify Content Accuracy on Main Page
Given I am on the main page
When I observe "<Content>"
Then it should match with expected text/information provided.
Examples:
| Content                          | Expected Text/Information                                     |
| Ruang GTK description            | Description of teacher workspace features and functionalities  |
| Portal pembelajaran digital       | Details about the digital learning portal's offerings        |
| Pengelolaan Kinerja guidelines   | Guidelines for performance management documentation           |
| Sudah Tahu Informasi Inj?         | Confirmation message text                                     |
Scenario Outline: Cross-Platform Navigation Consistency
Given I am on the main page in "<Context>"
When I navigate through menus and tabs using native or webview interfaces
Then all elements should be consistently displayed across different platforms.
Examples:
| Context                        | Expected Behavior                                            |
| Native                          | All screens, links, and buttons are functional               |
| WebView                         | Screenshots of each screen to verify visual consistency       |
| ChromeContext                   | Verify that the app can be accessed via Chrome's intent launcher|
Scenario Outline: Error Handling on Main Page
Given I am on the main page
When I attempt to perform "<Action>" with invalid data or in an incorrect manner
Then I should see a meaningful error message and the app should remain stable.
Examples:
| Action                        | Invalid Input/Scenario                                       |
| Tap on "Ruang GTK" without account login| Error message prompting to log in first                |
| Enter wrong credentials when logging in  | Error message indicating incorrect username or password   |
| Attempting to access restricted content as a guest | Redirect to the main page with an information message about user permissions.