{"crawl_summary": {"total_elements_collected": 28, "total_pages_visited": 1, "crash_count": 0, "last_successful_action": "Processing main menu: <PERSON><PERSON><PERSON>", "crawl_completed": true}, "navigation_path": {"final_path": ["Main Page"], "final_depth": 0, "context_history": ["native"]}, "collected_elements": [{"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 378, "width": 1148, "x": 63, "y": 487}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 378, "width": 1148, "x": 63, "y": 487}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang GTK", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang Bahasa", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang Publik", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 457, "width": 1307, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "Sudah Tahu Informasi Ini?", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 457, "width": 1307, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.view.View", "clickable": true, "visible": true, "bounds": {"height": 457, "width": 1307, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "text": "", "content_desc": "", "resource_id": "", "class_name": "android.view.View", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang Guru dan <PERSON> (GTK)\nSambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!\nTahun ajaran baru tinggal menghitung hari, Bapak/Ibu guru apakah sudah ready?Yuk mulai siapkan diri Anda untuk menyambut tahun ajaran baru dengan penuh inspirasi!&nbsp;Ruang GTK dan Ruang Murid berkol...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "", "resource_id": "", "class_name": "android.view.View", "clickable": true, "visible": true, "bounds": {"height": 365, "width": 1314, "x": 63, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:00.861264"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.widget.Button[1]", "text": "", "content_desc": "<PERSON><PERSON> Ban<PERSON>?\n<PERSON><PERSON> pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 368, "width": 1314, "x": 63, "y": 2378}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:00.861264"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Kemendikdasmen Berikan Apresiasi <PERSON>h Inovatif dalam Meningkatkan Kualitas Pendidikan\nJakarta, Kemendikdasmen&nbsp;– Sebagai bentuk penghargaan terhadap dedikasi dan komitmen daerah-daerah dalam memajukan sektor pendidikan, Kementerian Pendidikan Das<PERSON> dan <PERSON> (Kemendikdasmen) men...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:19.835485"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Peluncuran Tiga Inisiatif Penting untuk Meningkatkan Pengembangan Anak Usia Dini di Asia Tenggara\nJakarta, 19 Desember 2024&nbsp;– Anak-anak adalah kunci pembangunan bangsa di masa depan. <PERSON><PERSON><PERSON> adalah penerima tongkat estafet kepemimpinan bangsa ini di kemudian hari. Tentunya mereka harus dipersi...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:39.755884"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON>ar <PERSON><PERSON> (ТKА)\n<PERSON> jawab <PERSON>utar TES KEMAMPUAN AKADEMIK (ТКА) merupakan bentuk asesmen terstandar nasional yang dirancang untuk mengukur capaian akademik murid pada mata pelajaran tertentu sesuai dengan kurikulum ...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:58.560496"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Kemendikdasmen Dorong Pemerataan Kualitas Pendidikan bagi Sekolah Negeri dan Swasta\n Pekanbaru, 19 Desember 2024&nbsp;– <PERSON><PERSON> rangka mewujudkan visi pendidikan bermutu untuk semua, Kementerian Pendidikan Dasar dan <PERSON> (Kemendikdasmen) terus memperkuat komitmennya dalam memastika...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:07:57.361859"}], "page_states": {"Main Page": {"page_name": "Main Page", "context": "native", "navigation_level": "main_page", "elements": [{"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 378, "width": 1148, "x": 63, "y": 487}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 378, "width": 1148, "x": 63, "y": 487}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 206, "width": 360, "x": 0, "y": 2830}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:27.730765"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang GTK", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang Bahasa", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang Publik", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": true, "bounds": {"height": 2, "width": 316, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 457, "width": 1307, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "Sudah Tahu Informasi Ini?", "resource_id": "", "class_name": "android.view.View", "clickable": false, "visible": true, "bounds": {"height": 457, "width": 1307, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "class_name": "android.view.View", "clickable": true, "visible": true, "bounds": {"height": 457, "width": 1307, "x": 67, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "text": "", "content_desc": "", "resource_id": "", "class_name": "android.view.View", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Ruang Guru dan <PERSON> (GTK)\nSambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!\nTahun ajaran baru tinggal menghitung hari, Bapak/Ibu guru apakah sudah ready?Yuk mulai siapkan diri Anda untuk menyambut tahun ajaran baru dengan penuh inspirasi!&nbsp;Ruang GTK dan Ruang Murid berkol...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:04:40.275528"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "text": "", "content_desc": "", "resource_id": "", "class_name": "android.view.View", "clickable": true, "visible": true, "bounds": {"height": 365, "width": 1314, "x": 63, "y": 144}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:00.861264"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.widget.Button[1]", "text": "", "content_desc": "<PERSON><PERSON> Ban<PERSON>?\n<PERSON><PERSON> pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan", "resource_id": "", "class_name": "android.widget.Button", "clickable": true, "visible": true, "bounds": {"height": 368, "width": 1314, "x": 63, "y": 2378}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:00.861264"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Kemendikdasmen Berikan Apresiasi <PERSON>h Inovatif dalam Meningkatkan Kualitas Pendidikan\nJakarta, Kemendikdasmen&nbsp;– Sebagai bentuk penghargaan terhadap dedikasi dan komitmen daerah-daerah dalam memajukan sektor pendidikan, Kementerian Pendidikan Das<PERSON> dan <PERSON> (Kemendikdasmen) men...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:19.835485"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Peluncuran Tiga Inisiatif Penting untuk Meningkatkan Pengembangan Anak Usia Dini di Asia Tenggara\nJakarta, 19 Desember 2024&nbsp;– Anak-anak adalah kunci pembangunan bangsa di masa depan. <PERSON><PERSON><PERSON> adalah penerima tongkat estafet kepemimpinan bangsa ini di kemudian hari. Tentunya mereka harus dipersi...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:39.755884"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "<PERSON>ar <PERSON><PERSON> (ТKА)\n<PERSON> jawab <PERSON>utar TES KEMAMPUAN AKADEMIK (ТКА) merupakan bentuk asesmen terstandar nasional yang dirancang untuk mengukur capaian akademik murid pada mata pelajaran tertentu sesuai dengan kurikulum ...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:06:58.560496"}, {"xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "text": "", "content_desc": "Kemendikdasmen Dorong Pemerataan Kualitas Pendidikan bagi Sekolah Negeri dan Swasta\n Pekanbaru, 19 Desember 2024&nbsp;– <PERSON><PERSON> rangka mewujudkan visi pendidikan bermutu untuk semua, Kementerian Pendidikan Dasar dan <PERSON> (Kemendikdasmen) terus memperkuat komitmennya dalam memastika...\nBaca Selengkapnya", "resource_id": "", "class_name": "android.widget.ImageView", "clickable": true, "visible": false, "bounds": {}, "page_context": "native", "navigation_level": "main_page", "collected_at": "2025-07-20T02:07:57.361859"}], "fully_explored": true, "scroll_position": 0, "page_source_hash": "3026a71a5fa88540f3bcbd90ab8df069", "timestamp": "2025-07-20T02:08:13.252426"}}, "visited_pages": ["Main Page"]}