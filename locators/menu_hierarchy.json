{"all_elements": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.FrameLayout", "text": "", "content_desc": "", "resource_id": "android:id/content", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "Ruang GTK", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "Ruang Bahasa", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "Ruang Publik", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]"}], "all_elements_full_xml": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "hierarchy", "text": "\n  ", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]", "index": "0", "rotation": "0", "width": "1440", "height": "3120"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.FrameLayout", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]", "index": "0", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.LinearLayout", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]", "index": "0", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "1", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.FrameLayout", "text": "", "content_desc": "", "resource_id": "android:id/content", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "index": "0", "resource-id": "android:id/content", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "2", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.FrameLayout", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "index": "0", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "1", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false", "pane-title": " "}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,0][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ScrollView", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "true", "selected": "false", "bounds": "[0,144][1440,2830]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.widget.ImageView[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,214][463,326]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "index": "1", "content-desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,487][1211,865]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "index": "2", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,949][1377,1187]", "displayed": "true", "hint": "<PERSON><PERSON>..", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "index": "3", "content-desc": "Temu<PERSON> Pendidikan Anda", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[67,1439][983,1513]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "index": "4", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[67,1618][1374,2416]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[67,1618][1374,2416]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "Ruang GTK", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "0", "content-desc": "Ruang GTK", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[67,1618][383,1961]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "1", "content-desc": "<PERSON><PERSON>", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[397,1618][713,1961]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "2", "content-desc": "<PERSON><PERSON>", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[727,1618][1043,1961]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "Ruang Bahasa", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "3", "content-desc": "Ruang Bahasa", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[1057,1618][1374,1961]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "4", "content-desc": "<PERSON><PERSON>", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[67,2073][383,2416]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "5", "content-desc": "<PERSON><PERSON>", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[397,2073][713,2416]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "Ruang Publik", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "6", "content-desc": "Ruang Publik", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[727,2073][1043,2416]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "index": "7", "content-desc": "<PERSON><PERSON>", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[1057,2073][1374,2416]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "index": "5", "content-desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,2685][924,2759]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "index": "6", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,2829][1377,2830]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,2829][1377,2830]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "index": "0", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,2829][1377,2830]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "index": "0", "content-desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[63,2829][1377,2830]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.view.View", "text": "", "content_desc": "", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "index": "1", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "false", "enabled": "true", "focusable": "false", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[0,2830][1440,3120]", "displayed": "true", "a11y-important": "false", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "index": "0", "content-desc": "Beranda\nBeranda\nTab 1 of 4", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "true", "bounds": "[0,2830][360,3036]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "index": "1", "content-desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[360,2830][720,3036]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "index": "2", "content-desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[720,2830][1080,3036]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "class": "android.widget.Button", "text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "resource_id": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "index": "3", "content-desc": "Akun\n<PERSON> 4 of 4", "resource-id": "", "checkable": "false", "checked": "false", "clickable": "true", "enabled": "true", "focusable": "true", "focused": "false", "long-clickable": "false", "password": "false", "scrollable": "false", "selected": "false", "bounds": "[1080,2830][1440,3036]", "displayed": "true", "a11y-important": "true", "screen-reader-focusable": "false", "drawing-order": "0", "showing-hint": "false", "text-entry-key": "false", "dismissable": "false", "a11y-focused": "false", "heading": "false", "live-region": "0", "context-clickable": "false", "content-invalid": "false"}], "menu_hierarchy": {"name": "Main Page", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang GTK", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Bahasa", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "4"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "5"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Publik", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "6"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "7"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "5"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "true", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}], "children": [{"name": "null", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Back", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.EditText", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.EditText[1]", "element_type": "android.widget.EditText", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}], "children": [{"name": "Back", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang GTK", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Bahasa", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "4"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "5"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Publik", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "6"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "7"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "5"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "true", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}], "children": [{"name": "Ruang GTK", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON> dan <PERSON> (GTK)", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Sumber inspirasi peningkatan kompetensi serta kinerja Guru dan <PERSON> (GTK)", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Belajar Berkelanjutan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Diklat\nPelatihan terbimbing menggunakan LMS", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Sertifikasi Pendidik\nPendidikan Profesi Guru (PPG) bagi Guru <PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> untuk pengembangan kompetensi guru dan tendik", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Komunitas\n144.000+ komunitas guru & tendik berbagi praktik baik", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}], "children": [{"name": "null", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang GTK", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Bahasa", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "4"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "5"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Publik", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "6"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.widget.ImageView[1]", "element_type": "android.widget.ImageView", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "7"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "5"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "true", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}], "children": [{"name": "<PERSON><PERSON>", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Sumber belajar yang beragam dan inspiratif untuk Murid Indonesia", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Buku Bacaan Digital\nPusat Buku Digital untuk Gerakan Literasi Nasional", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Album Lagu Anak\nAlbum <PERSON><PERSON>an Anak Indonesia Hebat", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Bank Soal\nKumpulan soal latihan berbagai mata pelajaran", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Rapor Digital\nLaporan penilaian dan evaluasi hasil belajar siswa", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "4"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "bounds": "[397,1618][713,1961]", "element_type": "android.widget.ImageView"}}, {"name": "<PERSON><PERSON>", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Data terpusat untuk akses pen<PERSON>an kebutuhan <PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Profil Sekolah\nPortal informasi profil dan data seluruh sekolah di Indonesia", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON> Pendidikan\nSistem evaluasi dan pemetaan mutu satuan pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Rencana Kegiatan dan Belanja Sekolah\nSistem pengelolaan rencana kegiatan dan belanja sekolah", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Bantuan Operasional\nSistem pengelolaan dan pelaporan dana bantuan operasional sekolah", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "4"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "bounds": "[727,1618][1043,1961]", "element_type": "android.widget.ImageView"}}, {"name": "Ruang Bahasa", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Ruang Bahasa", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Platform digital Bahasa Indonesia agar lebih dikenal dan dimanfaatkan dengan baik", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Kamus Bahasa\nPortal Kamus Besar Bahasa Indonesia (KBBI)", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> produk-produk pener<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Layanan UKBI\nPortal Uji <PERSON>iran Berbahasa Indonesia (UKBI)", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "BIPA Daring\nProgram pembelajaran Bahasa Indonesia bagi penutur asing", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Bahasa", "bounds": "[1057,1618][1374,1961]", "element_type": "android.widget.ImageView"}}, {"name": "<PERSON><PERSON>", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>n pemerintah daerah untuk mengelola sumber daya sekolah hingga evaluasi pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Neraca Pendidikan Daerah\nLaman informasi potret kinerja pendidikan di daerah", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON>or Pendidi<PERSON> Daerah\nSistem evaluasi dan pemetaan mutu pendidikan daerah", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah\nSistem informasi untuk memfasilitasi Dinas Pendidikan dalam melakukan pengawasan tata kelola anggaran.", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "bounds": "[67,2073][383,2416]", "element_type": "android.widget.ImageView"}}, {"name": "<PERSON><PERSON>", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON>, serta inisiasi program kemitraan pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Mi<PERSON> Barjas Pendidikan\nSistem kemitraan pengadaan barang dan jasa pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON>kan\nWadah inisiasi dan pengembangan kemitraan pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Relawan Pendidikan\nPortal kolaborasi relawan untuk kemajuan pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "bounds": "[397,2073][713,2416]", "element_type": "android.widget.ImageView"}}, {"name": "Ruang Publik", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ScrollView", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]", "element_type": "android.widget.ScrollView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Ruang Publik", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Informasi dan materi pendidikan, serta layanan pengaduan untuk masyarakat umum", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Bantuan Pendidikan\nPortal informasi bantuan dan beasiswa pendidikan nasional", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Layanan Informasi dan Pengaduan\nLayanan pengaduan dan penyam<PERSON>ian masukan pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Informasi Data Pendidikan\nData resmi pendidikan dan kebahasaan dalam satu portal", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Publikasi Ilmiah\nPortal hasil publikasi penelitian dan kajian pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "4"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang Publik", "bounds": "[727,2073][1043,2416]", "element_type": "android.widget.ImageView"}}, {"name": "<PERSON><PERSON>", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "Sarana partisipasi orang tua melalui pantauan capaian Murid dan dukungan belajar di rumah", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Layanan Informasi dan Pengaduan\nLayanan pengaduan dan penyam<PERSON>ian masukan pendidikan", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Panduan Pendampingan\nPortal informasi terpadu untuk pendampingan pendidikan oleh orang tua", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Konsultasi Pendidikan\nLayanan konsultasi orang tua dengan pihak sekolah", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "<PERSON><PERSON>", "bounds": "[1057,2073][1374,2416]", "element_type": "android.widget.ImageView"}}, {"name": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "locators": [{"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "android:id/content", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.FrameLayout", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]", "element_type": "android.widget.FrameLayout", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.webkit.WebView", "text": "<PERSON><PERSON><PERSON> | Ruang Murid", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]", "element_type": "android.webkit.WebView", "clickable": "false", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "true", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "__next", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Image", "text": "rumah-pendidikan-icon", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.Image[1]", "element_type": "android.widget.Image", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "Bagian dari", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "<PERSON><PERSON><PERSON>", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "<PERSON><PERSON><PERSON>", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "Selamat datang di", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Image", "text": "logo-new", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.widget.Image[1]", "element_type": "android.widget.Image", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "<PERSON><PERSON>", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "Apa yang akan kamu dapatkan di sini?", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "4"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "main-content", "class_name": "android.view.View", "text": "", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]", "element_type": "android.view.View", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "2"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "<PERSON><PERSON> kelas be<PERSON>a?", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "PAUD", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.RadioButton", "text": "3-4 tahun", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioGroup[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioButton[1]", "element_type": "android.widget.RadioButton", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "3-4 tahun", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioGroup[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.RadioButton", "text": "4-5 tahun", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioGroup[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioButton[1]", "element_type": "android.widget.RadioButton", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "4-5 tahun", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioGroup[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.RadioButton", "text": "5-6 tahun", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioGroup[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioButton[1]", "element_type": "android.widget.RadioButton", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "0"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.TextView", "text": "5-6 tahun", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.RadioGroup[1]/android.view.View[1]/android.view.View[1]/android.widget.TextView[1]", "element_type": "android.widget.TextView", "clickable": "false", "focusable": "false", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "1"}, {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "SD", "content_desc": "", "xpath": "/hierarchy[1]/android.widget.FrameLayout[1]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.ViewGroup[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.widget.Button[1]", "element_type": "android.widget.Button", "clickable": "true", "focusable": "true", "enabled": "true", "selected": "false", "checkable": "false", "checked": "false", "scrollable": "false", "index": "3"}], "children": [], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "bounds": "[63,2829][1377,2830]", "element_type": "android.widget.Button"}}], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "null", "bounds": "[14,183][182,351]", "element_type": "android.widget.Button"}}], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.ImageView", "text": "", "content_desc": "Ruang GTK", "bounds": "[67,1618][383,1961]", "element_type": "android.widget.ImageView"}}], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.widget.Button", "text": "", "content_desc": "Back", "bounds": "[14,155][182,323]", "element_type": "android.widget.Button"}}], "locator": {"screen_name": "com.kemendikdasmen.rumahpendidikan:.MainActivity", "package": "com.kemendikdasmen.rumahpendidikan", "resource_id": "", "class_name": "android.view.View", "text": "", "content_desc": "null", "bounds": "[63,949][1377,1187]", "element_type": "android.view.View"}}]}}