{"main_page": [{"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,487][1211,865]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "clickable": false, "class": "android.view.View", "bounds": "[67,1439][983,1513]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Ruang GTK", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,1618][383,1961]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,1618][713,1961]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,1618][1043,1961]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Ruang Bahasa", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,1618][1374,1961]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,2073][383,2416]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,2073][713,2416]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Ruang Publik", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,2073][1043,2416]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,2073][1374,2416]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,2685][924,2759]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2829][1377,2830]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[0,2830][360,3036]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[360,2830][720,3036]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[720,2830][1080,3036]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[1080,2830][1440,3036]", "page": "Main Page", "scroll_position": 0}, {"text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "clickable": false, "class": "android.view.View", "bounds": "[67,440][983,513]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "Ruang GTK", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,618][383,961]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,618][713,961]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,618][1043,961]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "Ruang Bahasa", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,618][1374,961]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,1073][383,1416]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,1073][713,1416]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "Ruang Publik", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,1073][1043,1416]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,1073][1374,1416]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1686][924,1759]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[63,1829][1377,2169]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2225][1377,2564]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2620][1377,2830]", "page": "Main Page", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,144][383,447]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,144][713,447]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "Ruang Publik", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,144][1043,447]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,144][1374,447]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,717][924,790]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[63,860][1377,1200]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[63,1256][1377,1595]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[63,1651][1377,1921]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "Sudah Tahu Informasi Ini?", "clickable": false, "class": "android.view.View", "bounds": "[56,2117][760,2190]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.view.View", "bounds": "[760,2117][1384,2190]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON>ar <PERSON><PERSON> (ТKА)\n<PERSON> jawab <PERSON>utar TES KEMAMPUAN AKADEMIK (ТКА) merupakan bentuk asesmen terstandar nasional yang dirancang untuk mengukur capaian akademik murid pada mata pelajaran tertentu sesuai dengan kurikulum ...\nBaca Selengkapnya", "clickable": true, "class": "android.widget.ImageView", "bounds": "[56,2260][1384,2830]", "page": "Main Page", "scroll_position": 2}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[63,144][1377,230]", "page": "Main Page", "scroll_position": 3}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[63,286][1377,626]", "page": "Main Page", "scroll_position": 3}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[63,682][1377,951]", "page": "Main Page", "scroll_position": 3}, {"text": "", "content_desc": "Sudah Tahu Informasi Ini?", "clickable": false, "class": "android.view.View", "bounds": "[56,1147][760,1221]", "page": "Main Page", "scroll_position": 3}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.view.View", "bounds": "[760,1147][1384,1221]", "page": "Main Page", "scroll_position": 3}, {"text": "", "content_desc": "<PERSON>ar <PERSON><PERSON> (ТKА)\n<PERSON> jawab <PERSON>utar TES KEMAMPUAN AKADEMIK (ТКА) merupakan bentuk asesmen terstandar nasional yang dirancang untuk mengukur capaian akademik murid pada mata pelajaran tertentu sesuai dengan kurikulum ...\nBaca Selengkapnya", "clickable": true, "class": "android.widget.ImageView", "bounds": "[56,1291][1384,2568]", "page": "Main Page", "scroll_position": 3}, {"text": "", "content_desc": "<PERSON><PERSON> Ban<PERSON>?\n<PERSON><PERSON> pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2820][1377,2830]", "page": "Main Page", "scroll_position": 3}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[63,144][1377,184]", "page": "Main Page", "scroll_position": 4}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[63,240][1377,509]", "page": "Main Page", "scroll_position": 4}, {"text": "", "content_desc": "Sudah Tahu Informasi Ini?", "clickable": false, "class": "android.view.View", "bounds": "[56,705][760,779]", "page": "Main Page", "scroll_position": 4}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.view.View", "bounds": "[760,705][1384,779]", "page": "Main Page", "scroll_position": 4}, {"text": "", "content_desc": "<PERSON>ar <PERSON><PERSON> (ТKА)\n<PERSON> jawab <PERSON>utar TES KEMAMPUAN AKADEMIK (ТКА) merupakan bentuk asesmen terstandar nasional yang dirancang untuk mengukur capaian akademik murid pada mata pelajaran tertentu sesuai dengan kurikulum ...\nBaca Selengkapnya", "clickable": true, "class": "android.widget.ImageView", "bounds": "[56,849][1384,2126]", "page": "Main Page", "scroll_position": 4}, {"text": "", "content_desc": "<PERSON><PERSON> Ban<PERSON>?\n<PERSON><PERSON> pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2378][1377,2746]", "page": "Main Page", "scroll_position": 4}], "menus": {"Ruang GTK": {"elements": [{"text": "", "content_desc": "<PERSON><PERSON> dan <PERSON> (GTK)", "clickable": false, "class": "android.view.View", "bounds": "[119,872][1321,1124]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "Sumber inspirasi peningkatan kompetensi serta kinerja Guru dan <PERSON> (GTK)", "clickable": false, "class": "android.view.View", "bounds": "[119,1124][1321,1264]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "Belajar Berkelanjutan", "clickable": false, "class": "android.view.View", "bounds": "[63,1383][1377,2965]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "Diklat\nPelatihan terbimbing menggunakan LMS", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1649][1321,1807]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "Sertifikasi Pendidik\nPendidikan Profesi Guru (PPG) bagi Guru <PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1919][1321,2146]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> untuk pengembangan kompetensi guru dan tendik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2258][1321,2486]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "Komunitas\n144.000+ komunitas guru & tendik berbagi praktik baik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2598][1321,2825]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,2965][1377,3120]", "page": "Submenu: Ruang GTK", "scroll_position": 0}, {"text": "", "content_desc": "Belajar Berkelanjutan", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,1596]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "Diklat\nPelatihan terbimbing menggunakan LMS", "clickable": true, "class": "android.widget.Button", "bounds": "[119,389][1321,438]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "Sertifikasi Pendidik\nPendidikan Profesi Guru (PPG) bagi Guru <PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,550][1321,777]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> untuk pengembangan kompetensi guru dan tendik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,889][1321,1117]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "Komunitas\n144.000+ komunitas guru & tendik berbagi praktik baik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1229][1321,1456]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1596][1377,2979]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan pen<PERSON><PERSON> kinerja <PERSON>a untuk pengembangan diri dan satdik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1862][1321,2160]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> Sekolah\nInfo persyaratan, tahapan, & cek kualifikasi untuk seleksi", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2272][1321,2499]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "Refleksi Kompetensi\nRekomendasi pembelajaran yang disusun personal untuk Anda", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2611][1321,2839]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "Inspirasi Pembelajaran", "clickable": false, "class": "android.view.View", "bounds": "[63,2979][1377,3120]", "page": "Submenu: Ruang GTK", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,1641]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan pen<PERSON><PERSON> kinerja <PERSON>a untuk pengembangan diri dan satdik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,524][1321,822]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> Sekolah\nInfo persyaratan, tahapan, & cek kualifikasi untuk seleksi", "clickable": true, "class": "android.widget.Button", "bounds": "[119,934][1321,1161]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "Refleksi Kompetensi\nRekomendasi pembelajaran yang disusun personal untuk Anda", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1273][1321,1501]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "Inspirasi Pembelajaran", "clickable": false, "class": "android.view.View", "bounds": "[63,1641][1377,3120]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>jar\n<PERSON> a<PERSON> & proyek, buku teks, dan seje<PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1907][1321,2134]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "CP/ATP\nCapaia<PERSON> dan <PERSON><PERSON> sesuai mata pelajaran <PERSON>a", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2246][1321,2474]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "Ide Praktik\nArtikel dan video terpilih tentang praktik pembelajaran terpusat pada murid", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2586][1321,2813]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> jejak karya, <PERSON><PERSON><PERSON>, dan kompet<PERSON>i pendidik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2925][1321,3120]", "page": "Submenu: Ruang GTK", "scroll_position": 2}, {"text": "", "content_desc": "Inspirasi Pembelajaran", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,3066]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>jar\n<PERSON> a<PERSON> & proyek, buku teks, dan seje<PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,588][1321,815]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "CP/ATP\nCapaia<PERSON> dan <PERSON><PERSON> sesuai mata pelajaran <PERSON>a", "clickable": true, "class": "android.widget.Button", "bounds": "[119,927][1321,1155]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "Ide Praktik\nArtikel dan video terpilih tentang praktik pembelajaran terpusat pada murid", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1267][1321,1494]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> jejak karya, <PERSON><PERSON><PERSON>, dan kompet<PERSON>i pendidik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1606][1321,1834]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "Video Inspirasi\nKumpulan video terpilih berisi penerapan praktik baik ", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1946][1321,2173]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> (Asesmen Murid & AKM Kelas)\nPaket soal dengan pemeriksaan otomatis dan analisis hasil", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2285][1321,2586]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "Kelas\nInformasi atau data kelompok murid berdasar<PERSON> hasil asesmen", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2698][1321,2926]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,3066][1377,3120]", "page": "Submenu: Ruang GTK", "scroll_position": 3}, {"text": "", "content_desc": "Inspirasi Pembelajaran", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,1688]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> jejak karya, <PERSON><PERSON><PERSON>, dan kompet<PERSON>i pendidik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,389][1321,456]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "Video Inspirasi\nKumpulan video terpilih berisi penerapan praktik baik ", "clickable": true, "class": "android.widget.Button", "bounds": "[119,568][1321,795]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> (Asesmen Murid & AKM Kelas)\nPaket soal dengan pemeriksaan otomatis dan analisis hasil", "clickable": true, "class": "android.widget.Button", "bounds": "[119,907][1321,1208]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "Kelas\nInformasi atau data kelompok murid berdasar<PERSON> hasil asesmen", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1320][1321,1548]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1688][1377,3120]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "Pengelolaan Pembelajaran\nDokumen rujukan untuk Pengelolaan Pembelajaran", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1954][1321,2181]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2293][1321,2451]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "Peningkatan Kompetensi\nDokumen rujukan untuk Peningkatan Kompetensi", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2563][1321,2790]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "Pengelolaan Satuan Pendidikan\nDokumen rujukan untuk Pengelolaan Satuan Pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2902][1321,3120]", "page": "Submenu: Ruang GTK", "scroll_position": 4}, {"text": "", "content_desc": "Inspirasi Pembelajaran", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,1188]", "page": "Submenu: Ruang GTK", "scroll_position": 5}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> (Asesmen Murid & AKM Kelas)\nPaket soal dengan pemeriksaan otomatis dan analisis hasil", "clickable": true, "class": "android.widget.Button", "bounds": "[119,408][1321,709]", "page": "Submenu: Ruang GTK", "scroll_position": 5}, {"text": "", "content_desc": "Kelas\nInformasi atau data kelompok murid berdasar<PERSON> hasil asesmen", "clickable": true, "class": "android.widget.Button", "bounds": "[119,821][1321,1048]", "page": "Submenu: Ruang GTK", "scroll_position": 5}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1188][1377,2770]", "page": "Submenu: Ruang GTK", "scroll_position": 5}, {"text": "", "content_desc": "Pengelolaan Pembelajaran\nDokumen rujukan untuk Pengelolaan Pembelajaran", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1454][1321,1682]", "page": "Submenu: Ruang GTK", "scroll_position": 5}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1794][1321,1951]", "page": "Submenu: Ruang GTK", "scroll_position": 5}, {"text": "", "content_desc": "Peningkatan Kompetensi\nDokumen rujukan untuk Peningkatan Kompetensi", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2063][1321,2291]", "page": "Submenu: Ruang GTK", "scroll_position": 5}, {"text": "", "content_desc": "Pengelolaan Satuan Pendidikan\nDokumen rujukan untuk Pengelolaan Satuan Pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2403][1321,2630]", "page": "Submenu: Ruang GTK", "scroll_position": 5}], "submenus": {}}, "Ruang Murid": {"elements": [{"text": "", "content_desc": "<PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,872][644,998]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Sumber belajar yang beragam dan inspiratif untuk Murid Indonesia", "clickable": false, "class": "android.view.View", "bounds": "[119,998][1321,1138]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1257][1377,3120]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1751]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Buku Bacaan Digital\nPusat Buku Digital untuk Gerakan Literasi Nasional", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1863][1321,2090]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Album Lagu Anak\nAlbum <PERSON><PERSON>an Anak Indonesia Hebat", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2202][1321,2430]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Bank Soal\nKumpulan soal latihan berbagai mata pelajaran", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2542][1321,2769]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Rapor Digital\nLaporan penilaian dan evaluasi hasil belajar siswa", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2881][1321,3109]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,3120]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[119,389][1321,393]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Buku Bacaan Digital\nPusat Buku Digital untuk Gerakan Literasi Nasional", "clickable": true, "class": "android.widget.Button", "bounds": "[119,505][1321,732]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Album Lagu Anak\nAlbum <PERSON><PERSON>an Anak Indonesia Hebat", "clickable": true, "class": "android.widget.Button", "bounds": "[119,844][1321,1072]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Bank Soal\nKumpulan soal latihan berbagai mata pelajaran", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1184][1321,1411]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Rapor Digital\nLaporan penilaian dan evaluasi hasil belajar siswa", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1751]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Riwayat Pendidikan\nCatatan perjalanan pendidikan siswa dari awal hingga jenjang terkini", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1863][1321,2090]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2202][1321,2430]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Sumber Buku Teks Pembelajaran\nJelajahi buku teks & non-teks resmi dari P<PERSON> Perbu<PERSON> Kemendikdasmen", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2542][1321,2769]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Pendidikan Jarak <PERSON><PERSON> pem<PERSON>ran jenjang pendidikan menengah secara daring dari mana saja", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2881][1321,3109]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,2770]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}, {"text": "", "content_desc": "Album Lagu Anak\nAlbum <PERSON><PERSON>an Anak Indonesia Hebat", "clickable": true, "class": "android.widget.Button", "bounds": "[119,389][1321,593]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}, {"text": "", "content_desc": "Bank Soal\nKumpulan soal latihan berbagai mata pelajaran", "clickable": true, "class": "android.widget.Button", "bounds": "[119,705][1321,933]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}, {"text": "", "content_desc": "Rapor Digital\nLaporan penilaian dan evaluasi hasil belajar siswa", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1045][1321,1272]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}, {"text": "", "content_desc": "Riwayat Pendidikan\nCatatan perjalanan pendidikan siswa dari awal hingga jenjang terkini", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1384][1321,1612]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}, {"text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1724][1321,1951]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}, {"text": "", "content_desc": "Sumber Buku Teks Pembelajaran\nJelajahi buku teks & non-teks resmi dari P<PERSON> Perbu<PERSON> Kemendikdasmen", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2063][1321,2291]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}, {"text": "", "content_desc": "Pendidikan Jarak <PERSON><PERSON> pem<PERSON>ran jenjang pendidikan menengah secara daring dari mana saja", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2403][1321,2630]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 2}], "submenus": {}}, "Ruang Sekolah": {"elements": [{"text": "", "content_desc": "<PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,872][734,998]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Data terpusat untuk akses pen<PERSON>an kebutuhan <PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,998][1321,1138]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1257][1377,3120]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Profil Sekolah\nPortal informasi profil dan data seluruh sekolah di Indonesia", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1751]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON> Pendidikan\nSistem evaluasi dan pemetaan mutu satuan pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1863][1321,2090]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Rencana Kegiatan dan Belanja Sekolah\nSistem pengelolaan rencana kegiatan dan belanja sekolah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2202][1321,2430]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Bantuan Operasional\nSistem pengelolaan dan pelaporan dana bantuan operasional sekolah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2542][1321,2769]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2881][1321,3109]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,389][1377,2770]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Profil Sekolah\nPortal informasi profil dan data seluruh sekolah di Indonesia", "clickable": true, "class": "android.widget.Button", "bounds": "[119,635][1321,863]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON> Pendidikan\nSistem evaluasi dan pemetaan mutu satuan pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,975][1321,1202]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Rencana Kegiatan dan Belanja Sekolah\nSistem pengelolaan rencana kegiatan dan belanja sekolah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1314][1321,1542]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Bantuan Operasional\nSistem pengelolaan dan pelaporan dana bantuan operasional sekolah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1654][1321,1881]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1993][1321,2221]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Pengadaan Barang dan Jasa Sekolah\nSistem digital yang membantu sekolah berbelanja di Mitra pengelola pasar daring SIPLah.", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2333][1321,2630]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}], "submenus": {}}, "Ruang Bahasa": {"elements": [{"text": "", "content_desc": "Ruang Bahasa", "clickable": false, "class": "android.view.View", "bounds": "[119,872][708,998]", "page": "Submenu: Ruang Bahasa", "scroll_position": 0}, {"text": "", "content_desc": "Platform digital Bahasa Indonesia agar lebih dikenal dan dimanfaatkan dengan baik", "clickable": false, "class": "android.view.View", "bounds": "[119,998][1321,1138]", "page": "Submenu: Ruang Bahasa", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1257][1377,2839]", "page": "Submenu: Ruang Bahasa", "scroll_position": 0}, {"text": "", "content_desc": "Kamus Bahasa\nPortal Kamus Besar Bahasa Indonesia (KBBI)", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1681]", "page": "Submenu: Ruang Bahasa", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> produk-produk pener<PERSON><PERSON><PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1793][1321,2020]", "page": "Submenu: Ruang Bahasa", "scroll_position": 0}, {"text": "", "content_desc": "Layanan UKBI\nPortal Uji <PERSON>iran Berbahasa Indonesia (UKBI)", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2132][1321,2360]", "page": "Submenu: Ruang Bahasa", "scroll_position": 0}, {"text": "", "content_desc": "BIPA Daring\nProgram pembelajaran Bahasa Indonesia bagi penutur asing", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2472][1321,2699]", "page": "Submenu: Ruang Bahasa", "scroll_position": 0}, {"text": "", "content_desc": "Ruang Bahasa", "clickable": false, "class": "android.view.View", "bounds": "[119,803][708,929]", "page": "Submenu: Ruang Bahasa", "scroll_position": 1}, {"text": "", "content_desc": "Platform digital Bahasa Indonesia agar lebih dikenal dan dimanfaatkan dengan baik", "clickable": false, "class": "android.view.View", "bounds": "[119,929][1321,1069]", "page": "Submenu: Ruang Bahasa", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1188][1377,2770]", "page": "Submenu: Ruang Bahasa", "scroll_position": 1}, {"text": "", "content_desc": "Kamus Bahasa\nPortal Kamus Besar Bahasa Indonesia (KBBI)", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1454][1321,1612]", "page": "Submenu: Ruang Bahasa", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> produk-produk pener<PERSON><PERSON><PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1724][1321,1951]", "page": "Submenu: Ruang Bahasa", "scroll_position": 1}, {"text": "", "content_desc": "Layanan UKBI\nPortal Uji <PERSON>iran Berbahasa Indonesia (UKBI)", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2063][1321,2291]", "page": "Submenu: Ruang Bahasa", "scroll_position": 1}, {"text": "", "content_desc": "BIPA Daring\nProgram pembelajaran Bahasa Indonesia bagi penutur asing", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2403][1321,2630]", "page": "Submenu: Ruang Bahasa", "scroll_position": 1}], "submenus": {}}, "Ruang Pemerintah": {"elements": [{"text": "", "content_desc": "<PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,872][880,998]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>n pemerintah daerah untuk mengelola sumber daya sekolah hingga evaluasi pendidikan", "clickable": false, "class": "android.view.View", "bounds": "[119,998][1321,1138]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1257][1377,3053]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Neraca Pendidikan Daerah\nLaman informasi potret kinerja pendidikan di daerah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1751]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON>or Pendidi<PERSON> Daerah\nSistem evaluasi dan pemetaan mutu pendidikan daerah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1863][1321,2090]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2202][1321,2430]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah\nSistem informasi untuk memfasilitasi Dinas Pendidikan dalam melakukan pengawasan tata kelola anggaran.", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2542][1321,2913]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,590][880,716]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON>n pemerintah daerah untuk mengelola sumber daya sekolah hingga evaluasi pendidikan", "clickable": false, "class": "android.view.View", "bounds": "[119,716][1321,856]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,974][1377,2770]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Neraca Pendidikan Daerah\nLaman informasi potret kinerja pendidikan di daerah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1241][1321,1468]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON>or Pendidi<PERSON> Daerah\nSistem evaluasi dan pemetaan mutu pendidikan daerah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1580][1321,1808]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1920][1321,2147]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}, {"text": "", "content_desc": "Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah\nSistem informasi untuk memfasilitasi Dinas Pendidikan dalam melakukan pengawasan tata kelola anggaran.", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2259][1321,2630]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 1}], "submenus": {}}, "Ruang Mitra": {"elements": [{"text": "", "content_desc": "<PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,872][624,998]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON>, serta inisiasi program kemitraan pendidikan", "clickable": false, "class": "android.view.View", "bounds": "[119,998][1321,1138]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1257][1377,2570]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Mi<PERSON> Barjas Pendidikan\nSistem kemitraan pengadaan barang dan jasa pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1751]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON>kan\nWadah inisiasi dan pengembangan kemitraan pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1863][1321,2090]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}, {"text": "", "content_desc": "Relawan Pendidikan\nPortal kolaborasi relawan untuk kemajuan pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2202][1321,2430]", "page": "Submenu: <PERSON><PERSON>", "scroll_position": 0}], "submenus": {}}, "Ruang Publik": {"elements": [{"text": "", "content_desc": "Ruang Publik", "clickable": false, "class": "android.view.View", "bounds": "[119,872][664,998]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "Informasi dan materi pendidikan, serta layanan pengaduan untuk masyarakat umum", "clickable": false, "class": "android.view.View", "bounds": "[119,998][1321,1138]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1257][1377,3120]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1751]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "Bantuan Pendidikan\nPortal informasi bantuan dan beasiswa pendidikan nasional", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1863][1321,2090]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "Layanan Informasi dan Pengaduan\nLayanan pengaduan dan penyam<PERSON>ian masukan pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2202][1321,2430]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "Informasi Data Pendidikan\nData resmi pendidikan dan kebahasaan dalam satu portal", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2542][1321,2769]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "Publikasi Ilmiah\nPortal hasil publikasi penelitian dan kajian pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2881][1321,3109]", "page": "Submenu: Ruang Publik", "scroll_position": 0}, {"text": "", "content_desc": "Ruang Publik", "clickable": false, "class": "android.view.View", "bounds": "[119,394][664,520]", "page": "Submenu: Ruang Publik", "scroll_position": 1}, {"text": "", "content_desc": "Informasi dan materi pendidikan, serta layanan pengaduan untuk masyarakat umum", "clickable": false, "class": "android.view.View", "bounds": "[119,520][1321,660]", "page": "Submenu: Ruang Publik", "scroll_position": 1}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,779][1377,2770]", "page": "Submenu: Ruang Publik", "scroll_position": 1}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1045][1321,1272]", "page": "Submenu: Ruang Publik", "scroll_position": 1}, {"text": "", "content_desc": "Bantuan Pendidikan\nPortal informasi bantuan dan beasiswa pendidikan nasional", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1384][1321,1612]", "page": "Submenu: Ruang Publik", "scroll_position": 1}, {"text": "", "content_desc": "Layanan Informasi dan Pengaduan\nLayanan pengaduan dan penyam<PERSON>ian masukan pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1724][1321,1951]", "page": "Submenu: Ruang Publik", "scroll_position": 1}, {"text": "", "content_desc": "Informasi Data Pendidikan\nData resmi pendidikan dan kebahasaan dalam satu portal", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2063][1321,2291]", "page": "Submenu: Ruang Publik", "scroll_position": 1}, {"text": "", "content_desc": "Publikasi Ilmiah\nPortal hasil publikasi penelitian dan kajian pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2403][1321,2630]", "page": "Submenu: Ruang Publik", "scroll_position": 1}], "submenus": {}}, "Ruang Orang Tua": {"elements": [], "submenus": {}}}}