{"timestamp": "20250720_223213", "collection_type": "hierarchical_simplified_with_locations", "total_pages": 2, "pages": {"Main Page": {"element_count": 23, "elements": [{"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,487][1211,865]", "page": "Main Page", "scroll_position": 0, "location": {"x": 63, "y": 487, "width": 1148, "height": 378, "center_x": 637, "center_y": 676}}, {"text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "clickable": false, "class": "android.view.View", "bounds": "[67,1439][983,1513]", "page": "Main Page", "scroll_position": 0, "location": {"x": 67, "y": 1439, "width": 916, "height": 74, "center_x": 525, "center_y": 1476}}, {"text": "", "content_desc": "Ruang GTK", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,1618][383,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 67, "y": 1618, "width": 316, "height": 343, "center_x": 225, "center_y": 1789}}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,1618][713,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 397, "y": 1618, "width": 316, "height": 343, "center_x": 555, "center_y": 1789}}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,1618][1043,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 727, "y": 1618, "width": 316, "height": 343, "center_x": 885, "center_y": 1789}}, {"text": "", "content_desc": "Ruang Bahasa", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,1618][1374,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 1057, "y": 1618, "width": 317, "height": 343, "center_x": 1215, "center_y": 1789}}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,2073][383,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 67, "y": 2073, "width": 316, "height": 343, "center_x": 225, "center_y": 2244}}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,2073][713,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 397, "y": 2073, "width": 316, "height": 343, "center_x": 555, "center_y": 2244}}, {"text": "", "content_desc": "Ruang Publik", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,2073][1043,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 727, "y": 2073, "width": 316, "height": 343, "center_x": 885, "center_y": 2244}}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,2073][1374,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 1057, "y": 2073, "width": 317, "height": 343, "center_x": 1215, "center_y": 2244}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,2685][924,2759]", "page": "Main Page", "scroll_position": 0, "location": {"x": 63, "y": 2685, "width": 861, "height": 74, "center_x": 493, "center_y": 2722}}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2829][1377,2830]", "page": "Main Page", "scroll_position": 0, "location": {"x": 63, "y": 2829, "width": 1314, "height": 1, "center_x": 720, "center_y": 2829}}, {"text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[0,2830][360,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 0, "y": 2830, "width": 360, "height": 206, "center_x": 180, "center_y": 2933}}, {"text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[360,2830][720,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 360, "y": 2830, "width": 360, "height": 206, "center_x": 540, "center_y": 2933}}, {"text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[720,2830][1080,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 720, "y": 2830, "width": 360, "height": 206, "center_x": 900, "center_y": 2933}}, {"text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[1080,2830][1440,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 1080, "y": 2830, "width": 360, "height": 206, "center_x": 1260, "center_y": 2933}}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2220][1377,2560]", "page": "Main Page", "scroll_position": 1, "location": {"x": 63, "y": 2220, "width": 1314, "height": 340, "center_x": 720, "center_y": 2390}}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2616][1377,2830]", "page": "Main Page", "scroll_position": 1, "location": {"x": 63, "y": 2616, "width": 1314, "height": 214, "center_x": 720, "center_y": 2723}}, {"text": "", "content_desc": "Sudah Tahu Informasi Ini?", "clickable": false, "class": "android.view.View", "bounds": "[56,2109][760,2183]", "page": "Main Page", "scroll_position": 2, "location": {"x": 56, "y": 2109, "width": 704, "height": 74, "center_x": 408, "center_y": 2146}}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.view.View", "bounds": "[760,2109][1384,2183]", "page": "Main Page", "scroll_position": 2, "location": {"x": 760, "y": 2109, "width": 624, "height": 74, "center_x": 1072, "center_y": 2146}}, {"text": "", "content_desc": "Laman Direktori Portal SPMB Berbasis SIAP SPMB Online\nI<PERSON> dan <PERSON>, selamat datang di laman direktori resmi portal Sistem Penerimaan Murid Baru (SPMB) untuk daerah-daerah yang menggunakan sistem SIAP SPMB Online.<PERSON>i, <PERSON><PERSON> dapat menemukan tautan lang...\nBaca Selengkapnya", "clickable": true, "class": "android.widget.ImageView", "bounds": "[56,2253][1384,2830]", "page": "Main Page", "scroll_position": 2, "location": {"x": 56, "y": 2253, "width": 1328, "height": 577, "center_x": 720, "center_y": 2541}}, {"text": "", "content_desc": "<PERSON>ar <PERSON><PERSON> (ТKА)\n<PERSON> jawab <PERSON>utar TES KEMAMPUAN AKADEMIK (ТКА) merupakan bentuk asesmen terstandar nasional yang dirancang untuk mengukur capaian akademik murid pada mata pelajaran tertentu sesuai dengan kurikulum ...\nBaca Selengkapnya", "clickable": true, "class": "android.widget.ImageView", "bounds": "[56,1285][1384,2562]", "page": "Main Page", "scroll_position": 3, "location": {"x": 56, "y": 1285, "width": 1328, "height": 1277, "center_x": 720, "center_y": 1923}}, {"text": "", "content_desc": "<PERSON><PERSON> Ban<PERSON>?\n<PERSON><PERSON> pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2814][1377,2830]", "page": "Main Page", "scroll_position": 3, "location": {"x": 63, "y": 2814, "width": 1314, "height": 16, "center_x": 720, "center_y": 2822}}]}, "Menu: Ruang GTK": {"element_count": 24, "elements": [{"text": "", "content_desc": "<PERSON><PERSON> dan <PERSON> (GTK)", "clickable": false, "class": "android.view.View", "bounds": "[119,872][1321,1124]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 119, "y": 872, "width": 1202, "height": 252, "center_x": 720, "center_y": 998}}, {"text": "", "content_desc": "Sumber inspirasi peningkatan kompetensi serta kinerja Guru dan <PERSON> (GTK)", "clickable": false, "class": "android.view.View", "bounds": "[119,1124][1321,1264]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 119, "y": 1124, "width": 1202, "height": 140, "center_x": 720, "center_y": 1194}}, {"text": "", "content_desc": "Belajar Berkelanjutan", "clickable": false, "class": "android.view.View", "bounds": "[63,1383][1377,2965]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 63, "y": 1383, "width": 1314, "height": 1582, "center_x": 720, "center_y": 2174}}, {"text": "", "content_desc": "Diklat\nPelatihan terbimbing menggunakan LMS", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1649][1321,1807]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 119, "y": 1649, "width": 1202, "height": 158, "center_x": 720, "center_y": 1728}}, {"text": "", "content_desc": "Sertifikasi Pendidik\nPendidikan Profesi Guru (PPG) bagi Guru <PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1919][1321,2146]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 119, "y": 1919, "width": 1202, "height": 227, "center_x": 720, "center_y": 2032}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> untuk pengembangan kompetensi guru dan tendik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2258][1321,2486]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 119, "y": 2258, "width": 1202, "height": 228, "center_x": 720, "center_y": 2372}}, {"text": "", "content_desc": "Komunitas\n144.000+ komunitas guru & tendik berbagi praktik baik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2598][1321,2825]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 119, "y": 2598, "width": 1202, "height": 227, "center_x": 720, "center_y": 2711}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,2965][1377,3120]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 0, "location": {"x": 63, "y": 2965, "width": 1314, "height": 155, "center_x": 720, "center_y": 3042}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan pen<PERSON><PERSON> kinerja <PERSON>a untuk pengembangan diri dan satdik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1836][1321,2134]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 1, "location": {"x": 119, "y": 1836, "width": 1202, "height": 298, "center_x": 720, "center_y": 1985}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> Sekolah\nInfo persyaratan, tahapan, & cek kualifikasi untuk seleksi", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2246][1321,2473]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 1, "location": {"x": 119, "y": 2246, "width": 1202, "height": 227, "center_x": 720, "center_y": 2359}}, {"text": "", "content_desc": "Refleksi Kompetensi\nRekomendasi pembelajaran yang disusun personal untuk Anda", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2585][1321,2813]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 1, "location": {"x": 119, "y": 2585, "width": 1202, "height": 228, "center_x": 720, "center_y": 2699}}, {"text": "", "content_desc": "Inspirasi Pembelajaran", "clickable": false, "class": "android.view.View", "bounds": "[63,2953][1377,3120]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 1, "location": {"x": 63, "y": 2953, "width": 1314, "height": 167, "center_x": 720, "center_y": 3036}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>jar\n<PERSON> a<PERSON> & proyek, buku teks, dan seje<PERSON>", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1858][1321,2086]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 2, "location": {"x": 119, "y": 1858, "width": 1202, "height": 228, "center_x": 720, "center_y": 1972}}, {"text": "", "content_desc": "CP/ATP\nCapaia<PERSON> dan <PERSON><PERSON> sesuai mata pelajaran <PERSON>a", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2198][1321,2425]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 2, "location": {"x": 119, "y": 2198, "width": 1202, "height": 227, "center_x": 720, "center_y": 2311}}, {"text": "", "content_desc": "Ide Praktik\nArtikel dan video terpilih tentang praktik pembelajaran terpusat pada murid", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2537][1321,2765]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 2, "location": {"x": 119, "y": 2537, "width": 1202, "height": 228, "center_x": 720, "center_y": 2651}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> jejak karya, <PERSON><PERSON><PERSON>, dan kompet<PERSON>i pendidik", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2877][1321,3104]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 2, "location": {"x": 119, "y": 2877, "width": 1202, "height": 227, "center_x": 720, "center_y": 2990}}, {"text": "", "content_desc": "Video Inspirasi\nKumpulan video terpilih berisi penerapan praktik baik ", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1852][1321,2079]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 3, "location": {"x": 119, "y": 1852, "width": 1202, "height": 227, "center_x": 720, "center_y": 1965}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> (Asesmen Murid & AKM Kelas)\nPaket soal dengan pemeriksaan otomatis dan analisis hasil", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2191][1321,2492]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 3, "location": {"x": 119, "y": 2191, "width": 1202, "height": 301, "center_x": 720, "center_y": 2341}}, {"text": "", "content_desc": "Kelas\nInformasi atau data kelompok murid berdasar<PERSON> hasil asesmen", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2604][1321,2832]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 3, "location": {"x": 119, "y": 2604, "width": 1202, "height": 228, "center_x": 720, "center_y": 2718}}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,2972][1377,3120]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 3, "location": {"x": 63, "y": 2972, "width": 1314, "height": 148, "center_x": 720, "center_y": 3046}}, {"text": "", "content_desc": "Pengelolaan Pembelajaran\nDokumen rujukan untuk Pengelolaan Pembelajaran", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1924][1321,2151]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 4, "location": {"x": 119, "y": 1924, "width": 1202, "height": 227, "center_x": 720, "center_y": 2037}}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2263][1321,2421]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 4, "location": {"x": 119, "y": 2263, "width": 1202, "height": 158, "center_x": 720, "center_y": 2342}}, {"text": "", "content_desc": "Peningkatan Kompetensi\nDokumen rujukan untuk Peningkatan Kompetensi", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2533][1321,2760]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 4, "location": {"x": 119, "y": 2533, "width": 1202, "height": 227, "center_x": 720, "center_y": 2646}}, {"text": "", "content_desc": "Pengelolaan Satuan Pendidikan\nDokumen rujukan untuk Pengelolaan Satuan Pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2872][1321,3100]", "page": "Menu: <PERSON>uang GTK", "scroll_position": 4, "location": {"x": 119, "y": 2872, "width": 1202, "height": 228, "center_x": 720, "center_y": 2986}}]}}, "features": ["Menu location tracking", "Smart scroll to menu position", "Hierarchical navigation", "Null element filtering", "Step-by-step scrolling"], "total_elements": 47}