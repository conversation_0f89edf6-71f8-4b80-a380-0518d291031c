{"timestamp": "20250721_000235", "collection_type": "hierarchical_simplified_with_visited_tracking", "total_pages": 2, "visited_status": {}, "pages": {"Main Page": {"element_count": 23, "visited": false, "visit_timestamp": null, "status": "⏳ PENDING", "elements": [{"text": "", "content_desc": "<PERSON><PERSON><PERSON><PERSON> dalam <PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,487][1211,865]", "page": "Main Page", "scroll_position": 0, "location": {"x": 63, "y": 487, "width": 1148, "height": 378, "center_x": 637, "center_y": 676}, "element_id": "Main Page_element_0", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Temu<PERSON> Pendidikan Anda", "clickable": false, "class": "android.view.View", "bounds": "[67,1439][983,1513]", "page": "Main Page", "scroll_position": 0, "location": {"x": 67, "y": 1439, "width": 916, "height": 74, "center_x": 525, "center_y": 1476}, "element_id": "Main Page_element_1", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Ruang GTK", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,1618][383,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 67, "y": 1618, "width": 316, "height": 343, "center_x": 225, "center_y": 1789}, "element_id": "Main Page_element_2", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,1618][713,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 397, "y": 1618, "width": 316, "height": 343, "center_x": 555, "center_y": 1789}, "element_id": "Main Page_element_3", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,1618][1043,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 727, "y": 1618, "width": 316, "height": 343, "center_x": 885, "center_y": 1789}, "element_id": "Main Page_element_4", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Ruang Bahasa", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,1618][1374,1961]", "page": "Main Page", "scroll_position": 0, "location": {"x": 1057, "y": 1618, "width": 317, "height": 343, "center_x": 1215, "center_y": 1789}, "element_id": "Main Page_element_5", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[67,2073][383,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 67, "y": 2073, "width": 316, "height": 343, "center_x": 225, "center_y": 2244}, "element_id": "Main Page_element_6", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[397,2073][713,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 397, "y": 2073, "width": 316, "height": 343, "center_x": 555, "center_y": 2244}, "element_id": "Main Page_element_7", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Ruang Publik", "clickable": true, "class": "android.widget.ImageView", "bounds": "[727,2073][1043,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 727, "y": 2073, "width": 316, "height": 343, "center_x": 885, "center_y": 2244}, "element_id": "Main Page_element_8", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.widget.ImageView", "bounds": "[1057,2073][1374,2416]", "page": "Main Page", "scroll_position": 0, "location": {"x": 1057, "y": 2073, "width": 317, "height": 343, "center_x": 1215, "center_y": 2244}, "element_id": "Main Page_element_9", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON> Banyak Diaks<PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,2685][924,2759]", "page": "Main Page", "scroll_position": 0, "location": {"x": 63, "y": 2685, "width": 861, "height": 74, "center_x": 493, "center_y": 2722}, "element_id": "Main Page_element_10", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Sumber Belajar\nPortal pembelajaran digital interaktif untuk semua jenjang", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2829][1377,2830]", "page": "Main Page", "scroll_position": 0, "location": {"x": 63, "y": 2829, "width": 1314, "height": 1, "center_x": 720, "center_y": 2829}, "element_id": "Main Page_element_11", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Beranda\nBeranda\nTab 1 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[0,2830][360,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 0, "y": 2830, "width": 360, "height": 206, "center_x": 180, "center_y": 2933}, "element_id": "Main Page_element_12", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON>\nR<PERSON> 2 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[360,2830][720,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 360, "y": 2830, "width": 360, "height": 206, "center_x": 540, "center_y": 2933}, "element_id": "Main Page_element_13", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Pemberitahuan\nPemberitahuan\nTab 3 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[720,2830][1080,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 720, "y": 2830, "width": 360, "height": 206, "center_x": 900, "center_y": 2933}, "element_id": "Main Page_element_14", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Akun\n<PERSON> 4 of 4", "clickable": true, "class": "android.widget.Button", "bounds": "[1080,2830][1440,3036]", "page": "Main Page", "scroll_position": 0, "location": {"x": 1080, "y": 2830, "width": 360, "height": 206, "center_x": 1260, "center_y": 2933}, "element_id": "Main Page_element_15", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "P<PERSON>t <PERSON>\nPortal buku pendidikan resmi untuk siswa, guru, dan ma<PERSON><PERSON>kat", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2215][1377,2554]", "page": "Main Page", "scroll_position": 1, "location": {"x": 63, "y": 2215, "width": 1314, "height": 339, "center_x": 720, "center_y": 2384}, "element_id": "Main Page_element_16", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Pengelolaan Kinerja\n⁠Dokumen rujukan untuk Pengelolaan Kinerja", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2610][1377,2830]", "page": "Main Page", "scroll_position": 1, "location": {"x": 63, "y": 2610, "width": 1314, "height": 220, "center_x": 720, "center_y": 2720}, "element_id": "Main Page_element_17", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Sudah Tahu Informasi Ini?", "clickable": false, "class": "android.view.View", "bounds": "[56,2110][760,2184]", "page": "Main Page", "scroll_position": 2, "location": {"x": 56, "y": 2110, "width": 704, "height": 74, "center_x": 408, "center_y": 2147}, "element_id": "Main Page_element_18", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON>", "clickable": true, "class": "android.view.View", "bounds": "[760,2110][1384,2184]", "page": "Main Page", "scroll_position": 2, "location": {"x": 760, "y": 2110, "width": 624, "height": 74, "center_x": 1072, "center_y": 2147}, "element_id": "Main Page_element_19", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Laman Direktori Portal SPMB Berbasis SIAP SPMB Online\nI<PERSON> dan <PERSON>, selamat datang di laman direktori resmi portal Sistem Penerimaan Murid Baru (SPMB) untuk daerah-daerah yang menggunakan sistem SIAP SPMB Online.<PERSON>i, <PERSON><PERSON> dapat menemukan tautan lang...\nBaca Selengkapnya", "clickable": true, "class": "android.widget.ImageView", "bounds": "[56,2254][1384,2830]", "page": "Main Page", "scroll_position": 2, "location": {"x": 56, "y": 2254, "width": 1328, "height": 576, "center_x": 720, "center_y": 2542}, "element_id": "Main Page_element_20", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON>ar <PERSON><PERSON> (ТKА)\n<PERSON> jawab <PERSON>utar TES KEMAMPUAN AKADEMIK (ТКА) merupakan bentuk asesmen terstandar nasional yang dirancang untuk mengukur capaian akademik murid pada mata pelajaran tertentu sesuai dengan kurikulum ...\nBaca Selengkapnya", "clickable": true, "class": "android.widget.ImageView", "bounds": "[56,1284][1384,2562]", "page": "Main Page", "scroll_position": 3, "location": {"x": 56, "y": 1284, "width": 1328, "height": 1278, "center_x": 720, "center_y": 1923}, "element_id": "Main Page_element_21", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON> Ban<PERSON>?\n<PERSON><PERSON> pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan", "clickable": true, "class": "android.widget.Button", "bounds": "[63,2814][1377,2830]", "page": "Main Page", "scroll_position": 3, "location": {"x": 63, "y": 2814, "width": 1314, "height": 16, "center_x": 720, "center_y": 2822}, "element_id": "Main Page_element_22", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}]}, "Menu: Ruang Sekolah": {"element_count": 9, "visited": false, "visit_timestamp": null, "status": "⏳ PENDING", "elements": [{"text": "", "content_desc": "<PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,872][734,998]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 119, "y": 872, "width": 615, "height": 126, "center_x": 426, "center_y": 935}, "element_id": "Menu: <PERSON><PERSON>_element_0", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Data terpusat untuk akses pen<PERSON>an kebutuhan <PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[119,998][1321,1138]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 119, "y": 998, "width": 1202, "height": 140, "center_x": 720, "center_y": 1068}, "element_id": "Menu: <PERSON><PERSON>_element_1", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON><PERSON>", "clickable": false, "class": "android.view.View", "bounds": "[63,1257][1377,3120]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 63, "y": 1257, "width": 1314, "height": 1863, "center_x": 720, "center_y": 2188}, "element_id": "Menu: <PERSON><PERSON>_element_2", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Profil Sekolah\nPortal informasi profil dan data seluruh sekolah di Indonesia", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1523][1321,1751]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 119, "y": 1523, "width": 1202, "height": 228, "center_x": 720, "center_y": 1637}, "element_id": "Menu: <PERSON><PERSON>_element_3", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "<PERSON><PERSON> Pendidikan\nSistem evaluasi dan pemetaan mutu satuan pendidikan", "clickable": true, "class": "android.widget.Button", "bounds": "[119,1863][1321,2090]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 119, "y": 1863, "width": 1202, "height": 227, "center_x": 720, "center_y": 1976}, "element_id": "Menu: <PERSON><PERSON>_element_4", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Rencana Kegiatan dan Belanja Sekolah\nSistem pengelolaan rencana kegiatan dan belanja sekolah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2202][1321,2430]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 119, "y": 2202, "width": 1202, "height": 228, "center_x": 720, "center_y": 2316}, "element_id": "Menu: <PERSON><PERSON>_element_5", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Bantuan Operasional\nSistem pengelolaan dan pelaporan dana bantuan operasional sekolah", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2542][1321,2769]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 119, "y": 2542, "width": 1202, "height": 227, "center_x": 720, "center_y": 2655}, "element_id": "Menu: <PERSON><PERSON>_element_6", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Akun Pendidikan\nManajemen akun terpadu layanan pendidikan digital", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2881][1321,3109]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 0, "location": {"x": 119, "y": 2881, "width": 1202, "height": 228, "center_x": 720, "center_y": 2995}, "element_id": "Menu: <PERSON><PERSON>_element_7", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}, {"text": "", "content_desc": "Pengadaan Barang dan Jasa Sekolah\nSistem digital yang membantu sekolah berbelanja di Mitra pengelola pasar daring SIPLah.", "clickable": true, "class": "android.widget.Button", "bounds": "[119,2333][1321,2630]", "page": "Menu: <PERSON><PERSON>", "scroll_position": 1, "location": {"x": 119, "y": 2333, "width": 1202, "height": 297, "center_x": 720, "center_y": 2481}, "element_id": "Menu: <PERSON><PERSON>_element_8", "visited": false, "visit_timestamp": null, "status": "⏳ PENDING"}]}}, "features": ["Menu location tracking", "Smart scroll to menu position", "Hierarchical navigation", "Null element filtering", "Step-by-step scrolling", "Visited status tracking"], "total_elements": 32, "visited_summary": {"total_pages": 2, "visited_pages": 0, "pending_pages": 2, "completion_percentage": 0.0, "status_overview": "0/2 pages completed"}}