#!/usr/bin/env python3

"""
Comprehensive Logging System for Android Automation Framework
Captures all terminal output and saves to timestamped files
"""

import os
import sys
import datetime
import threading
import subprocess
from pathlib import Path
from typing import Optional, TextIO

class TerminalLogger:
    """Captures and logs all terminal output to timestamped files"""
    
    def __init__(self, script_name: str, log_base_dir: str = "execution_logs"):
        self.script_name = script_name
        self.log_base_dir = Path(log_base_dir)
        self.start_time = datetime.datetime.now()
        self.log_file_path = self._create_log_file_path()
        self.log_file: Optional[TextIO] = None
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        
        # Create log directory
        self.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
    def _create_log_file_path(self) -> Path:
        """Create timestamped log file path"""
        timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")
        filename = f"{self.script_name}_{timestamp}.txt"
        
        # Create folder structure: execution_logs/YYYY/MM/DD/
        date_folder = self.log_base_dir / self.start_time.strftime("%Y") / self.start_time.strftime("%m") / self.start_time.strftime("%d")
        return date_folder / filename
        
    def start_logging(self):
        """Start capturing terminal output"""
        try:
            self.log_file = open(self.log_file_path, 'w', encoding='utf-8', buffering=1)
            
            # Write header
            self._write_header()
            
            # Redirect stdout and stderr
            sys.stdout = TeeOutput(self.original_stdout, self.log_file)
            sys.stderr = TeeOutput(self.original_stderr, self.log_file)
            
            print(f"📝 Logging started: {self.log_file_path}")
            print(f"🕐 Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)
            
        except Exception as e:
            print(f"❌ Failed to start logging: {e}")
            
    def stop_logging(self):
        """Stop capturing terminal output and close log file"""
        try:
            if self.log_file:
                end_time = datetime.datetime.now()
                duration = end_time - self.start_time
                
                print("=" * 80)
                print(f"🕐 End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"⏱️  Total duration: {duration}")
                print(f"📝 Log saved to: {self.log_file_path}")
                
                # Write footer
                self._write_footer(end_time, duration)
                
                # Restore original stdout/stderr
                sys.stdout = self.original_stdout
                sys.stderr = self.original_stderr
                
                self.log_file.close()
                self.log_file = None
                
        except Exception as e:
            print(f"❌ Failed to stop logging: {e}")
            
    def _write_header(self):
        """Write log file header"""
        header = f"""
{'=' * 80}
ANDROID AUTOMATION FRAMEWORK - EXECUTION LOG
{'=' * 80}
Script: {self.script_name}
Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
Log File: {self.log_file_path}
Python Version: {sys.version}
Working Directory: {os.getcwd()}
{'=' * 80}

"""
        self.log_file.write(header)
        self.log_file.flush()
        
    def _write_footer(self, end_time: datetime.datetime, duration: datetime.timedelta):
        """Write log file footer"""
        footer = f"""

{'=' * 80}
EXECUTION COMPLETED
{'=' * 80}
End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
Total Duration: {duration}
Exit Status: {'SUCCESS' if sys.exc_info()[0] is None else 'ERROR'}
{'=' * 80}
"""
        self.log_file.write(footer)
        self.log_file.flush()

class TeeOutput:
    """Tee output to both original stream and log file"""
    
    def __init__(self, original_stream: TextIO, log_file: TextIO):
        self.original_stream = original_stream
        self.log_file = log_file
        
    def write(self, text: str):
        """Write to both streams"""
        self.original_stream.write(text)
        self.log_file.write(text)
        self.original_stream.flush()
        self.log_file.flush()
        
    def flush(self):
        """Flush both streams"""
        self.original_stream.flush()
        self.log_file.flush()
        
    def __getattr__(self, name):
        """Delegate other attributes to original stream"""
        return getattr(self.original_stream, name)

class ProcessLogger:
    """Logger for subprocess execution with real-time output capture"""
    
    def __init__(self, script_name: str, log_base_dir: str = "execution_logs"):
        self.script_name = script_name
        self.log_base_dir = Path(log_base_dir)
        self.start_time = datetime.datetime.now()
        self.log_file_path = self._create_log_file_path()
        
        # Create log directory
        self.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
    def _create_log_file_path(self) -> Path:
        """Create timestamped log file path"""
        timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")
        filename = f"{self.script_name}_{timestamp}.txt"
        
        # Create folder structure: execution_logs/YYYY/MM/DD/
        date_folder = self.log_base_dir / self.start_time.strftime("%Y") / self.start_time.strftime("%m") / self.start_time.strftime("%d")
        return date_folder / filename
        
    def run_with_logging(self, command: list, cwd: str = None) -> int:
        """Run command and log all output"""
        try:
            with open(self.log_file_path, 'w', encoding='utf-8', buffering=1) as log_file:
                # Write header
                self._write_header(log_file, command)
                
                print(f"📝 Logging execution to: {self.log_file_path}")
                print(f"🚀 Running: {' '.join(command)}")
                print("=" * 80)
                
                # Start process
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1,
                    cwd=cwd
                )
                
                # Read and log output in real-time
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        print(output.strip())
                        log_file.write(output)
                        log_file.flush()
                
                # Wait for process to complete
                return_code = process.poll()
                
                end_time = datetime.datetime.now()
                duration = end_time - self.start_time
                
                print("=" * 80)
                print(f"🕐 End time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"⏱️  Total duration: {duration}")
                print(f"🔢 Exit code: {return_code}")
                print(f"📝 Log saved to: {self.log_file_path}")
                
                # Write footer
                self._write_footer(log_file, end_time, duration, return_code)
                
                return return_code
                
        except Exception as e:
            print(f"❌ Failed to run with logging: {e}")
            return 1
            
    def _write_header(self, log_file: TextIO, command: list):
        """Write log file header"""
        header = f"""
{'=' * 80}
ANDROID AUTOMATION FRAMEWORK - PROCESS EXECUTION LOG
{'=' * 80}
Script: {self.script_name}
Command: {' '.join(command)}
Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
Log File: {self.log_file_path}
Python Version: {sys.version}
Working Directory: {os.getcwd()}
{'=' * 80}

"""
        log_file.write(header)
        log_file.flush()
        
    def _write_footer(self, log_file: TextIO, end_time: datetime.datetime, duration: datetime.timedelta, return_code: int):
        """Write log file footer"""
        footer = f"""

{'=' * 80}
PROCESS EXECUTION COMPLETED
{'=' * 80}
End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
Total Duration: {duration}
Exit Code: {return_code}
Status: {'SUCCESS' if return_code == 0 else 'ERROR'}
{'=' * 80}
"""
        log_file.write(footer)
        log_file.flush()

def create_log_summary():
    """Create a summary of all log files"""
    log_base_dir = Path("execution_logs")
    if not log_base_dir.exists():
        return
        
    summary_file = log_base_dir / "execution_summary.txt"
    
    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("ANDROID AUTOMATION FRAMEWORK - EXECUTION SUMMARY\n")
            f.write("=" * 60 + "\n\n")
            
            # Find all log files
            log_files = list(log_base_dir.rglob("*.txt"))
            log_files = [f for f in log_files if f.name != "execution_summary.txt"]
            log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            f.write(f"Total Executions: {len(log_files)}\n")
            f.write(f"Summary Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for log_file in log_files:
                stat = log_file.stat()
                size_mb = stat.st_size / (1024 * 1024)
                modified = datetime.datetime.fromtimestamp(stat.st_mtime)
                
                f.write(f"File: {log_file.relative_to(log_base_dir)}\n")
                f.write(f"  Size: {size_mb:.2f} MB\n")
                f.write(f"  Modified: {modified.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"  Path: {log_file}\n\n")
                
        print(f"📊 Log summary created: {summary_file}")
        
    except Exception as e:
        print(f"❌ Failed to create log summary: {e}")
