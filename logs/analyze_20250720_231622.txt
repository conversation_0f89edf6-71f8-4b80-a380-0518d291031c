📝 Logging enabled - saving to: logs/analyze_20250720_231622.txt
🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT
Based on analyze_proper_flow.py pattern
Implements your exact hierarchical navigation flow
Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown 4) Server crashes
======================================================================
[PROGRESS] 📂 Loading existing progress file: automation_progress_20250720_230107.json
[PROGRESS] ✅ Loaded progress with 9 menus

📱 Starting Appium session...
[SIMPLE] Starting Appium session for: com.kemendikdasmen.rumahpendidikan
[SIMPLE] Connecting to Appium server...
[SIMPLE] Activating app...
✅ Simple Appium session started and app activated!
⏳ Waiting for app to load...
[WAIT] Waiting for page to load (max 15s)...
[WAIT] ✅ Page loaded with 16 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
🎯 Starting hierarchical crawl...

================================================================================
🎯 STARTING HIERARCHICAL CRAWL WITH PROGRESS TRACKING - YOUR EXACT FLOW
================================================================================
Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu
Real-time JSON sync for visited status tracking
================================================================================
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected

📋 STEP 1: Collecting all elements on MAIN PAGE

[COLLECT] 📋 Starting element collection for: Main Page
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[COLLECT]     1. ⚪ 'Jelajahi Beragam Layanan Pendidikan dalam Genggama'
[COLLECT]     2. ⚪ 'Temukan Ruang Pendidikan Anda'
[LOCATION] 📍 Menu 'Ruang GTK' found at y=1618, center=(225, 1789)
[COLLECT]     3. 🔘 'Ruang GTK'
[LOCATION] 📍 Menu 'Ruang Murid' found at y=1618, center=(555, 1789)
[COLLECT]     4. 🔘 'Ruang Murid'
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=1618, center=(885, 1789)
[COLLECT]     5. 🔘 'Ruang Sekolah'
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=1618, center=(1215, 1789)
[COLLECT]     6. 🔘 'Ruang Bahasa'
[LOCATION] 📍 Menu 'Ruang Pemerintah' found at y=2073, center=(225, 2244)
[COLLECT]     7. 🔘 'Ruang Pemerintah'
[LOCATION] 📍 Menu 'Ruang Mitra' found at y=2073, center=(555, 2244)
[COLLECT]     8. 🔘 'Ruang Mitra'
[LOCATION] 📍 Menu 'Ruang Publik' found at y=2073, center=(885, 2244)
[COLLECT]     9. 🔘 'Ruang Publik'
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=2073, center=(1215, 2244)
[COLLECT]    10. 🔘 'Ruang Orang Tua'
[COLLECT]    11. ⚪ 'Layanan Paling Banyak Diakses'
[LOCATION] 📍 Menu 'Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang' found at y=2829, center=(720, 2829)
[COLLECT]    12. 🔘 'Sumber Belajar
Portal pembelajaran digital interak'
[COLLECT]    13. 🔘 'Beranda
Beranda
Tab 1 of 4'
[COLLECT]    14. 🔘 'Ruang
Ruang
Tab 2 of 4'
[COLLECT]    15. 🔘 'Pemberitahuan
Pemberitahuan
Tab 3 of 4'
[COLLECT]    16. 🔘 'Akun
Akun
Tab 4 of 4'
[COLLECT] Found 16 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[LOCATION] 📍 Menu 'Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat' found at y=2244, center=(720, 2413)
[COLLECT]    17. 🔘 'Pusat Perbukuan
Portal buku pendidikan resmi untuk'
[LOCATION] 📍 Menu 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelolaan Kinerja' found at y=2639, center=(720, 2734)
[COLLECT]    18. 🔘 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelo'
[COLLECT] Found 2 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT]    19. ⚪ 'Sudah Tahu Informasi Ini?'
[COLLECT]    20. 🔘 'Lihat Semua'
[COLLECT]    21. 🔘 'Laman Direktori Portal SPMB Berbasis SIAP SPMB Onl'
[COLLECT] Found 3 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[COLLECT]    22. 🔘 'Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
T'
[COLLECT] Found 1 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT]    23. 🔘 'Butuh Bantuan?
Lihat pertanyaan yang paling sering'
[COLLECT] Found 1 new elements at scroll position 4
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 5/5)...
[COLLECT] Found 0 new elements at scroll position 5
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Main Page: 23 total elements
[LOCATION] 📍 Found 11 menu locations
[PROGRESS] 💾 Progress saved: 2/9 menus, 0/47 elements
[PROGRESS] ✅ Updated menu 'Main Page' - Visited: True

🎯 STEP 2: Found 8 main menus to crawl

✅ MENU Ruang GTK already visited in JSON - skipping
[PROGRESS] 📅 Visit timestamp: 20250720_230551
[PROGRESS] 📊 Elements collected: 24

============================================================
🎯 PROCESSING MENU: Ruang Murid
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 2/9 menus, 0/47 elements
📋 Remaining unvisited: ['Ruang Murid', 'Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Murid
[CLICK] 📍 Using known location for Ruang Murid
[CLICK] Menu found at scroll position 0, y=1618
[CLICK] 🔄 Smart scrolling to top for position 0...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ✅ At top - found 'jelajahi beragam layanan pendidikan dalam genggaman' at y=144
[SMART_TOP] ✅ Reached top after 3 attempts
[CLICK] Trying pattern 1: //*[@text='Ruang Murid' or @content-desc='Ruang Murid']...
[CLICK] Found element, attempting click...
[CLICK] ✅ Successfully clicked Ruang Murid (regular click)
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 8 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
✅ Successfully navigated to Ruang Murid
[PROGRESS] 💾 Progress saved: 3/9 menus, 0/47 elements
[PROGRESS] ✅ Updated menu 'Ruang Murid' - Visited: True
[PROGRESS] ✅ IMMEDIATELY marked 'Ruang Murid' as VISITED upon navigation

[COLLECT] 📋 Starting element collection for: Menu: Ruang Murid
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[LOCATION] 📍 Menu 'Ruang Murid' found at y=872, center=(381, 935)
[COLLECT]     1. ⚪ 'Ruang Murid'
[LOCATION] 📍 Menu 'Sumber belajar yang beragam dan inspiratif untuk Murid Indonesia' found at y=998, center=(720, 1068)
[COLLECT]     2. ⚪ 'Sumber belajar yang beragam dan inspiratif untuk M'
[COLLECT]     3. ⚪ 'Layanan Pendidikan'
[LOCATION] 📍 Menu 'Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang' found at y=1523, center=(720, 1637)
[COLLECT]     4. 🔘 'Sumber Belajar
Portal pembelajaran digital interak'
[COLLECT]     5. 🔘 'Buku Bacaan Digital
Pusat Buku Digital untuk Gerak'
[COLLECT]     6. 🔘 'Album Lagu Anak
Album Lirik Lagu Tujuh Kebiasaan A'
[COLLECT]     7. 🔘 'Bank Soal
Kumpulan soal latihan berbagai mata pela'
[COLLECT]     8. 🔘 'Rapor Digital
Laporan penilaian dan evaluasi hasil'
[COLLECT] Found 8 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[COLLECT]     9. 🔘 'Riwayat Pendidikan
Catatan perjalanan pendidikan s'
[COLLECT]    10. 🔘 'Akun Pendidikan
Manajemen akun terpadu layanan pen'
[LOCATION] 📍 Menu 'Sumber Buku Teks Pembelajaran
Jelajahi buku teks & non-teks resmi dari Pusat Perbukuan Kemendikdasmen' found at y=2538, center=(720, 2651)
[COLLECT]    11. 🔘 'Sumber Buku Teks Pembelajaran
Jelajahi buku teks &'
[COLLECT]    12. 🔘 'Pendidikan Jarak Jauh
Akses pembelajaran jenjang p'
[COLLECT] Found 4 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT] Found 0 new elements at scroll position 2
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Menu: Ruang Murid: 12 total elements
[LOCATION] 📍 Found 4 menu locations
[PROGRESS] 💾 Progress saved: 3/9 menus, 0/59 elements
[PROGRESS] ✅ Updated menu 'Ruang Murid' - Visited: True
[PROGRESS] ✅ Updated 'Ruang Murid' with 12 collected elements

🔍 Starting submenu crawl for Ruang Murid...

[SUBMENU_CRAWL] 📂 Starting submenu crawl for Ruang Murid at depth 1
[SUBMENU_CRAWL] ✅ Already collected elements for Ruang Murid - skipping collection
[SUBMENU_CRAWL] 🔄 Ensuring at top before processing submenus...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 4/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 5/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] ⚠️ Could not reach top after 5 attempts

[SUBMENU] 🔍 Finding submenu items...
[SUBMENU] Found 5 potential submenu items
[SUBMENU_CRAWL] 📍 Using location data for 4 potential submenus
[SUBMENU_CRAWL]   - Ruang Murid: y=872
[SUBMENU_CRAWL]   - Sumber belajar yang beragam dan inspiratif untuk Murid Indonesia: y=998
[SUBMENU_CRAWL]   - Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang: y=1523
[SUBMENU_CRAWL]   - Sumber Buku Teks Pembelajaran
Jelajahi buku teks & non-teks resmi dari Pusat Perbukuan Kemendikdasmen: y=2538
[SUBMENU_CRAWL] Found 5 potential submenus in Ruang Murid

[SUBMENU_CRAWL] Clicking submenu 1/5: Submenu_1
[CLICK_SUBMENU] Attempting to click: Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
[CLICK_SUBMENU] ✅ Successfully clicked submenu: Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang
[WAIT] Waiting for page to load (max 5s)...
[WAIT] ✅ Page loaded with 20 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
[SUBMENU_CRAWL] ✅ Marked Submenu_1 as visited
[SUBMENU_CRAWL] 📂 Navigated to different page - doing sub-crawl...

[SUBMENU_CRAWL] 📂 Starting submenu crawl for Ruang Murid > Submenu_1 at depth 2
[SUBMENU_CRAWL] 📋 First time in Ruang Murid > Submenu_1 - collecting elements...

[COLLECT] 📋 Starting element collection for: Ruang Murid > Submenu_1_SUBMENU_COLLECTION
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[LOCATION] 📍 Menu 'Sumber Belajar' found at y=195, center=(516, 242)
[COLLECT]     1. ⚪ 'Sumber Belajar'
[LOCATION] 📍 Menu 'Pilih Kelas | Ruang Murid' found at y=340, center=(720, 1730)
[COLLECT]     2. ⚪ 'Pilih Kelas | Ruang Murid'
[COLLECT]     3. ⚪ 'Rumah Pendidikan Icon'
[COLLECT]     4. ⚪ 'Bagian dari'
[COLLECT]     5. 🔘 'Rumah Pendidikan'
[COLLECT]     6. ⚪ 'Rumah Pendidikan'
[COLLECT]     7. ⚪ 'Selamat datang di'
[LOCATION] 📍 Menu 'Ruang Murid Logo' found at y=788, center=(477, 861)
[COLLECT]     8. ⚪ 'Ruang Murid Logo'
[LOCATION] 📍 Menu 'Sumber Belajar' found at y=1012, center=(722, 1099)
[COLLECT]     9. ⚪ 'Sumber Belajar'
[COLLECT]    10. 🔘 'Apa yang akan kamu dapatkan di sini?'
[COLLECT]    11. ⚪ 'Kamu kelas berapa?'
[COLLECT]    12. 🔘 'PAUD'
[COLLECT]    13. 🔘 '3-4 tahun'
[COLLECT]    14. 🔘 '4-5 tahun'
[COLLECT]    15. 🔘 '5-6 tahun'
[COLLECT]    16. 🔘 'SD'
[COLLECT] Found 16 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[COLLECT]    17. 🔘 'SMP'
[COLLECT]    18. 🔘 'SMA & SMK (Materi Umum) Materi untuk mata pelajara'
[COLLECT]    19. 🔘 'SMK (Materi Kejuruan) Materi untuk bidang kejuruan'
[COLLECT] Found 3 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT]    20. 🔘 'Mulai Eksplorasi'
[COLLECT]    21. ⚪ 'Ketersediaan materi masih terbatas dan sedang dala'
[COLLECT] Found 2 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[COLLECT]    22. 🔘 'Bantuan'
[COLLECT]    23. ⚪ 'Bantuan'
[COLLECT] Found 2 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT] Found 0 new elements at scroll position 4
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Ruang Murid > Submenu_1_SUBMENU_COLLECTION: 23 total elements
[LOCATION] 📍 Found 3 menu locations
[SUBMENU_CRAWL] ✅ Collected 23 elements from Ruang Murid > Submenu_1
[SUBMENU_CRAWL] 🔄 Ensuring at top before processing submenus...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 4/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 5/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] ⚠️ Could not reach top after 5 attempts

[SUBMENU] 🔍 Finding submenu items...
[SUBMENU] Found 1 potential submenu items
[SUBMENU_CRAWL] 📍 Using location data for 3 potential submenus
[SUBMENU_CRAWL]   - Sumber Belajar: y=1012
[SUBMENU_CRAWL]   - Pilih Kelas | Ruang Murid: y=340
[SUBMENU_CRAWL]   - Ruang Murid Logo: y=788
[SUBMENU_CRAWL] Found 1 potential submenus in Ruang Murid > Submenu_1

[SUBMENU_CRAWL] Clicking submenu 1/1: Submenu_1
[CLICK_SUBMENU] Attempting to click: Rumah Pendidikan
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
[CLICK_SUBMENU] ✅ Successfully clicked submenu: Rumah Pendidikan
[WAIT] Waiting for page to load (max 5s)...
[WAIT] ✅ Page loaded with 35 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
[SUBMENU_CRAWL] ✅ Marked Submenu_1 as visited
[SUBMENU_CRAWL] 📂 Navigated to different page - doing sub-crawl...

[SUBMENU_CRAWL] 📂 Starting submenu crawl for Ruang Murid > Submenu_1 > Submenu_1 at depth 3
[SUBMENU_CRAWL] 📋 At sub-submenu level (depth 3) - COLLECT ONLY, NO CLICKING

[COLLECT] 📋 Starting element collection for: Ruang Murid > Submenu_1 > Submenu_1_FINAL_COLLECTION
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[LOCATION] 📍 Menu 'Sumber Belajar' found at y=195, center=(516, 242)
[COLLECT]     1. ⚪ 'Sumber Belajar'
[COLLECT]     2. ⚪ 'Rumah Pendidikan: Ruang Kolaborasi untuk Pendidika'
[COLLECT]     3. 🔘 'Langsung ke konten utama'
[COLLECT]     4. ⚪ 'Langsung ke konten utama'
[COLLECT]     5. ⚪ 'Site Header'
[COLLECT]     6. ⚪ 'Primary Navigation'
[COLLECT]     7. 🔘 'Rumah Pendidikan Home'
[COLLECT]     8. ⚪ 'Rumah Pendidikan'
[COLLECT]     9. ⚪ 'Pencarian layanan pendidikan'
[COLLECT]    10. 🔘 'Cari'
[COLLECT]    11. ⚪ 'Main Content'
[COLLECT]    12. 🔘 'Tumbuh Bersama, Jadi Inspirasi untuk Sesama'
[COLLECT]    13. ⚪ 'Tumbuh Bersama, Jadi Inspirasi untuk Sesama'
[COLLECT]    14. ⚪ 'Jelajahi Ruang di Rumah Pendidikan'
[COLLECT]    15. ⚪ 'Daftar ruang di Rumah Pendidikan'
[LOCATION] 📍 Menu 'Ruang GTK' found at y=1649, center=(213, 1762)
[COLLECT]    16. 🔘 'Ruang GTK'
[LOCATION] 📍 Menu 'Ruang GTK' found at y=1810, center=(211, 1838)
[COLLECT]    17. ⚪ 'Ruang GTK'
[LOCATION] 📍 Menu 'Ruang Murid' found at y=1649, center=(551, 1762)
[COLLECT]    18. 🔘 'Ruang Murid'
[LOCATION] 📍 Menu 'Ruang Murid' found at y=1810, center=(551, 1838)
[COLLECT]    19. ⚪ 'Ruang Murid'
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=1649, center=(892, 1797)
[COLLECT]    20. 🔘 'Ruang Sekolah'
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=1810, center=(890, 1873)
[COLLECT]    21. ⚪ 'Ruang Sekolah'
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=1649, center=(1230, 1797)
[COLLECT]    22. 🔘 'Ruang Bahasa'
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=1810, center=(1230, 1873)
[COLLECT]    23. ⚪ 'Ruang Bahasa'
[LOCATION] 📍 Menu 'Ruang Pemerintah' found at y=1999, center=(213, 2146)
[COLLECT]    24. 🔘 'Ruang Pemerintah'
[LOCATION] 📍 Menu 'Ruang Pemerintah' found at y=2160, center=(211, 2223)
[COLLECT]    25. ⚪ 'Ruang Pemerintah'
[LOCATION] 📍 Menu 'Ruang Mitra' found at y=1999, center=(551, 2111)
[COLLECT]    26. 🔘 'Ruang Mitra'
[LOCATION] 📍 Menu 'Ruang Mitra' found at y=2160, center=(551, 2188)
[COLLECT]    27. ⚪ 'Ruang Mitra'
[LOCATION] 📍 Menu 'Ruang Publik' found at y=1999, center=(892, 2111)
[COLLECT]    28. 🔘 'Ruang Publik'
[LOCATION] 📍 Menu 'Ruang Publik' found at y=2160, center=(890, 2188)
[COLLECT]    29. ⚪ 'Ruang Publik'
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=1999, center=(1230, 2146)
[COLLECT]    30. 🔘 'Ruang Orang Tua'
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=2160, center=(1230, 2223)
[COLLECT]    31. ⚪ 'Ruang Orang Tua'
[COLLECT]    32. ⚪ 'Semangat Rumah Pendidikan'
[COLLECT] Found 32 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[COLLECT]    33. ⚪ 'Rumah Pendidikan berkomitmen untuk menjadi pusat r'
[COLLECT]    34. 🔘 'Pelajari Selengkapnya'
[COLLECT]    35. 🔘 'Pelajari Selengkapnya'
[COLLECT]    36. ⚪ 'Layanan Unggulan di Rumah Pendidikan'
[COLLECT] Found 4 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[LOCATION] 📍 Menu 'Sumber Belajar Portal pembelajaran digital interaktif untuk semua jenjang.' found at y=2359, center=(721, 2739)
[COLLECT]    37. 🔘 'Sumber Belajar Portal pembelajaran digital interak'
[COLLECT]    38. 🔘 'Buku Bacaan Digital Pusat Buku Digital untuk Gerak'
[COLLECT]    39. 🔘 'Pelatihan Mandiri Materi untuk pengembangan kompet'
[COLLECT] Found 3 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[LOCATION] 📍 Menu 'Sumber Belajar' found at y=2240, center=(815, 2291)
[COLLECT]    40. ⚪ 'Sumber Belajar'
[COLLECT]    41. ⚪ 'Portal pembelajaran digital interaktif untuk semua'
[COLLECT]    42. 🔘 'Ke slide 1'
[COLLECT]    43. 🔘 'Ke slide 2'
[COLLECT]    44. 🔘 'Ke slide 3'
[COLLECT]    45. 🔘 'Ke slide 4'
[COLLECT]    46. 🔘 'Ke slide 5'
[COLLECT] Found 7 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT]    47. ⚪ 'Siapa saja yang sudah memanfaatkan kemudahan kolab'
[COLLECT]    48. ⚪ 'Simak perjalanan mereka'
[COLLECT] Found 2 new elements at scroll position 4
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 5/5)...
[COLLECT]    49. ⚪ '"Rumah Pendidikan merupakan gagasan yang sangat me'
[COLLECT] Found 1 new elements at scroll position 5
[COLLECT] ✅ Collection complete for Ruang Murid > Submenu_1 > Submenu_1_FINAL_COLLECTION: 49 total elements
[LOCATION] 📍 Found 10 menu locations
[SUBMENU_CRAWL] ✅ Final collection: 49 elements from Ruang Murid > Submenu_1 > Submenu_1

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[WAIT] Waiting for page to load (max 5s)...
[WAIT] ✅ Page loaded with 8 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[SUBMENU_CRAWL] 🔄 Back action completed - preparing to continue to next menu...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 4/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 5/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] ⚠️ Could not reach top after 5 attempts
[SUBMENU_CRAWL] 📜 Scrolled to ensure next menus are visible
[SUBMENU_CRAWL] ✅ Completed submenu crawl for Ruang Murid > Submenu_1

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[WAIT] Waiting for page to load (max 5s)...
[WAIT] ✅ Page loaded with 17 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[SUBMENU_CRAWL] 🔄 Back action completed - preparing to continue to next menu...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ✅ At top - found 'jelajahi beragam layanan pendidikan dalam genggaman' at y=144
[SMART_TOP] ✅ Already at top - no scroll needed (prevents pull-to-refresh)
[SUBMENU_CRAWL] 📜 Scrolled to ensure next menus are visible

[SUBMENU_CRAWL] Clicking submenu 2/5: Submenu_2
[CLICK_SUBMENU] Attempting to click: UnknownElement
[CLICK_SUBMENU] ❌ Failed to click submenu: UnknownElement
[SUBMENU_CRAWL] Could not click submenu: Submenu_2

[SUBMENU_CRAWL] Clicking submenu 3/5: Submenu_3
[CLICK_SUBMENU] Attempting to click: UnknownElement
[CLICK_SUBMENU] ❌ Failed to click submenu: UnknownElement
[SUBMENU_CRAWL] Could not click submenu: Submenu_3

[SUBMENU_CRAWL] Clicking submenu 4/5: Submenu_4
[CLICK_SUBMENU] Attempting to click: UnknownElement
[CLICK_SUBMENU] ❌ Failed to click submenu: UnknownElement
[SUBMENU_CRAWL] Could not click submenu: Submenu_4

[SUBMENU_CRAWL] Clicking submenu 5/5: Submenu_5
[CLICK_SUBMENU] Attempting to click: UnknownElement
[CLICK_SUBMENU] ❌ Failed to click submenu: UnknownElement
[SUBMENU_CRAWL] Could not click submenu: Submenu_5
[SUBMENU_CRAWL] ✅ Completed submenu crawl for Ruang Murid

🔙 Going back to main page from Ruang Murid...

[NAVIGATE_MAIN] 🏠 Navigating back to main page...

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 1)

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 2)

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
