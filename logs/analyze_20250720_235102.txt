📝 Logging enabled - saving to: logs/analyze_20250720_235102.txt
🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT
Based on analyze_proper_flow.py pattern
Implements your exact hierarchical navigation flow
Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown 4) Server crashes
======================================================================
[PROGRESS] 📂 Loading existing progress file: automation_progress_20250720_230107.json
[PROGRESS] ✅ Loaded progress with 9 menus

📱 Starting Appium session...
[SIMPLE] Starting Appium session for: com.kemendikdasmen.rumahpendidikan
[SIMPLE] Connecting to Appium server...
[SIMPLE] Activating app...
✅ Simple Appium session started and app activated!
⏳ Waiting for app to load...
[WAIT] Waiting for page to load (max 15s)...
[WAIT] ✅ Page loaded with 16 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
🎯 Starting hierarchical crawl...

================================================================================
🎯 STARTING HIERARCHICAL CRAWL WITH PROGRESS TRACKING - YOUR EXACT FLOW
================================================================================
Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu
Real-time JSON sync for visited status tracking
================================================================================
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected

📋 STEP 1: Collecting all elements on MAIN PAGE

[COLLECT] 📋 Starting element collection for: Main Page
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[COLLECT]     1. ⚪ 'Jelajahi Beragam Layanan Pendidikan dalam Genggama'
[COLLECT]     2. ⚪ 'Temukan Ruang Pendidikan Anda'
[LOCATION] 📍 Menu 'Ruang GTK' found at y=1618, center=(225, 1789)
[COLLECT]     3. 🔘 'Ruang GTK'
[LOCATION] 📍 Menu 'Ruang Murid' found at y=1618, center=(555, 1789)
[COLLECT]     4. 🔘 'Ruang Murid'
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=1618, center=(885, 1789)
[COLLECT]     5. 🔘 'Ruang Sekolah'
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=1618, center=(1215, 1789)
[COLLECT]     6. 🔘 'Ruang Bahasa'
[LOCATION] 📍 Menu 'Ruang Pemerintah' found at y=2073, center=(225, 2244)
[COLLECT]     7. 🔘 'Ruang Pemerintah'
[LOCATION] 📍 Menu 'Ruang Mitra' found at y=2073, center=(555, 2244)
[COLLECT]     8. 🔘 'Ruang Mitra'
[LOCATION] 📍 Menu 'Ruang Publik' found at y=2073, center=(885, 2244)
[COLLECT]     9. 🔘 'Ruang Publik'
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=2073, center=(1215, 2244)
[COLLECT]    10. 🔘 'Ruang Orang Tua'
[COLLECT]    11. ⚪ 'Layanan Paling Banyak Diakses'
[LOCATION] 📍 Menu 'Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang' found at y=2829, center=(720, 2829)
[COLLECT]    12. 🔘 'Sumber Belajar
Portal pembelajaran digital interak'
[COLLECT]    13. 🔘 'Beranda
Beranda
Tab 1 of 4'
[COLLECT]    14. 🔘 'Ruang
Ruang
Tab 2 of 4'
[COLLECT]    15. 🔘 'Pemberitahuan
Pemberitahuan
Tab 3 of 4'
[COLLECT]    16. 🔘 'Akun
Akun
Tab 4 of 4'
[COLLECT] Found 16 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[LOCATION] 📍 Menu 'Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat' found at y=2199, center=(720, 2369)
[COLLECT]    17. 🔘 'Pusat Perbukuan
Portal buku pendidikan resmi untuk'
[LOCATION] 📍 Menu 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelolaan Kinerja' found at y=2595, center=(720, 2712)
[COLLECT]    18. 🔘 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelo'
[COLLECT] Found 2 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT]    19. ⚪ 'Sudah Tahu Informasi Ini?'
[COLLECT]    20. 🔘 'Lihat Semua'
[COLLECT]    21. 🔘 'Laman Direktori Portal SPMB Berbasis SIAP SPMB Onl'
[COLLECT] Found 3 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[COLLECT]    22. 🔘 'Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
T'
[COLLECT]    23. 🔘 'Butuh Bantuan?
Lihat pertanyaan yang paling sering'
[COLLECT] Found 2 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT] Found 0 new elements at scroll position 4
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Main Page: 23 total elements
[LOCATION] 📍 Found 11 menu locations
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'

🎯 STEP 2: Found 8 main menus to crawl
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'

============================================================
🎯 PROCESSING MENU: Ruang GTK
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 3/9 menus, 0/59 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang GTK
[CLICK] 📍 Using known location for Ruang GTK
[CLICK] Menu found at scroll position 0, y=1618
[CLICK] 🔄 Smart scrolling to top for position 0...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ✅ At top - found 'jelajahi beragam layanan pendidikan dalam genggaman' at y=144
[SMART_TOP] ✅ Reached top after 3 attempts
[CLICK] Trying pattern 1: //*[@text='Ruang GTK' or @content-desc='Ruang GTK']...
[CLICK] Found element, attempting click...
[CLICK] ✅ Successfully clicked Ruang GTK (regular click)
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 8 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
✅ Successfully navigated to Ruang GTK
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ✅ IMMEDIATELY marked 'Ruang GTK' as VISITED upon navigation

[COLLECT] 📋 Starting element collection for: Menu: Ruang GTK
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[COLLECT]     1. ⚪ 'Ruang Guru dan Tenaga Kependidikan (GTK)'
[COLLECT]     2. ⚪ 'Sumber inspirasi peningkatan kompetensi serta kine'
[COLLECT]     3. ⚪ 'Belajar Berkelanjutan'
[COLLECT]     4. 🔘 'Diklat
Pelatihan terbimbing menggunakan LMS'
[COLLECT]     5. 🔘 'Sertifikasi Pendidik
Pendidikan Profesi Guru (PPG)'
[COLLECT]     6. 🔘 'Pelatihan Mandiri
Materi untuk pengembangan kompet'
[COLLECT]     7. 🔘 'Komunitas
144.000+ komunitas guru & tendik berbagi'
[COLLECT]     8. ⚪ 'Karir dan Kinerja'
[COLLECT] Found 8 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[LOCATION] 📍 Menu 'Pengelolaan Kinerja
Perencanaan, pelaksanaan, dan penilaian kinerja Anda untuk pengembangan diri dan satdik' found at y=1885, center=(720, 2033)
[COLLECT]     9. 🔘 'Pengelolaan Kinerja
Perencanaan, pelaksanaan, dan '
[COLLECT]    10. 🔘 'Seleksi Kepala Sekolah
Info persyaratan, tahapan, '
[COLLECT]    11. 🔘 'Refleksi Kompetensi
Rekomendasi pembelajaran yang '
[COLLECT]    12. ⚪ 'Inspirasi Pembelajaran'
[COLLECT] Found 4 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT]    13. 🔘 'Perangkat Ajar
Modul ajar & proyek, buku teks, dan'
[COLLECT]    14. 🔘 'CP/ATP
Capaian Pembelajaran dan Alur Tujuan Pembel'
[COLLECT]    15. 🔘 'Ide Praktik
Artikel dan video terpilih tentang pra'
[COLLECT]    16. 🔘 'Bukti Karya
Rekam jejak karya, kinerja, dan kompet'
[COLLECT] Found 4 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[COLLECT]    17. 🔘 'Video Inspirasi
Kumpulan video terpilih berisi pen'
[COLLECT]    18. 🔘 'Asesmen (Asesmen Murid & AKM Kelas)
Paket soal den'
[COLLECT]    19. 🔘 'Kelas
Informasi atau data kelompok murid berdasark'
[COLLECT]    20. ⚪ 'Dokumen Rujukan'
[COLLECT] Found 4 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT]    21. 🔘 'Pengelolaan Pembelajaran
Dokumen rujukan untuk Pen'
[LOCATION] 📍 Menu 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelolaan Kinerja' found at y=2254, center=(720, 2333)
[COLLECT]    22. 🔘 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelo'
[COLLECT]    23. 🔘 'Peningkatan Kompetensi
Dokumen rujukan untuk Penin'
[COLLECT]    24. 🔘 'Pengelolaan Satuan Pendidikan
Dokumen rujukan untu'
[COLLECT] Found 4 new elements at scroll position 4
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 5/5)...
[COLLECT] Found 0 new elements at scroll position 5
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Menu: Ruang GTK: 24 total elements
[LOCATION] 📍 Found 2 menu locations
[HIERARCHY] ❌ Error updating hierarchical progress: 'hierarchy'
[HIERARCHY] ✅ Updated 'Ruang GTK' with 24 collected elements

🔍 Starting hierarchical submenu crawl for Ruang GTK...

[HIERARCHY] 📂 Starting hierarchical submenu crawl for Ruang GTK at depth 1
[HIERARCHY] Found 0 potential submenu items
[HIERARCHY] ✅ Completed hierarchical submenu crawl for Ruang GTK

🔙 Going back to main page from Ruang GTK...

[NAVIGATE_MAIN] 🏠 Navigating back to main page...

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
