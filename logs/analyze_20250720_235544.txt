📝 Logging enabled - saving to: logs/analyze_20250720_235544.txt
🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT
Based on analyze_proper_flow.py pattern
Implements your exact hierarchical navigation flow
Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown 4) Server crashes
======================================================================
[PROGRESS] 📂 Loading existing progress file: automation_progress_20250720_230107.json
[PROGRESS] ✅ Loaded progress with 9 menus

📱 Starting Appium session...
[SIMPLE] Starting Appium session for: com.kemendikdasmen.rumahpendidikan
[SIMPLE] Connecting to Appium server...
[SIMPLE] Activating app...
