📝 Logging enabled - saving to: logs/analyze_20250721_000119.txt
🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT
Based on analyze_proper_flow.py pattern
Implements your exact hierarchical navigation flow
Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown 4) Server crashes
======================================================================
[PROGRESS] 📂 Loading existing progress file: automation_progress_20250720_230107.json
[PROGRESS] ✅ Loaded progress with 9 menus

📱 Starting Appium session...
[SIMPLE] Starting Appium session for: com.kemendikdasmen.rumahpendidikan
[SIMPLE] Connecting to Appium server...
[SIMPLE] Activating app...
✅ Simple Appium session started and app activated!
⏳ Waiting for app to load...
[WAIT] Waiting for page to load (max 15s)...
[WAIT] ✅ Page loaded with 16 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
🎯 Starting hierarchical crawl...

================================================================================
🎯 STARTING HIERARCHICAL CRAWL WITH PROGRESS TRACKING - YOUR EXACT FLOW
================================================================================
Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu
Real-time JSON sync for visited status tracking
================================================================================
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected

📋 STEP 1: Collecting all elements on MAIN PAGE

[COLLECT] 📋 Starting element collection for: Main Page
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[COLLECT]     1. ⚪ 'Jelajahi Beragam Layanan Pendidikan dalam Genggama'
[COLLECT]     2. ⚪ 'Temukan Ruang Pendidikan Anda'
[LOCATION] 📍 Menu 'Ruang GTK' found at y=1618, center=(225, 1789)
[COLLECT]     3. 🔘 'Ruang GTK'
[LOCATION] 📍 Menu 'Ruang Murid' found at y=1618, center=(555, 1789)
[COLLECT]     4. 🔘 'Ruang Murid'
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=1618, center=(885, 1789)
[COLLECT]     5. 🔘 'Ruang Sekolah'
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=1618, center=(1215, 1789)
[COLLECT]     6. 🔘 'Ruang Bahasa'
[LOCATION] 📍 Menu 'Ruang Pemerintah' found at y=2073, center=(225, 2244)
[COLLECT]     7. 🔘 'Ruang Pemerintah'
[LOCATION] 📍 Menu 'Ruang Mitra' found at y=2073, center=(555, 2244)
[COLLECT]     8. 🔘 'Ruang Mitra'
[LOCATION] 📍 Menu 'Ruang Publik' found at y=2073, center=(885, 2244)
[COLLECT]     9. 🔘 'Ruang Publik'
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=2073, center=(1215, 2244)
[COLLECT]    10. 🔘 'Ruang Orang Tua'
[COLLECT]    11. ⚪ 'Layanan Paling Banyak Diakses'
[LOCATION] 📍 Menu 'Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang' found at y=2829, center=(720, 2829)
[COLLECT]    12. 🔘 'Sumber Belajar
Portal pembelajaran digital interak'
[COLLECT]    13. 🔘 'Beranda
Beranda
Tab 1 of 4'
[COLLECT]    14. 🔘 'Ruang
Ruang
Tab 2 of 4'
[COLLECT]    15. 🔘 'Pemberitahuan
Pemberitahuan
Tab 3 of 4'
[COLLECT]    16. 🔘 'Akun
Akun
Tab 4 of 4'
[COLLECT] Found 16 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[LOCATION] 📍 Menu 'Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat' found at y=2215, center=(720, 2384)
[COLLECT]    17. 🔘 'Pusat Perbukuan
Portal buku pendidikan resmi untuk'
[LOCATION] 📍 Menu 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelolaan Kinerja' found at y=2610, center=(720, 2720)
[COLLECT]    18. 🔘 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelo'
[COLLECT] Found 2 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT]    19. ⚪ 'Sudah Tahu Informasi Ini?'
[COLLECT]    20. 🔘 'Lihat Semua'
[COLLECT]    21. 🔘 'Laman Direktori Portal SPMB Berbasis SIAP SPMB Onl'
[COLLECT] Found 3 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[COLLECT]    22. 🔘 'Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
T'
[COLLECT]    23. 🔘 'Butuh Bantuan?
Lihat pertanyaan yang paling sering'
[COLLECT] Found 2 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT] Found 0 new elements at scroll position 4
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Main Page: 23 total elements
[LOCATION] 📍 Found 11 menu locations
[HIERARCHY] ✅ Updated 'Main Page' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

🎯 STEP 2: Found 8 main menus to crawl
[HIERARCHY] ✅ Updated 'Ruang GTK' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Murid' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Sekolah' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Bahasa' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Pemerintah' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Mitra' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Publik' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Orang Tua' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

✅ MENU Ruang GTK already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250720_235346
[HIERARCHY] 📊 Elements collected: 24
[HIERARCHY] 📊 Submenus: 0

✅ MENU Ruang Murid already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250720_235633
[HIERARCHY] 📊 Elements collected: 12
[HIERARCHY] 📊 Submenus: 0

============================================================
🎯 PROCESSING MENU: Ruang Sekolah
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 3/9 menus, 0/59 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Sekolah
[CLICK] 📍 Using known location for Ruang Sekolah
[CLICK] Menu found at scroll position 0, y=1618
[CLICK] 🔄 Smart scrolling to top for position 0...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ✅ At top - found 'jelajahi beragam layanan pendidikan dalam genggaman' at y=144
[SMART_TOP] ✅ Reached top after 3 attempts
[CLICK] Trying pattern 1: //*[@text='Ruang Sekolah' or @content-desc='Ruang Sekolah']...
[CLICK] Found element, attempting click...
[CLICK] ✅ Successfully clicked Ruang Sekolah (regular click)
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 8 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
✅ Successfully navigated to Ruang Sekolah
[HIERARCHY] ✅ Updated 'Ruang Sekolah' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ IMMEDIATELY marked 'Ruang Sekolah' as VISITED upon navigation

[COLLECT] 📋 Starting element collection for: Menu: Ruang Sekolah
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=872, center=(426, 935)
[COLLECT]     1. ⚪ 'Ruang Sekolah'
[COLLECT]     2. ⚪ 'Data terpusat untuk akses pengelolaan kebutuhan Sa'
[COLLECT]     3. ⚪ 'Layanan Pendidikan'
[COLLECT]     4. 🔘 'Profil Sekolah
Portal informasi profil dan data se'
[COLLECT]     5. 🔘 'Rapor Satuan Pendidikan
Sistem evaluasi dan pemeta'
[COLLECT]     6. 🔘 'Rencana Kegiatan dan Belanja Sekolah
Sistem pengel'
[COLLECT]     7. 🔘 'Bantuan Operasional
Sistem pengelolaan dan pelapor'
[COLLECT]     8. 🔘 'Akun Pendidikan
Manajemen akun terpadu layanan pen'
[COLLECT] Found 8 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[COLLECT]     9. 🔘 'Pengadaan Barang dan Jasa Sekolah
Sistem digital y'
[COLLECT] Found 1 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT] Found 0 new elements at scroll position 2
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Menu: Ruang Sekolah: 9 total elements
[LOCATION] 📍 Found 1 menu locations
[HIERARCHY] ✅ Updated 'Ruang Sekolah' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Sekolah' with 9 collected elements

🔍 Starting hierarchical submenu crawl for Ruang Sekolah...

[HIERARCHY] 📂 Starting hierarchical submenu crawl for Ruang Sekolah at depth 1
[HIERARCHY] Found 0 potential submenu items
[HIERARCHY] ✅ Completed hierarchical submenu crawl for Ruang Sekolah

🔙 Going back to main page from Ruang Sekolah...

[NAVIGATE_MAIN] 🏠 Navigating back to main page...

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 1)

[BACK] 🔙 Going back to previous page...
[BACK] ⚠️ Could not navigate back
[NAVIGATE_MAIN] Back navigation failed (attempt 2)

[BACK] 🔙 Going back to previous page...
[BACK] ⚠️ Could not navigate back
[NAVIGATE_MAIN] Back navigation failed (attempt 3)
[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts
✅ Completed crawling menu: Ruang Sekolah
[PROGRESS] 🎯 Next unvisited menu from JSON: Ruang Sekolah
[PROGRESS] 📋 Preparing to navigate to: Ruang Sekolah

============================================================
🎯 PROCESSING MENU: Ruang Bahasa
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 4/9 menus, 0/68 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Bahasa
[HEALTH] ❌ Server unhealthy: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)
[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart
[HEALTH] 💡 Recommendation: Restart the automation script
[CLICK] ❌ Server unhealthy, attempting crash recovery for menu: Ruang Bahasa
[CLICK] 💡 Crash recovery should be handled by calling function
❌ Could not click menu: Ruang Bahasa

============================================================
🎯 PROCESSING MENU: Ruang Pemerintah
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 4/9 menus, 0/68 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Pemerintah
[HEALTH] ❌ Server unhealthy: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)
[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart
[HEALTH] 💡 Recommendation: Restart the automation script
[CLICK] ❌ Server unhealthy, attempting crash recovery for menu: Ruang Pemerintah
[CLICK] 💡 Crash recovery should be handled by calling function
❌ Could not click menu: Ruang Pemerintah

============================================================
🎯 PROCESSING MENU: Ruang Mitra
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 4/9 menus, 0/68 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Mitra
[HEALTH] ❌ Server unhealthy: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)
[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart
[HEALTH] 💡 Recommendation: Restart the automation script
[CLICK] ❌ Server unhealthy, attempting crash recovery for menu: Ruang Mitra
[CLICK] 💡 Crash recovery should be handled by calling function
❌ Could not click menu: Ruang Mitra

============================================================
🎯 PROCESSING MENU: Ruang Publik
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 4/9 menus, 0/68 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Publik
[HEALTH] ❌ Server unhealthy: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)
[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart
[HEALTH] 💡 Recommendation: Restart the automation script
[CLICK] ❌ Server unhealthy, attempting crash recovery for menu: Ruang Publik
[CLICK] 💡 Crash recovery should be handled by calling function
❌ Could not click menu: Ruang Publik

============================================================
🎯 PROCESSING MENU: Ruang Orang Tua
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 4/9 menus, 0/68 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Orang Tua
[HEALTH] ❌ Server unhealthy: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)
[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart
[HEALTH] 💡 Recommendation: Restart the automation script
[CLICK] ❌ Server unhealthy, attempting crash recovery for menu: Ruang Orang Tua
[CLICK] 💡 Crash recovery should be handled by calling function
❌ Could not click menu: Ruang Orang Tua

📊 FINAL PROGRESS SUMMARY:
  - Total menus: 9
  - Visited menus: 4
  - Total elements: 68
  - Visited elements: 0
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

💾 Data saved to: locators/simplified_hierarchical_collection_20250721_000235.json
📊 Total elements collected: 32
✅ Visited pages: 0/2
📈 Completion: 0.0%
📋 Status: 0/2 pages completed

================================================================================
🎉 HIERARCHICAL CRAWL WITH PROGRESS TRACKING COMPLETE!
================================================================================
✅ Crawled 8 main menus
✅ Collected data from 2 pages
✅ Real-time progress saved to: automation_progress_20250720_230107.json
================================================================================

🎉 SIMPLIFIED ANALYSIS COMPLETE!
✅ Successfully collected data from 2 pages
🔄 Session restarts performed: 0

💾 Data saved to: locators/simplified_hierarchical_collection_20250721_000235.json
📊 Total elements collected: 32
✅ Visited pages: 0/2
📈 Completion: 0.0%
📋 Status: 0/2 pages completed

✅ ALL PROCESSING COMPLETE - NO CRASHES!
Press Enter to close session...

🔄 Closing Appium session...
✅ Session closed successfully

🎯 ANALYSIS FINISHED
Check the logs for any issues that need attention
