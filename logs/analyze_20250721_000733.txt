📝 Logging enabled - saving to: logs/analyze_20250721_000733.txt
🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT
Based on analyze_proper_flow.py pattern
Implements your exact hierarchical navigation flow
Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown 4) Server crashes
======================================================================
[PROGRESS] 📂 Loading existing progress file: automation_progress_20250720_230107.json
[PROGRESS] ✅ Loaded progress with 9 menus

📱 Starting Appium session...
[SIMPLE] Starting Appium session for: com.kemendikdasmen.rumahpendidikan
[SIMPLE] Connecting to Appium server...
[SIMPLE] Activating app...
✅ Simple Appium session started and app activated!
⏳ Waiting for app to load...
[WAIT] Waiting for page to load (max 15s)...
[WAIT] ✅ Page loaded with 16 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
🎯 Starting hierarchical crawl...

================================================================================
🎯 STARTING HIERARCHICAL CRAWL WITH PROGRESS TRACKING - YOUR EXACT FLOW
================================================================================
Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu
Real-time JSON sync for visited status tracking
================================================================================
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected

📋 STEP 1: Collecting all elements on MAIN PAGE

[COLLECT] 📋 Starting element collection for: Main Page
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[COLLECT]     1. ⚪ 'Jelajahi Beragam Layanan Pendidikan dalam Genggama'
[LOCATION] 📍 Menu 'Temukan Ruang Pendidikan Anda' found at y=1439, center=(525, 1476)
[COLLECT]     2. ⚪ 'Temukan Ruang Pendidikan Anda'
[LOCATION] 📍 Menu 'Ruang GTK' found at y=1618, center=(225, 1789)
[COLLECT]     3. 🔘 'Ruang GTK'
[LOCATION] 📍 Menu 'Ruang Murid' found at y=1618, center=(555, 1789)
[COLLECT]     4. 🔘 'Ruang Murid'
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=1618, center=(885, 1789)
[COLLECT]     5. 🔘 'Ruang Sekolah'
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=1618, center=(1215, 1789)
[COLLECT]     6. 🔘 'Ruang Bahasa'
[LOCATION] 📍 Menu 'Ruang Pemerintah' found at y=2073, center=(225, 2244)
[COLLECT]     7. 🔘 'Ruang Pemerintah'
[LOCATION] 📍 Menu 'Ruang Mitra' found at y=2073, center=(555, 2244)
[COLLECT]     8. 🔘 'Ruang Mitra'
[LOCATION] 📍 Menu 'Ruang Publik' found at y=2073, center=(885, 2244)
[COLLECT]     9. 🔘 'Ruang Publik'
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=2073, center=(1215, 2244)
[COLLECT]    10. 🔘 'Ruang Orang Tua'
[COLLECT]    11. ⚪ 'Layanan Paling Banyak Diakses'
[LOCATION] 📍 Menu 'Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang' found at y=2829, center=(720, 2829)
[COLLECT]    12. 🔘 'Sumber Belajar
Portal pembelajaran digital interak'
[COLLECT]    13. 🔘 'Beranda
Beranda
Tab 1 of 4'
[LOCATION] 📍 Menu 'Ruang
Ruang
Tab 2 of 4' found at y=2830, center=(540, 2933)
[COLLECT]    14. 🔘 'Ruang
Ruang
Tab 2 of 4'
[LOCATION] 📍 Menu 'Pemberitahuan
Pemberitahuan
Tab 3 of 4' found at y=2830, center=(900, 2933)
[COLLECT]    15. 🔘 'Pemberitahuan
Pemberitahuan
Tab 3 of 4'
[LOCATION] 📍 Menu 'Akun
Akun
Tab 4 of 4' found at y=2830, center=(1260, 2933)
[COLLECT]    16. 🔘 'Akun
Akun
Tab 4 of 4'
[COLLECT] Found 16 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[LOCATION] 📍 Menu 'Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat' found at y=2214, center=(720, 2384)
[COLLECT]    17. 🔘 'Pusat Perbukuan
Portal buku pendidikan resmi untuk'
[LOCATION] 📍 Menu 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelolaan Kinerja' found at y=2610, center=(720, 2720)
[COLLECT]    18. 🔘 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelo'
[COLLECT] Found 2 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT]    19. ⚪ 'Sudah Tahu Informasi Ini?'
[LOCATION] 📍 Menu 'Lihat Semua' found at y=2104, center=(1072, 2140)
[COLLECT]    20. 🔘 'Lihat Semua'
[COLLECT]    21. 🔘 'Laman Direktori Portal SPMB Berbasis SIAP SPMB Onl'
[COLLECT] Found 3 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[COLLECT]    22. 🔘 'Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
T'
[LOCATION] 📍 Menu 'Butuh Bantuan?
Lihat pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan' found at y=2807, center=(720, 2818)
[COLLECT]    23. 🔘 'Butuh Bantuan?
Lihat pertanyaan yang paling sering'
[COLLECT] Found 2 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT] Found 0 new elements at scroll position 4
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Main Page: 23 total elements
[LOCATION] 📍 Found 17 menu locations
[HIERARCHY] ✅ Updated 'Main Page' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

🎯 STEP 2: Found 9 main menus to crawl
📋 EXACT MENU ORDER (as they appear on page):
  1. Ruang GTK
  2. Ruang Murid
  3. Ruang Sekolah
  4. Ruang Bahasa
  5. Ruang Pemerintah
  6. Ruang Mitra
  7. Ruang Publik
  8. Ruang Orang Tua
  9. Lihat Semua
[HIERARCHY] ✅ Updated 'Ruang GTK' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Murid' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Sekolah' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Bahasa' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Pemerintah' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Mitra' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Publik' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Orang Tua' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Lihat Semua' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

🎯 STEP 3: Processing menus in EXACT PAGE ORDER

📍 CHECKING MENU 1/9: Ruang GTK
✅ MENU Ruang GTK already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250720_235346
[HIERARCHY] 📊 Elements collected: 24
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 2/9: Ruang Murid
✅ MENU Ruang Murid already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250720_235633
[HIERARCHY] 📊 Elements collected: 12
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 3/9: Ruang Sekolah
✅ MENU Ruang Sekolah already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250721_000203
[HIERARCHY] 📊 Elements collected: 9
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 4/9: Ruang Bahasa
🎯 PROCESSING MENU 4/9: Ruang Bahasa (not yet visited)

============================================================
🎯 PROCESSING MENU: Ruang Bahasa
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 4/10 menus, 0/68 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Bahasa
[CLICK] 📍 Using known location for Ruang Bahasa
[CLICK] Menu found at scroll position 0, y=1618
[CLICK] 🔄 Smart scrolling to top for position 0...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ✅ At top - found 'jelajahi beragam layanan pendidikan dalam genggaman' at y=144
[SMART_TOP] ✅ Reached top after 3 attempts
[CLICK] Trying pattern 1: //*[@text='Ruang Bahasa' or @content-desc='Ruang Bahasa']...
[CLICK] Found element, attempting click...
[CLICK] ✅ Successfully clicked Ruang Bahasa (regular click)
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 7 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
✅ Successfully navigated to Ruang Bahasa
[HIERARCHY] ✅ Updated 'Ruang Bahasa' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ IMMEDIATELY marked 'Ruang Bahasa' as VISITED upon navigation

[COLLECT] 📋 Starting element collection for: Menu: Ruang Bahasa
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=872, center=(413, 935)
[COLLECT]     1. ⚪ 'Ruang Bahasa'
[COLLECT]     2. ⚪ 'Platform digital Bahasa Indonesia agar lebih diken'
[COLLECT]     3. ⚪ 'Layanan Pendidikan'
[COLLECT]     4. 🔘 'Kamus Bahasa
Portal Kamus Besar Bahasa Indonesia ('
[COLLECT]     5. 🔘 'Penerjemahan Daring
Akses produk-produk penerjemah'
[COLLECT]     6. 🔘 'Layanan UKBI
Portal Uji Kemahiran Berbahasa Indone'
[COLLECT]     7. 🔘 'BIPA Daring
Program pembelajaran Bahasa Indonesia '
[COLLECT] Found 7 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[COLLECT] Found 0 new elements at scroll position 1
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Menu: Ruang Bahasa: 7 total elements
[LOCATION] 📍 Found 1 menu locations
[HIERARCHY] ✅ Updated 'Ruang Bahasa' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Bahasa' with 7 collected elements

🔍 Starting hierarchical submenu crawl for Ruang Bahasa...

[HIERARCHY] 📂 Starting hierarchical submenu crawl for Ruang Bahasa at depth 1
[HIERARCHY] Found 0 potential submenu items
[HIERARCHY] ✅ Completed hierarchical submenu crawl for Ruang Bahasa

🔙 Going back to main page from Ruang Bahasa...

[NAVIGATE_MAIN] 🏠 Navigating back to main page...

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 1)

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 2)

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 3)
[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts
✅ Completed crawling menu: Ruang Bahasa
[PROGRESS] 🎯 Next unvisited menu from JSON: Ruang Sekolah
[PROGRESS] 📋 Preparing to navigate to: Ruang Sekolah

📍 CHECKING MENU 5/9: Ruang Pemerintah
🎯 PROCESSING MENU 5/9: Ruang Pemerintah (not yet visited)

============================================================
🎯 PROCESSING MENU: Ruang Pemerintah
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 5/10 menus, 0/75 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Pemerintah
[CLICK] 📍 Using known location for Ruang Pemerintah
[CLICK] Menu found at scroll position 0, y=2073
[CLICK] 🔄 Smart scrolling to top for position 0...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 4/5
