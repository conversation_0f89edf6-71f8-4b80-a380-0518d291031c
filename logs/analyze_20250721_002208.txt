📝 Logging enabled - saving to: logs/analyze_20250721_002208.txt
🚀 SIMPLIFIED ANDROID APP ANALYZER - CRASH RESISTANT
Based on analyze_proper_flow.py pattern
Implements your exact hierarchical navigation flow
Fixes: 1) Duplicate collection 2) Incomplete scroll 3) App shutdown 4) Server crashes
======================================================================
[PROGRESS] 📂 Loading existing progress file: automation_progress_20250720_230107.json
[PROGRESS] ✅ Loaded progress with 9 menus

📱 Starting Appium session...
[SIMPLE] Starting Appium session for: com.kemendikdasmen.rumahpendidikan
[SIMPLE] Connecting to Appium server...
[SIMPLE] Activating app...
✅ Simple Appium session started and app activated!
⏳ Waiting for app to load...
[WAIT] Waiting for page to load (max 15s)...
[WAIT] ✅ Page loaded with 16 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
🎯 Starting hierarchical crawl...

================================================================================
🎯 STARTING HIERARCHICAL CRAWL WITH PROGRESS TRACKING - YOUR EXACT FLOW
================================================================================
Flow: Main → Menu → Submenu → Sub-submenu → Back → Next Menu
Real-time JSON sync for visited status tracking
================================================================================
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected

📋 STEP 1: Collecting all elements on MAIN PAGE

[COLLECT] 📋 Starting element collection for: Main Page
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[COLLECT]     1. ⚪ 'Jelajahi Beragam Layanan Pendidikan dalam Genggama'
[LOCATION] 📍 Menu 'Temukan Ruang Pendidikan Anda' found at y=1439, center=(525, 1476)
[COLLECT]     2. ⚪ 'Temukan Ruang Pendidikan Anda'
[LOCATION] 📍 Menu 'Ruang GTK' found at y=1618, center=(225, 1789)
[COLLECT]     3. 🔘 'Ruang GTK'
[LOCATION] 📍 Menu 'Ruang Murid' found at y=1618, center=(555, 1789)
[COLLECT]     4. 🔘 'Ruang Murid'
[LOCATION] 📍 Menu 'Ruang Sekolah' found at y=1618, center=(885, 1789)
[COLLECT]     5. 🔘 'Ruang Sekolah'
[LOCATION] 📍 Menu 'Ruang Bahasa' found at y=1618, center=(1215, 1789)
[COLLECT]     6. 🔘 'Ruang Bahasa'
[LOCATION] 📍 Menu 'Ruang Pemerintah' found at y=2073, center=(225, 2244)
[COLLECT]     7. 🔘 'Ruang Pemerintah'
[LOCATION] 📍 Menu 'Ruang Mitra' found at y=2073, center=(555, 2244)
[COLLECT]     8. 🔘 'Ruang Mitra'
[LOCATION] 📍 Menu 'Ruang Publik' found at y=2073, center=(885, 2244)
[COLLECT]     9. 🔘 'Ruang Publik'
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=2073, center=(1215, 2244)
[COLLECT]    10. 🔘 'Ruang Orang Tua'
[COLLECT]    11. ⚪ 'Layanan Paling Banyak Diakses'
[LOCATION] 📍 Menu 'Sumber Belajar
Portal pembelajaran digital interaktif untuk semua jenjang' found at y=2829, center=(720, 2829)
[COLLECT]    12. 🔘 'Sumber Belajar
Portal pembelajaran digital interak'
[COLLECT]    13. 🔘 'Beranda
Beranda
Tab 1 of 4'
[LOCATION] 📍 Menu 'Ruang
Ruang
Tab 2 of 4' found at y=2830, center=(540, 2933)
[COLLECT]    14. 🔘 'Ruang
Ruang
Tab 2 of 4'
[LOCATION] 📍 Menu 'Pemberitahuan
Pemberitahuan
Tab 3 of 4' found at y=2830, center=(900, 2933)
[COLLECT]    15. 🔘 'Pemberitahuan
Pemberitahuan
Tab 3 of 4'
[LOCATION] 📍 Menu 'Akun
Akun
Tab 4 of 4' found at y=2830, center=(1260, 2933)
[COLLECT]    16. 🔘 'Akun
Akun
Tab 4 of 4'
[COLLECT] Found 16 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[LOCATION] 📍 Menu 'Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat' found at y=2214, center=(720, 2384)
[COLLECT]    17. 🔘 'Pusat Perbukuan
Portal buku pendidikan resmi untuk'
[LOCATION] 📍 Menu 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelolaan Kinerja' found at y=2610, center=(720, 2720)
[COLLECT]    18. 🔘 'Pengelolaan Kinerja
⁠Dokumen rujukan untuk Pengelo'
[COLLECT] Found 2 new elements at scroll position 1
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 2/5)...
[COLLECT]    19. ⚪ 'Sudah Tahu Informasi Ini?'
[LOCATION] 📍 Menu 'Lihat Semua' found at y=2110, center=(1072, 2146)
[COLLECT]    20. 🔘 'Lihat Semua'
[COLLECT]    21. 🔘 'Laman Direktori Portal SPMB Berbasis SIAP SPMB Onl'
[COLLECT] Found 3 new elements at scroll position 2
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 3/5)...
[COLLECT]    22. 🔘 'Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
T'
[LOCATION] 📍 Menu 'Butuh Bantuan?
Lihat pertanyaan yang paling sering ditanya, atau kunjungi pusat bantuan' found at y=2503, center=(720, 2666)
[COLLECT]    23. 🔘 'Butuh Bantuan?
Lihat pertanyaan yang paling sering'
[COLLECT] Found 2 new elements at scroll position 3
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 4/5)...
[COLLECT] Found 0 new elements at scroll position 4
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Main Page: 23 total elements
[LOCATION] 📍 Found 17 menu locations
[HIERARCHY] ✅ Updated 'Main Page' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

🎯 STEP 2: Found 9 main menus to crawl
📋 EXACT MENU ORDER (as they appear on page):
  1. Ruang GTK
  2. Ruang Murid
  3. Ruang Sekolah
  4. Ruang Bahasa
  5. Ruang Pemerintah
  6. Ruang Mitra
  7. Ruang Publik
  8. Ruang Orang Tua
  9. Lihat Semua
[HIERARCHY] ✅ Updated 'Ruang GTK' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Murid' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Sekolah' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Bahasa' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Pemerintah' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Mitra' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Publik' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Orang Tua' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Lihat Semua' - Level: 1, Visited: False
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

🎯 STEP 3: Processing menus in EXACT PAGE ORDER

📍 CHECKING MENU 1/9: Ruang GTK
✅ MENU Ruang GTK already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250720_235346
[HIERARCHY] 📊 Elements collected: 24
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 2/9: Ruang Murid
✅ MENU Ruang Murid already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250720_235633
[HIERARCHY] 📊 Elements collected: 12
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 3/9: Ruang Sekolah
✅ MENU Ruang Sekolah already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250721_000203
[HIERARCHY] 📊 Elements collected: 9
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 4/9: Ruang Bahasa
✅ MENU Ruang Bahasa already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250721_000821
[HIERARCHY] 📊 Elements collected: 7
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 5/9: Ruang Pemerintah
✅ MENU Ruang Pemerintah already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250721_001336
[HIERARCHY] 📊 Elements collected: 7
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 6/9: Ruang Mitra
✅ MENU Ruang Mitra already visited in hierarchical JSON - skipping
[HIERARCHY] 📅 Visit timestamp: 20250721_002037
[HIERARCHY] 📊 Elements collected: 6
[HIERARCHY] 📊 Submenus: 0

📍 CHECKING MENU 7/9: Ruang Publik
🎯 PROCESSING MENU 7/9: Ruang Publik (not yet visited)

============================================================
🎯 PROCESSING MENU: Ruang Publik
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 7/10 menus, 0/88 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Publik
[CLICK] 📍 Using known location for Ruang Publik
[CLICK] Menu found at scroll position 0, y=2073
[CLICK] 🔄 Smart scrolling to top for position 0...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Not at top, need to scroll up...
[SMART_TOP] Scroll to top attempt 1/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 2/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ❌ Not at top - no top indicators found near top
[SMART_TOP] Scroll to top attempt 3/5
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ✅ At top - found 'jelajahi beragam layanan pendidikan dalam genggaman' at y=144
[SMART_TOP] ✅ Reached top after 3 attempts
[CLICK] Trying pattern 1: //*[@text='Ruang Publik' or @content-desc='Ruang Publik']...
[CLICK] Found element, attempting click...
[CLICK] ✅ Successfully clicked Ruang Publik (regular click)
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 8 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
✅ Successfully navigated to Ruang Publik
[HIERARCHY] ✅ Updated 'Ruang Publik' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ IMMEDIATELY marked 'Ruang Publik' as VISITED upon navigation
[HEALTH] 🔍 Performing comprehensive health check...
[APP_CHECK] 📱 Current package: com.kemendikdasmen.rumahpendidikan
[APP_CHECK] 📱 Current activity: .MainActivity
[APP_CHECK] ✅ Target app is running
[HEALTH] ✅ Both server and app are healthy

[COLLECT] 📋 Starting element collection for: Menu: Ruang Publik
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[LOCATION] 📍 Menu 'Ruang Publik' found at y=872, center=(391, 935)
[COLLECT]     1. ⚪ 'Ruang Publik'
[COLLECT]     2. ⚪ 'Informasi dan materi pendidikan, serta layanan pen'
[COLLECT]     3. ⚪ 'Layanan Pendidikan'
[LOCATION] 📍 Menu 'Pusat Perbukuan
Portal buku pendidikan resmi untuk siswa, guru, dan masyarakat' found at y=1523, center=(720, 1637)
[COLLECT]     4. 🔘 'Pusat Perbukuan
Portal buku pendidikan resmi untuk'
[COLLECT]     5. 🔘 'Bantuan Pendidikan
Portal informasi bantuan dan be'
[COLLECT]     6. 🔘 'Layanan Informasi dan Pengaduan
Layanan pengaduan '
[COLLECT]     7. 🔘 'Informasi Data Pendidikan
Data resmi pendidikan da'
[COLLECT]     8. 🔘 'Publikasi Ilmiah
Portal hasil publikasi penelitian'
[COLLECT] Found 8 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[COLLECT] Found 0 new elements at scroll position 1
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Menu: Ruang Publik: 8 total elements
[LOCATION] 📍 Found 2 menu locations
[HIERARCHY] ✅ Updated 'Ruang Publik' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Publik' with 8 collected elements

🔍 Starting hierarchical submenu crawl for Ruang Publik...

[HIERARCHY] 📂 Starting hierarchical submenu crawl for Ruang Publik at depth 1
[HIERARCHY] Found 0 potential submenu items
[HIERARCHY] ✅ Completed hierarchical submenu crawl for Ruang Publik

🔙 Going back to main page from Ruang Publik...
[HEALTH] 🔍 Performing comprehensive health check...
[APP_CHECK] 📱 Current package: com.kemendikdasmen.rumahpendidikan
[APP_CHECK] 📱 Current activity: .MainActivity
[APP_CHECK] ✅ Target app is running
[HEALTH] ✅ Both server and app are healthy

[NAVIGATE_MAIN] 🏠 Navigating back to main page...

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 1)

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 2)

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 3)
[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts
[HEALTH] 🔍 Performing comprehensive health check...
[APP_CHECK] 📱 Current package: com.google.android.apps.nexuslauncher
[APP_CHECK] 📱 Current activity: .NexusLauncherActivity
[APP_CHECK] ❌ Target app not running - current: com.google.android.apps.nexuslauncher
[HEALTH] ❌ App not running
❌ App/server issue after going back from Ruang Publik: app_closed

🔄 APP RECOVERY: App closed unexpectedly, attempting recovery...
[RECOVERY] 📱 Target package: com.kemendikdasmen.rumahpendidikan
[RECOVERY] 🚀 Attempting to activate app...
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 7 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[RECOVERY] ✅ Successfully activated app
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[RECOVERY] ✅ Handled popups

[RECOVERY] 🏠 Navigating to main page after recovery...
[CONTEXT] ✅ In main app context
[RECOVERY] 📱 Current context: APP, app: com.kemendikdasmen.rumahpendidikan
[RECOVERY] 🔄 Not in main app, attempting to return...
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 16 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[RECOVERY] ✅ Successfully activated main app
[RECOVERY] ✅ Successfully on main page
[RECOVERY] ✅ Navigated to main page
[APP_CHECK] 📱 Current package: com.kemendikdasmen.rumahpendidikan
[APP_CHECK] 📱 Current activity: .MainActivity
[APP_CHECK] ✅ Target app is running
[RECOVERY] 🎉 App recovery successful!
✅ Completed crawling menu: Ruang Publik
[PROGRESS] 🎯 Next unvisited menu from JSON: Ruang Sekolah
[PROGRESS] 📋 Preparing to navigate to: Ruang Sekolah

📍 CHECKING MENU 8/9: Ruang Orang Tua
🎯 PROCESSING MENU 8/9: Ruang Orang Tua (not yet visited)

============================================================
🎯 PROCESSING MENU: Ruang Orang Tua
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 8/10 menus, 0/96 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Ruang Orang Tua
[CLICK] 📍 Using known location for Ruang Orang Tua
[CLICK] Menu found at scroll position 0, y=2073
[CLICK] 🔄 Smart scrolling to top for position 0...
[SMART_TOP] 📍 Smart scroll to top...
[TOP_CHECK] 🔍 Checking if at top of page...
[TOP_CHECK] ✅ At top - found 'jelajahi beragam layanan pendidikan dalam genggaman' at y=487
[SMART_TOP] ✅ Already at top - no scroll needed (prevents pull-to-refresh)
[CLICK] Trying pattern 1: //*[@text='Ruang Orang Tua' or @content-desc='Ruang Orang Tu...
[CLICK] Found element, attempting click...
[CLICK] ✅ Successfully clicked Ruang Orang Tua (regular click)
[WAIT] Waiting for page to load (max 10s)...
[WAIT] ✅ Page loaded with 6 elements
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[CONTEXT] ✅ In main app context
✅ Successfully navigated to Ruang Orang Tua
[HIERARCHY] ✅ Updated 'Ruang Orang Tua' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ IMMEDIATELY marked 'Ruang Orang Tua' as VISITED upon navigation
[HEALTH] 🔍 Performing comprehensive health check...
[APP_CHECK] 📱 Current package: com.kemendikdasmen.rumahpendidikan
[APP_CHECK] 📱 Current activity: .MainActivity
[APP_CHECK] ✅ Target app is running
[HEALTH] ✅ Both server and app are healthy

[COLLECT] 📋 Starting element collection for: Menu: Ruang Orang Tua
[POPUP] 🔍 Checking for login popup...
[POPUP] ✅ No login popup detected
[COLLECT] Collecting elements (scroll position 0/5)...
[LOCATION] 📍 Menu 'Ruang Orang Tua' found at y=872, center=(476, 935)
[COLLECT]     1. ⚪ 'Ruang Orang Tua'
[COLLECT]     2. ⚪ 'Sarana partisipasi orang tua melalui pantauan capa'
[COLLECT]     3. ⚪ 'Layanan Pendidikan'
[COLLECT]     4. 🔘 'Layanan Informasi dan Pengaduan
Layanan pengaduan '
[COLLECT]     5. 🔘 'Panduan Pendampingan
Portal informasi terpadu untu'
[COLLECT]     6. 🔘 'Konsultasi Pendidikan
Layanan konsultasi orang tua'
[COLLECT] Found 6 new elements at scroll position 0
[COLLECT] Scrolling down to reveal more elements...
[COLLECT] Collecting elements (scroll position 1/5)...
[COLLECT] Found 0 new elements at scroll position 1
[COLLECT] ✅ No new elements found, collection complete
[COLLECT] ✅ Collection complete for Menu: Ruang Orang Tua: 6 total elements
[LOCATION] 📍 Found 1 menu locations
[HIERARCHY] ✅ Updated 'Ruang Orang Tua' - Level: 1, Visited: True
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined
[HIERARCHY] ✅ Updated 'Ruang Orang Tua' with 6 collected elements

🔍 Starting hierarchical submenu crawl for Ruang Orang Tua...

[HIERARCHY] 📂 Starting hierarchical submenu crawl for Ruang Orang Tua at depth 1
[HIERARCHY] Found 0 potential submenu items
[HIERARCHY] ✅ Completed hierarchical submenu crawl for Ruang Orang Tua

🔙 Going back to main page from Ruang Orang Tua...
[HEALTH] 🔍 Performing comprehensive health check...
[APP_CHECK] 📱 Current package: com.kemendikdasmen.rumahpendidikan
[APP_CHECK] 📱 Current activity: .MainActivity
[APP_CHECK] ✅ Target app is running
[HEALTH] ✅ Both server and app are healthy

[NAVIGATE_MAIN] 🏠 Navigating back to main page...

[BACK] 🔙 Going back to previous page...
[BACK] ✅ Used back button
[NAVIGATE_MAIN] Not on main page yet, trying again... (attempt 1)

[BACK] 🔙 Going back to previous page...
[BACK] ⚠️ Could not navigate back
[NAVIGATE_MAIN] Back navigation failed (attempt 2)

[BACK] 🔙 Going back to previous page...
[BACK] ⚠️ Could not navigate back
[NAVIGATE_MAIN] Back navigation failed (attempt 3)
[NAVIGATE_MAIN] ⚠️ Could not reach main page after all attempts
[HEALTH] 🔍 Performing comprehensive health check...
[HEALTH] ❌ Server unhealthy: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)
[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart
[HEALTH] 💡 Recommendation: Restart the automation script
[HEALTH] ❌ Server unhealthy
❌ App/server issue after going back from Ruang Orang Tua: server_crash
✅ Completed crawling menu: Ruang Orang Tua
[PROGRESS] 🎯 Next unvisited menu from JSON: Ruang Sekolah
[PROGRESS] 📋 Preparing to navigate to: Ruang Sekolah

📍 CHECKING MENU 9/9: Lihat Semua
🎯 PROCESSING MENU 9/9: Lihat Semua (not yet visited)

============================================================
🎯 PROCESSING MENU: Lihat Semua
📊 PROGRESS: Checking real-time status from JSON...
📊 Current: 9/10 menus, 0/102 elements
📋 Remaining unvisited: ['Ruang Sekolah', 'Ruang Bahasa', 'Ruang Pemerintah', 'Ruang Mitra', 'Ruang Publik', 'Ruang Orang Tua']
============================================================

[CLICK] 🎯 Attempting to click menu: Lihat Semua
[HEALTH] ❌ Server unhealthy: Message: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: 'POST /elements' cannot be proxied to UiAutomator2 server because the instrumentation process is not running (probably crashed). Check the server log and/or the logcat output for more details
    at UIA2Proxy.command (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:359:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at AndroidUiautomator2Driver.doFindElementOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/commands/find.js:40:5)
    at doFind (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:47:17)
    at wrappedCondFn (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:141:14)
    at spin (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:219:20)
    at waitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/asyncbox/lib/asyncbox.js:236:10)
    at AndroidUiautomator2Driver.implicitWaitForCondition (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/timeout.ts:143:12)
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:70:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElements (/Users/<USER>/.nvm/versions/node/v20.18.1/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:79:12)
[HEALTH] 🚨 UiAutomator2 server crashed - session needs restart
[HEALTH] 💡 Recommendation: Restart the automation script
[CLICK] ❌ Server unhealthy, attempting crash recovery for menu: Lihat Semua
[CLICK] 💡 Crash recovery should be handled by calling function
❌ Could not click menu: Lihat Semua

📊 FINAL PROGRESS SUMMARY:
  - Total menus: 10
  - Visited menus: 9
  - Total elements: 102
  - Visited elements: 0
[PROGRESS] ❌ Error saving progress: name 'visited_menus' is not defined

💾 Data saved to: locators/simplified_hierarchical_collection_20250721_002547.json
📊 Total elements collected: 37
✅ Visited pages: 0/3
📈 Completion: 0.0%
📋 Status: 0/3 pages completed

================================================================================
🎉 HIERARCHICAL CRAWL WITH PROGRESS TRACKING COMPLETE!
================================================================================
✅ Crawled 9 main menus
✅ Collected data from 3 pages
✅ Real-time progress saved to: automation_progress_20250720_230107.json
================================================================================

🎉 SIMPLIFIED ANALYSIS COMPLETE!
✅ Successfully collected data from 3 pages
🔄 Session restarts performed: 0

💾 Data saved to: locators/simplified_hierarchical_collection_20250721_002547.json
📊 Total elements collected: 37
✅ Visited pages: 0/3
📈 Completion: 0.0%
📋 Status: 0/3 pages completed

✅ ALL PROCESSING COMPLETE - NO CRASHES!
Press Enter to close session...

🔄 Closing Appium session...
✅ Session closed successfully

🎯 ANALYSIS FINISHED
Check the logs for any issues that need attention
