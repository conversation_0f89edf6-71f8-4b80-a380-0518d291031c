import os
import subprocess
import time
import yaml
import sys
import json
from datetime import datetime
from typing import Dict, List, Any
import re
from pathlib import Path
import platform

CONFIG_PATH = os.path.join("config", "config.yaml")

def load_config():
    with open(CONFIG_PATH, 'r') as f:
        return yaml.safe_load(f)

def start_emulator(avd_name, headless=True):
    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
    if 'emulator-' in result.stdout:
        print("Emulator already running.")
        return
    headless_flag = ['-no-window'] if headless else []
    subprocess.Popen(['emulator', '-avd', avd_name] + headless_flag)
    print(f"Starting emulator {avd_name} (headless={headless})...")
    while True:
        result = subprocess.run(['adb', 'shell', 'getprop', 'sys.boot_completed'], capture_output=True, text=True)
        if '1' in result.stdout:
            print("Emulator booted.")
            break
        print("Waiting for emulator to boot...")
        time.sleep(5)

def get_apk_path(apk_folder):
    apks = [f for f in os.listdir(apk_folder) if f.endswith('.apk')]
    if not apks:
        raise FileNotFoundError("No APK found in folder: " + apk_folder)
    return os.path.join(apk_folder, apks[0])

def get_package_name(apk_path):
    result = subprocess.run(['aapt', 'dump', 'badging', apk_path], capture_output=True, text=True)
    for line in result.stdout.splitlines():
        if line.startswith('package:'):
            for part in line.split():
                if part.startswith('name='):
                    return part.split('=')[1].strip("'")
    raise Exception("Could not determine package name from APK.")

def is_app_installed(package):
    result = subprocess.run(['adb', 'shell', 'pm', 'list', 'packages'], capture_output=True, text=True)
    return package in result.stdout

def uninstall_app(package):
    subprocess.run(['adb', 'uninstall', package])
    print(f"Uninstalled {package}")

def install_app(apk_path):
    subprocess.run(['adb', 'install', '-r', apk_path])
    print(f"Installed {apk_path}")

def prompt_user(msg):
    ans = input(msg + ' [y/n]: ').strip().lower()
    return ans == 'y'

def get_os_version():
    """Get Android OS version"""
    try:
        result = subprocess.run(['adb', 'shell', 'getprop', 'ro.build.version.release'], capture_output=True, text=True)
        return result.stdout.strip()
    except:
        return "Unknown"

def get_device_info():
    """Get comprehensive device information"""
    device_info = {}
    try:
        # Android version
        device_info['android_version'] = get_os_version()

        # Device model
        result = subprocess.run(['adb', 'shell', 'getprop', 'ro.product.model'], capture_output=True, text=True)
        device_info['device_model'] = result.stdout.strip()

        # Device manufacturer
        result = subprocess.run(['adb', 'shell', 'getprop', 'ro.product.manufacturer'], capture_output=True, text=True)
        device_info['manufacturer'] = result.stdout.strip()

        # API level
        result = subprocess.run(['adb', 'shell', 'getprop', 'ro.build.version.sdk'], capture_output=True, text=True)
        device_info['api_level'] = result.stdout.strip()

        # Screen density
        result = subprocess.run(['adb', 'shell', 'wm', 'density'], capture_output=True, text=True)
        device_info['screen_density'] = result.stdout.strip()

        # Screen size
        result = subprocess.run(['adb', 'shell', 'wm', 'size'], capture_output=True, text=True)
        device_info['screen_size'] = result.stdout.strip()

    except Exception as e:
        print(f"Warning: Could not get complete device info: {e}")

    return device_info

class ProfessionalTestRunner:
    def __init__(self, config: dict):
        self.config = config
        self.start_time = None
        self.end_time = None
        self.test_results = []
        self.device_info = {}
        self.package_name = ""

    def run_gherkin_scenarios(self, features_folder: str, report_folder: str, package: str) -> str:
        """Run Gherkin scenarios and generate professional HTML report"""
        self.package_name = package
        self.device_info = get_device_info()

        feature_file = os.path.join(features_folder, f"{package}.feature")
        if not os.path.exists(feature_file):
            print(f"Feature file not found: {feature_file}")
            return self._generate_error_report(report_folder, f"Feature file not found: {feature_file}")

        print(f"🚀 Starting Gherkin scenario execution for {package}")
        print(f"📱 Device: {self.device_info.get('manufacturer', 'Unknown')} {self.device_info.get('device_model', 'Unknown')}")
        print(f"🤖 Android Version: {self.device_info.get('android_version', 'Unknown')}")

        self.start_time = datetime.now()
        print(f"⏰ Test execution started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Run behave with JSON formatter to capture detailed results
        json_output_file = os.path.join(report_folder, f"behave_results_{int(time.time())}.json")
        os.makedirs(report_folder, exist_ok=True)

        # Run behave with multiple formatters
        cmd = [
            'behave', feature_file,
            '--no-capture',
            '--format=json',
            f'--outfile={json_output_file}',
            '--format=pretty',  # Also show pretty output in console
            '--no-skipped',
            '--no-junit'
        ]

        print(f"🔄 Executing command: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
            self.end_time = datetime.now()

            print(f"✅ Test execution completed at: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Parse results
            self._parse_behave_results(json_output_file, result)

            # Generate professional HTML report
            html_report_path = self._generate_professional_html_report(report_folder)

            return html_report_path

        except subprocess.TimeoutExpired:
            self.end_time = datetime.now()
            print("❌ Test execution timed out after 30 minutes")
            return self._generate_error_report(report_folder, "Test execution timed out")

        except Exception as e:
            self.end_time = datetime.now()
            print(f"❌ Error during test execution: {e}")
            return self._generate_error_report(report_folder, str(e))

    def _parse_behave_results(self, json_file: str, process_result):
        """Parse behave JSON results"""
        try:
            if os.path.exists(json_file):
                with open(json_file, 'r') as f:
                    behave_data = json.load(f)

                for feature in behave_data:
                    feature_name = feature.get('name', 'Unknown Feature')

                    for scenario in feature.get('elements', []):
                        scenario_result = {
                            'feature_name': feature_name,
                            'scenario_name': scenario.get('name', 'Unknown Scenario'),
                            'scenario_type': scenario.get('type', 'scenario'),
                            'status': 'passed',
                            'steps': [],
                            'duration': 0,
                            'error_message': '',
                            'tags': scenario.get('tags', [])
                        }

                        total_duration = 0
                        scenario_failed = False

                        for step in scenario.get('steps', []):
                            step_result = {
                                'keyword': step.get('keyword', ''),
                                'name': step.get('name', ''),
                                'status': step.get('result', {}).get('status', 'undefined'),
                                'duration': step.get('result', {}).get('duration', 0),
                                'error_message': ''
                            }

                            if step_result['duration']:
                                total_duration += step_result['duration']

                            # Check for errors
                            if step_result['status'] in ['failed', 'error']:
                                scenario_failed = True
                                error_msg = step.get('result', {}).get('error_message', '')
                                if not error_msg:
                                    # Try to get error from traceback
                                    traceback = step.get('result', {}).get('traceback', [])
                                    if traceback:
                                        error_msg = '\n'.join(traceback)
                                step_result['error_message'] = error_msg
                                scenario_result['error_message'] = error_msg

                            scenario_result['steps'].append(step_result)

                        scenario_result['duration'] = total_duration
                        if scenario_failed:
                            scenario_result['status'] = 'failed'

                        self.test_results.append(scenario_result)

            else:
                # Fallback: parse from process output
                self._parse_console_output(process_result)

        except Exception as e:
            print(f"Warning: Could not parse behave results: {e}")
            self._parse_console_output(process_result)

    def _parse_console_output(self, process_result):
        """Fallback method to parse console output"""
        output_lines = process_result.stdout.split('\n') if process_result.stdout else []

        current_scenario = None
        for line in output_lines:
            line = line.strip()

            # Detect scenario start
            if line.startswith('Scenario:'):
                if current_scenario:
                    self.test_results.append(current_scenario)

                current_scenario = {
                    'feature_name': 'Unknown Feature',
                    'scenario_name': line.replace('Scenario:', '').strip(),
                    'scenario_type': 'scenario',
                    'status': 'passed',
                    'steps': [],
                    'duration': 0,
                    'error_message': '',
                    'tags': []
                }

            # Detect step results
            elif current_scenario and ('✓' in line or '✗' in line or 'PASSED' in line or 'FAILED' in line):
                step_status = 'passed' if ('✓' in line or 'PASSED' in line) else 'failed'
                if step_status == 'failed':
                    current_scenario['status'] = 'failed'

        # Add last scenario
        if current_scenario:
            self.test_results.append(current_scenario)

    def _generate_professional_html_report(self, report_folder: str) -> str:
        """Generate professional HTML report"""
        timestamp = int(time.time())
        report_dir = os.path.join(report_folder, f"professional_report_{timestamp}")
        os.makedirs(report_dir, exist_ok=True)

        report_file = os.path.join(report_dir, "index.html")

        # Calculate statistics
        total_scenarios = len(self.test_results)
        passed_scenarios = len([r for r in self.test_results if r['status'] == 'passed'])
        failed_scenarios = total_scenarios - passed_scenarios

        total_duration = sum(r['duration'] for r in self.test_results)
        execution_duration = (self.end_time - self.start_time).total_seconds() if self.end_time and self.start_time else 0

        # Generate HTML content
        html_content = self._generate_html_template(
            total_scenarios, passed_scenarios, failed_scenarios,
            total_duration, execution_duration
        )

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"📊 Professional HTML report generated: {report_file}")
        return report_file

    def _generate_html_template(self, total_scenarios: int, passed_scenarios: int,
                               failed_scenarios: int, total_duration: float,
                               execution_duration: float) -> str:
        """Generate the HTML template for the report"""

        # Calculate pass rate
        pass_rate = (passed_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0

        # Format durations
        def format_duration(seconds):
            if seconds < 60:
                return f"{seconds:.2f}s"
            elif seconds < 3600:
                return f"{seconds//60:.0f}m {seconds%60:.2f}s"
            else:
                return f"{seconds//3600:.0f}h {(seconds%3600)//60:.0f}m {seconds%60:.2f}s"

        # Generate scenario details HTML
        scenarios_html = ""
        for i, result in enumerate(self.test_results, 1):
            status_class = "success" if result['status'] == 'passed' else "danger"
            status_icon = "✅" if result['status'] == 'passed' else "❌"

            steps_html = ""
            for step in result['steps']:
                step_status_class = "success" if step['status'] == 'passed' else "danger"
                step_icon = "✅" if step['status'] == 'passed' else "❌"

                error_detail = ""
                if step['error_message']:
                    error_detail = f"""
                    <div class="alert alert-danger mt-2">
                        <strong>Error Details:</strong><br>
                        <pre>{step['error_message']}</pre>
                    </div>
                    """

                steps_html += f"""
                <tr class="table-{step_status_class}">
                    <td>{step_icon}</td>
                    <td>{step['keyword']}</td>
                    <td>{step['name']}</td>
                    <td>{step['status'].upper()}</td>
                    <td>{format_duration(step['duration'])}</td>
                </tr>
                {f'<tr><td colspan="5">{error_detail}</td></tr>' if error_detail else ''}
                """

            error_section = ""
            if result['error_message']:
                error_section = f"""
                <div class="alert alert-danger">
                    <h6><strong>Failure Reason:</strong></h6>
                    <pre>{result['error_message']}</pre>
                </div>
                """

            scenarios_html += f"""
            <div class="card mb-4">
                <div class="card-header bg-{status_class} text-white">
                    <h5 class="mb-0">
                        {status_icon} Scenario {i}: {result['scenario_name']}
                        <span class="badge badge-light float-right">{result['status'].upper()}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Feature:</strong> {result['feature_name']}<br>
                            <strong>Type:</strong> {result['scenario_type']}<br>
                            <strong>Duration:</strong> {format_duration(result['duration'])}
                        </div>
                        <div class="col-md-6">
                            <strong>Tags:</strong> {', '.join([tag['name'] for tag in result['tags']]) if result['tags'] else 'None'}
                        </div>
                    </div>

                    {error_section}

                    <h6>Steps:</h6>
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Keyword</th>
                                <th>Step</th>
                                <th>Result</th>
                                <th>Duration</th>
                            </tr>
                        </thead>
                        <tbody>
                            {steps_html}
                        </tbody>
                    </table>
                </div>
            </div>
            """

        # Complete HTML template
        html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Test Report - {self.package_name}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .report-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }}
        .stats-card {{
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }}
        .stats-card:hover {{
            transform: translateY(-2px);
        }}
        .pass-rate {{
            font-size: 3rem;
            font-weight: bold;
        }}
        .device-info {{
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }}
        pre {{
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }}
    </style>
</head>
<body>
    <div class="report-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fas fa-chart-line"></i> Professional Test Report</h1>
                    <h3>{self.package_name}</h3>
                </div>
                <div class="col-md-4 text-end">
                    <h4><i class="fas fa-calendar"></i> {self.start_time.strftime('%Y-%m-%d')}</h4>
                    <p><i class="fas fa-clock"></i> Generated at {datetime.now().strftime('%H:%M:%S')}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Executive Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="pass-rate text-{'success' if pass_rate >= 80 else 'warning' if pass_rate >= 60 else 'danger'}">{pass_rate:.1f}%</div>
                        <h6>Pass Rate</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="text-success">{passed_scenarios}</h3>
                        <h6>Passed</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="text-danger">{failed_scenarios}</h3>
                        <h6>Failed</h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="text-info">{total_scenarios}</h3>
                        <h6>Total</h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Execution Details -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card device-info">
                    <div class="card-header">
                        <h5><i class="fas fa-mobile-alt"></i> Device Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr><td><strong>Android Version:</strong></td><td>{self.device_info.get('android_version', 'Unknown')}</td></tr>
                            <tr><td><strong>Device Model:</strong></td><td>{self.device_info.get('manufacturer', 'Unknown')} {self.device_info.get('device_model', 'Unknown')}</td></tr>
                            <tr><td><strong>API Level:</strong></td><td>{self.device_info.get('api_level', 'Unknown')}</td></tr>
                            <tr><td><strong>Screen Size:</strong></td><td>{self.device_info.get('screen_size', 'Unknown')}</td></tr>
                            <tr><td><strong>Screen Density:</strong></td><td>{self.device_info.get('screen_density', 'Unknown')}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> Execution Timeline</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr><td><strong>Start Time:</strong></td><td>{self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else 'Unknown'}</td></tr>
                            <tr><td><strong>End Time:</strong></td><td>{self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else 'Unknown'}</td></tr>
                            <tr><td><strong>Total Duration:</strong></td><td>{format_duration(execution_duration)}</td></tr>
                            <tr><td><strong>Test Duration:</strong></td><td>{format_duration(total_duration)}</td></tr>
                            <tr><td><strong>Host OS:</strong></td><td>{platform.system()} {platform.release()}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scenario Results -->
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-list-check"></i> Scenario Results</h2>
                {scenarios_html}
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-5 py-4 bg-light text-center">
            <p class="mb-0">
                <i class="fas fa-robot"></i> Generated by Enhanced Android Automation System
                | <i class="fas fa-calendar"></i> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
        """

        return html_template

    def _generate_error_report(self, report_folder: str, error_message: str) -> str:
        """Generate error report when test execution fails"""
        timestamp = int(time.time())
        report_dir = os.path.join(report_folder, f"error_report_{timestamp}")
        os.makedirs(report_dir, exist_ok=True)

        report_file = os.path.join(report_dir, "error_report.html")

        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Execution Error - {self.package_name}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-danger">
            <h1><i class="fas fa-exclamation-triangle"></i> Test Execution Error</h1>
            <hr>
            <h4>Package: {self.package_name}</h4>
            <p><strong>Error:</strong> {error_message}</p>
            <p><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
        """

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return report_file

def main():
    """Enhanced main function with professional reporting"""

    # Initialize logging if not already done
    logger = None
    try:
        from logger_system import TerminalLogger, create_log_summary
        import atexit

        logger = TerminalLogger("main", "execution_logs")
        logger.start_logging()
        atexit.register(logger.stop_logging)
        atexit.register(create_log_summary)

    except ImportError:
        # logger_system not available, continue without logging
        pass
    except Exception as e:
        print(f"⚠️  Logging initialization failed: {e}")

    try:
        print("=" * 80)
        print("PROFESSIONAL GHERKIN TEST RUNNER")
        print("=" * 80)

        # Load configuration
        config = load_config()

        # Setup emulator and app
        print("🚀 Setting up test environment...")
        start_emulator(config['emulator']['avd_name'], config['emulator']['headless'])

        apk_path = get_apk_path(config['apk_folder'])
        package = get_package_name(apk_path)
        print(f"📱 Target package: {package}")

        # Handle app installation
        if is_app_installed(package):
            if prompt_user(f"{package} is already installed. Run with current app?"):
                print("✅ Using current installed app.")
            else:
                print("🔄 Reinstalling app...")
                uninstall_app(package)
                install_app(apk_path)
        else:
            print("📦 Installing app...")
            install_app(apk_path)

        # Get device information
        device_info = get_device_info()
        print(f"🤖 Android Version: {device_info.get('android_version', 'Unknown')}")
        print(f"📱 Device: {device_info.get('manufacturer', 'Unknown')} {device_info.get('device_model', 'Unknown')}")

        # Initialize professional test runner
        test_runner = ProfessionalTestRunner(config)

        # Check if feature file exists
        features_folder = config.get('features_folder', './features')
        feature_file = os.path.join(features_folder, f"{package}.feature")

        if not os.path.exists(feature_file):
            print(f"❌ Feature file not found: {feature_file}")
            print("💡 Please run 'python analyze.py' first to generate Gherkin scenarios")
            sys.exit(1)

        print(f"📋 Found feature file: {feature_file}")

        # Run Gherkin scenarios with professional reporting
        report_folder = config.get('report_folder', './reports')
        html_report_path = test_runner.run_gherkin_scenarios(features_folder, report_folder, package)

        # Display results summary
        total_scenarios = len(test_runner.test_results)
        passed_scenarios = len([r for r in test_runner.test_results if r['status'] == 'passed'])
        failed_scenarios = total_scenarios - passed_scenarios

        print("\n" + "=" * 80)
        print("TEST EXECUTION SUMMARY")
        print("=" * 80)
        print(f"📊 Total Scenarios: {total_scenarios}")
        print(f"✅ Passed: {passed_scenarios}")
        print(f"❌ Failed: {failed_scenarios}")
        print(f"📈 Pass Rate: {(passed_scenarios/total_scenarios*100):.1f}%" if total_scenarios > 0 else "📈 Pass Rate: 0%")

        if test_runner.start_time and test_runner.end_time:
            duration = (test_runner.end_time - test_runner.start_time).total_seconds()
            print(f"⏱️  Total Duration: {duration:.2f} seconds")

        print(f"📄 Professional HTML Report: {html_report_path}")

        # Open report in browser (optional)
        if prompt_user("Open HTML report in browser?"):
            try:
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(html_report_path)}")
            except Exception as e:
                print(f"Could not open browser: {e}")

        print("\n🎉 Test execution completed successfully!")

        # Exit with appropriate code
        sys.exit(0 if failed_scenarios == 0 else 1)

    except KeyboardInterrupt:
        print("\n❌ Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error during test execution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()