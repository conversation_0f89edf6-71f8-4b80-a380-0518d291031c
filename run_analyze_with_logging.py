#!/usr/bin/env python3

"""
Wrapper script to run analyze.py with comprehensive logging
All terminal output will be saved to timestamped log files
"""

import sys
import os
import atexit
from logger_system import TerminalLogger, create_log_summary

def main():
    """Run analyze.py with comprehensive logging"""
    
    # Initialize logger
    logger = TerminalLogger("analyze", "execution_logs")
    
    # Register cleanup function
    atexit.register(logger.stop_logging)
    atexit.register(create_log_summary)
    
    try:
        # Start logging
        logger.start_logging()
        
        # Import and run the analyze module
        print("🚀 Starting Android App Analysis with Enhanced Logging")
        print("📱 Target: com.kemendikdasmen.rumahpendidikan")
        print("🎯 Features: Package Filtering + Smart Clicking + Comprehensive Crawling")
        print()
        
        # Import the analyze module and run it
        import analyze
        
        # Check if analyze has an enhanced_main function
        if hasattr(analyze, 'enhanced_main'):
            print("🔧 Running enhanced_main() function...")
            analyze.enhanced_main()
        elif hasattr(analyze, 'main'):
            print("🔧 Running main() function...")
            analyze.main()
        else:
            print("❌ No main function found in analyze.py")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Execution interrupted by user (Ctrl+C)")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        print("\n✅ Execution completed")

if __name__ == "__main__":
    main()
