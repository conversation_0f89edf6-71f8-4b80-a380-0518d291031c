#!/usr/bin/env python3

"""
Wrapper script to run main.py with comprehensive logging
All terminal output will be saved to timestamped log files
"""

import sys
import os
import atexit
from logger_system import TerminalLogger, create_log_summary

def main():
    """Run main.py with comprehensive logging"""
    
    # Initialize logger
    logger = TerminalLogger("main", "execution_logs")
    
    # Register cleanup function
    atexit.register(logger.stop_logging)
    atexit.register(create_log_summary)
    
    try:
        # Start logging
        logger.start_logging()
        
        # Import and run the main module
        print("🚀 Starting Professional Android Test Runner with Enhanced Logging")
        print("📱 Target: com.kemendikdasmen.rumahpendidikan")
        print("🎯 Features: Professional Testing + Gherkin Generation + HTML Reports")
        print()
        
        # Check if main.py exists
        if not os.path.exists('main.py'):
            print("❌ main.py not found in current directory")
            sys.exit(1)
        
        # Import the main module and run it
        import main
        
        # Check if main has a main function
        if hasattr(main, 'main'):
            print("🔧 Running main() function...")
            main.main()
        elif hasattr(main, 'enhanced_main'):
            print("🔧 Running enhanced_main() function...")
            main.enhanced_main()
        else:
            print("❌ No main function found in main.py")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Execution interrupted by user (Ctrl+C)")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        print("\n✅ Execution completed")

if __name__ == "__main__":
    main()
