#!/usr/bin/env python3

"""
Universal script runner with comprehensive logging
Runs any Python script and captures all output to timestamped log files
"""

import sys
import os
import argparse
from pathlib import Path
from logger_system import ProcessLogger, create_log_summary

def main():
    """Run any Python script with comprehensive logging"""
    
    parser = argparse.ArgumentParser(
        description="Run Python scripts with comprehensive logging",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_with_logging.py analyze.py
  python run_with_logging.py main.py
  python run_with_logging.py --script analyze.py
  python run_with_logging.py --script main.py --log-dir custom_logs
        """
    )
    
    parser.add_argument(
        'script',
        nargs='?',
        help='Python script to run (analyze.py or main.py)'
    )
    
    parser.add_argument(
        '--script', '-s',
        dest='script_alt',
        help='Alternative way to specify script'
    )
    
    parser.add_argument(
        '--log-dir', '-l',
        default='execution_logs',
        help='Base directory for log files (default: execution_logs)'
    )
    
    parser.add_argument(
        '--python', '-p',
        default='python',
        help='Python executable to use (default: python)'
    )
    
    args = parser.parse_args()
    
    # Determine script to run
    script_to_run = args.script or args.script_alt
    
    if not script_to_run:
        print("❌ No script specified. Use --help for usage information.")
        sys.exit(1)
    
    # Validate script exists
    if not os.path.exists(script_to_run):
        print(f"❌ Script not found: {script_to_run}")
        sys.exit(1)
    
    # Extract script name for logging
    script_name = Path(script_to_run).stem
    
    # Initialize logger
    logger = ProcessLogger(script_name, args.log_dir)
    
    try:
        print("🚀 Starting Python Script Execution with Comprehensive Logging")
        print(f"📄 Script: {script_to_run}")
        print(f"📁 Log Directory: {args.log_dir}")
        print(f"🐍 Python: {args.python}")
        print()
        
        # Run the script with logging
        command = [args.python, script_to_run]
        return_code = logger.run_with_logging(command, cwd=os.getcwd())
        
        # Create summary
        create_log_summary()
        
        sys.exit(return_code)
        
    except KeyboardInterrupt:
        print("\n⚠️  Execution interrupted by user (Ctrl+C)")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
