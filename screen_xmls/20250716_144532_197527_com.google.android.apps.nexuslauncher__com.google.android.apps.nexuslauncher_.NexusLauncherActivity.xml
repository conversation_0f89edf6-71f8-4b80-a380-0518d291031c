<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy index="0" class="hierarchy" rotation="0" width="1440" height="3120">
  <android.widget.FrameLayout index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,3120]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="0" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
    <android.widget.LinearLayout index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,3120]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
      <android.widget.FrameLayout index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" resource-id="android:id/content" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,3120]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
        <android.widget.FrameLayout index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/launcher" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,3120]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
          <android.widget.FrameLayout index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/drag_layer" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,3120]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
            <android.view.View index="0" package="com.google.android.apps.nexuslauncher" class="android.view.View" text="" resource-id="com.google.android.apps.nexuslauncher:id/scrim_view" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,3120]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="6" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
            <android.widget.RelativeLayout index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.RelativeLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/apps_view" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,3120]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="9" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
              <android.widget.GridView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.GridView" text="" resource-id="com.google.android.apps.nexuslauncher:id/search_results_list_view" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,285][1440,3120]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="Photos" content-desc="Photos" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[62,411][391,797]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                <android.widget.TextView index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="Clock" content-desc="Clock" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[391,411][720,797]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                <android.widget.TextView index="2" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="Gmail" content-desc="Gmail" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[720,411][1049,797]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="3" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                <android.widget.TextView index="3" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="YouTube" content-desc="YouTube" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[1049,411][1378,797]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="4" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                <android.view.View index="4" package="com.google.android.apps.nexuslauncher" class="android.view.View" text="" resource-id="com.google.android.apps.nexuslauncher:id/search_result_divider" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[62,797][1378,839]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="5" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                <android.view.ViewGroup index="5" package="com.google.android.apps.nexuslauncher" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[62,839][1378,1752]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="6" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                  <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="Search web and more" resource-id="com.google.android.apps.nexuslauncher:id/title_text" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[146,923][1294,1035]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  <android.widget.TextView index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="You can search for web and app content from logged in accounts, including Play, Settings and Contacts. Selected suggestions may be saved in your Google app or Google Play search history. You can make updates in search settings." resource-id="com.google.android.apps.nexuslauncher:id/body_text" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[146,1091][1294,1437]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  <android.widget.Button index="2" package="com.google.android.apps.nexuslauncher" class="android.widget.Button" text="Got it" resource-id="com.google.android.apps.nexuslauncher:id/got_it" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1060,1521][1294,1689]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="3" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                </android.view.ViewGroup>
                <android.view.View index="6" package="com.google.android.apps.nexuslauncher" class="android.view.View" text="" resource-id="com.google.android.apps.nexuslauncher:id/search_result_divider" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[62,1752][1378,1794]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="7" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                <android.widget.LinearLayout index="7" package="com.google.android.apps.nexuslauncher" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[62,1794][1378,1990]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="8" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                  <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="the bear season 5" resource-id="com.google.android.apps.nexuslauncher:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[90,1808][258,1976]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  <android.view.ViewGroup index="1" package="com.google.android.apps.nexuslauncher" class="android.view.ViewGroup" text="" resource-id="com.google.android.apps.nexuslauncher:id/text_rows" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[258,1854][1378,1930]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                    <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="the bear season 5" content-desc="Search the bear season 5" resource-id="com.google.android.apps.nexuslauncher:id/title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[286,1854][743,1930]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  </android.view.ViewGroup>
                </android.widget.LinearLayout>
                <android.widget.LinearLayout index="8" package="com.google.android.apps.nexuslauncher" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[62,1990][1378,2242]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="9" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                  <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="England" resource-id="com.google.android.apps.nexuslauncher:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[90,2032][258,2200]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  <android.view.ViewGroup index="1" package="com.google.android.apps.nexuslauncher" class="android.view.ViewGroup" text="" resource-id="com.google.android.apps.nexuslauncher:id/text_rows" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[258,2045][1154,2187]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                    <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="England" content-desc="Search england cricket team" resource-id="com.google.android.apps.nexuslauncher:id/title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[286,2045][501,2121]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                    <android.widget.TextView index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="Cricket team" resource-id="com.google.android.apps.nexuslauncher:id/subtitle" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[286,2121][620,2187]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="3" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  </android.view.ViewGroup>
                  <android.widget.FrameLayout index="2" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/suggest_entity_container" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1182,2046][1322,2186]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="7" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                    <android.view.ViewGroup index="0" package="com.google.android.apps.nexuslauncher" class="android.view.ViewGroup" text="" resource-id="com.google.android.apps.nexuslauncher:id/result_thumbnail" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1182,2046][1322,2186]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                      <android.widget.ImageView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.ImageView" text="" resource-id="com.google.android.apps.nexuslauncher:id/thumbnail" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1215,2046][1290,2186]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                    </android.view.ViewGroup>
                  </android.widget.FrameLayout>
                </android.widget.LinearLayout>
                <android.widget.LinearLayout index="9" package="com.google.android.apps.nexuslauncher" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[62,2242][1378,2438]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="10" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                  <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="trump bill" resource-id="com.google.android.apps.nexuslauncher:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[90,2256][258,2424]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  <android.view.ViewGroup index="1" package="com.google.android.apps.nexuslauncher" class="android.view.ViewGroup" text="" resource-id="com.google.android.apps.nexuslauncher:id/text_rows" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[258,2302][1378,2378]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                    <android.widget.TextView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.TextView" text="trump bill" content-desc="Search trump bill" resource-id="com.google.android.apps.nexuslauncher:id/title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[286,2302][535,2378]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  </android.view.ViewGroup>
                </android.widget.LinearLayout>
                <android.widget.LinearLayout index="10" package="com.google.android.apps.nexuslauncher" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[62,2438][1378,2606]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="11" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                  <android.widget.ImageButton index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.ImageButton" text="" content-desc="Learn more about search on your device" resource-id="com.google.android.apps.nexuslauncher:id/learn_more_button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1014,2438][1182,2606]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  <android.widget.ImageButton index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.ImageButton" text="" content-desc="Search settings" resource-id="com.google.android.apps.nexuslauncher:id/search_settings_button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1210,2438][1378,2606]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                </android.widget.LinearLayout>
              </android.widget.GridView>
              <android.widget.FrameLayout index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" content-desc="Search" resource-id="com.google.android.apps.nexuslauncher:id/search_container_all_apps" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[50,169][1390,390]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="6" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                <android.widget.RelativeLayout index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.RelativeLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[50,184][1376,375]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                  <android.widget.ImageView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.ImageView" text="" content-desc="Google app" resource-id="com.google.android.apps.nexuslauncher:id/g_icon" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[78,195][246,363]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  <android.widget.FrameLayout index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/typeahead_input" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[274,184][1040,375]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="3" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                    <android.widget.EditText index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.EditText" text="Search web and more" resource-id="com.google.android.apps.nexuslauncher:id/input" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="true" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[274,184][1040,375]" displayed="true" hint="Search web and more" a11y-important="true" screen-reader-focusable="false" input-type="1" drawing-order="2" showing-hint="true" text-entry-key="false" multiline="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" max-text-length="5000" content-invalid="false" />
                  </android.widget.FrameLayout>
                  <android.widget.LinearLayout index="2" package="com.google.android.apps.nexuslauncher" class="android.widget.LinearLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/end_icons" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1040,195][1376,363]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="4" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false">
                    <android.widget.ImageView index="0" package="com.google.android.apps.nexuslauncher" class="android.widget.ImageView" text="" content-desc="Voice search" resource-id="com.google.android.apps.nexuslauncher:id/mic_icon" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1040,195][1208,363]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                    <android.widget.ImageButton index="1" package="com.google.android.apps.nexuslauncher" class="android.widget.ImageButton" text="" content-desc="Google Lens" resource-id="com.google.android.apps.nexuslauncher:id/lens_icon" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1208,195][1376,363]" displayed="true" a11y-important="true" screen-reader-focusable="false" drawing-order="2" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                  </android.widget.LinearLayout>
                  <android.widget.FrameLayout index="3" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/action_btn_container" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1194,188][1376,370]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
                </android.widget.RelativeLayout>
              </android.widget.FrameLayout>
            </android.widget.RelativeLayout>
            <android.view.View index="3" package="com.google.android.apps.nexuslauncher" class="android.view.View" text="" content-desc="Home" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,144][1440,3036]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="1" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
            <android.widget.FrameLayout index="4" package="com.google.android.apps.nexuslauncher" class="android.widget.FrameLayout" text="" resource-id="com.google.android.apps.nexuslauncher:id/overview_actions_view" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,2593][1440,3120]" displayed="true" a11y-important="false" screen-reader-focusable="false" drawing-order="8" showing-hint="false" text-entry-key="false" dismissable="false" a11y-focused="false" heading="false" live-region="0" context-clickable="false" content-invalid="false" />
          </android.widget.FrameLayout>
        </android.widget.FrameLayout>
      </android.widget.FrameLayout>
    </android.widget.LinearLayout>
  </android.widget.FrameLayout>
</hierarchy>