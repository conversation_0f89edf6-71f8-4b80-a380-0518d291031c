#!/usr/bin/env python3

"""
Test script to verify that the system actually scrolls to menu positions
Tests your requirement: "you still need to do action scroll until you in the position"
"""

import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def start_test_session(package):
    """Start test Appium session"""
    print(f"[TEST] Starting test session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)
    
    driver.activate_app(package)
    time.sleep(3)
    
    return driver

def test_actual_scrolling_to_position(driver):
    """Test that the system actually performs scrolling actions"""
    print("\n🧪 TEST: ACTUAL SCROLLING TO POSITION")
    print("=" * 70)
    print("Testing: 'you still need to do action scroll until you in the position'")
    
    # Import functions from analyze.py
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, scroll_to_position, simple_smart_click_menu
    
    try:
        # Step 1: Collect menu locations
        print("\n📋 Step 1: Collecting menu locations...")
        elements, menu_locations = collect_all_elements_simple(driver, "Scroll Test")
        
        if not menu_locations:
            print("[TEST] ❌ No menu locations found")
            return False
        
        # Find a menu that's not at position 0 (requires scrolling)
        target_menu = None
        target_position = 0
        
        for menu_name, location in menu_locations.items():
            if location['scroll_position'] > 0:
                target_menu = menu_name
                target_position = location['scroll_position']
                break
        
        if not target_menu:
            print("[TEST] ⚠️ No menus found that require scrolling")
            # Use first menu anyway
            target_menu = list(menu_locations.keys())[0]
            target_position = menu_locations[target_menu]['scroll_position']
        
        print(f"[TEST] 🎯 Target menu: {target_menu}")
        print(f"[TEST] 📍 Target scroll position: {target_position}")
        print(f"[TEST] 📍 Menu location: y={menu_locations[target_menu]['y']}")
        
        # Step 2: Scroll to bottom first to test scrolling action
        print(f"\n📋 Step 2: Scrolling to bottom first...")
        size = driver.get_window_size()
        for i in range(5):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.7)
            end_y = int(size['height'] * 0.3)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)
            print(f"[TEST] Bottom scroll {i+1}/5")
        
        print(f"[TEST] ✅ Now at bottom position")
        
        # Step 3: Test scroll to specific position
        print(f"\n📋 Step 3: Testing scroll to position {target_position}...")
        scroll_result = scroll_to_position(driver, target_position)
        print(f"[TEST] Scroll to position result: {'✅ SUCCESS' if scroll_result else '❌ FAILED'}")
        
        # Step 4: Verify menu is now visible
        print(f"\n📋 Step 4: Verifying menu is now visible...")
        try:
            menu_elements = driver.find_elements("xpath", f"//*[@text='{target_menu}' or @content-desc='{target_menu}']")
            if menu_elements:
                current_location = menu_elements[0].location
                current_y = current_location['y']
                
                # Check if in visible area
                visible_top = 200
                visible_bottom = size['height'] - 200
                is_visible = visible_top <= current_y <= visible_bottom
                
                print(f"[TEST] Menu now at y={current_y}")
                print(f"[TEST] Visible area: {visible_top} to {visible_bottom}")
                print(f"[TEST] Menu visible: {'✅ YES' if is_visible else '❌ NO'}")
                
                return is_visible
            else:
                print(f"[TEST] ❌ Menu not found after scrolling")
                return False
                
        except Exception as e:
            print(f"[TEST] Error checking menu visibility: {e}")
            return False
        
    except Exception as e:
        print(f"[TEST] Error in scrolling test: {e}")
        return False

def test_smart_click_with_scrolling(driver):
    """Test smart click that actually scrolls to position"""
    print("\n🧪 TEST: SMART CLICK WITH ACTUAL SCROLLING")
    print("=" * 70)
    print("Testing: Smart click that scrolls to menu position before clicking")
    
    # Import functions
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, simple_smart_click_menu
    
    try:
        # Collect menu locations
        elements, menu_locations = collect_all_elements_simple(driver, "Click Test")
        
        if not menu_locations:
            print("[TEST] ❌ No menu locations found")
            return False
        
        # Find a menu that requires scrolling
        target_menu = None
        for menu_name, location in menu_locations.items():
            if location['scroll_position'] > 0:
                target_menu = menu_name
                break
        
        if not target_menu:
            target_menu = list(menu_locations.keys())[0]
        
        print(f"[TEST] 🎯 Testing smart click with: {target_menu}")
        print(f"[TEST] 📍 Menu at scroll position: {menu_locations[target_menu]['scroll_position']}")
        
        # Scroll to bottom first
        print(f"[TEST] 📋 Scrolling to bottom first...")
        size = driver.get_window_size()
        for i in range(5):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.7)
            end_y = int(size['height'] * 0.3)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)
        
        print(f"[TEST] ✅ Now at bottom, testing smart click...")
        
        # Test smart click with location data
        click_result = simple_smart_click_menu(driver, target_menu, menu_locations)
        print(f"[TEST] Smart click result: {'✅ SUCCESS' if click_result else '❌ FAILED'}")
        
        if click_result:
            print(f"[TEST] ✅ Menu clicked successfully! Going back...")
            driver.back()
            time.sleep(2)
        
        return click_result
        
    except Exception as e:
        print(f"[TEST] Error in smart click test: {e}")
        return False

def test_scroll_position_accuracy(driver):
    """Test scroll position accuracy"""
    print("\n🧪 TEST: SCROLL POSITION ACCURACY")
    print("=" * 70)
    print("Testing: Accuracy of scroll position targeting")
    
    # Import functions
    import sys
    sys.path.append('.')
    from analyze import scroll_to_position
    
    try:
        positions_to_test = [0, 1, 2, 3]
        results = []
        
        for position in positions_to_test:
            print(f"\n📍 Testing scroll to position {position}...")
            
            # Scroll to position
            scroll_result = scroll_to_position(driver, position)
            
            # Wait and check
            time.sleep(2)
            
            # Try to find elements that should be visible at this position
            try:
                elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
                element_count = len(elements)
                print(f"[TEST] Position {position}: {element_count} elements visible")
                results.append((position, scroll_result, element_count))
            except:
                results.append((position, scroll_result, 0))
        
        print(f"\n📊 SCROLL POSITION RESULTS:")
        for position, success, element_count in results:
            status = "✅ SUCCESS" if success else "❌ FAILED"
            print(f"  - Position {position}: {status} ({element_count} elements)")
        
        return all(success for _, success, _ in results)
        
    except Exception as e:
        print(f"[TEST] Error in position accuracy test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING ACTUAL SCROLLING TO POSITION")
    print("=" * 80)
    print("Testing your requirement:")
    print("'you already collect the position menu, but you still need to do action scroll until you in the position'")
    print("=" * 80)
    
    try:
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"
        
        driver = start_test_session(package)
        
        # Test 1: Actual scrolling to position
        test1_result = test_actual_scrolling_to_position(driver)
        
        # Test 2: Smart click with scrolling
        test2_result = test_smart_click_with_scrolling(driver)
        
        # Test 3: Scroll position accuracy
        test3_result = test_scroll_position_accuracy(driver)
        
        print("\n" + "="*80)
        print("✅ ALL TESTS COMPLETE")
        print("="*80)
        
        print(f"\n📊 TEST RESULTS:")
        print(f"  - Actual Scrolling to Position: {'✅ PASS' if test1_result else '❌ FAIL'}")
        print(f"  - Smart Click with Scrolling: {'✅ PASS' if test2_result else '❌ FAIL'}")
        print(f"  - Scroll Position Accuracy: {'✅ PASS' if test3_result else '❌ FAIL'}")
        
        print(f"\n🎯 YOUR REQUIREMENT STATUS:")
        print(f"  ✅ Menu position collection: WORKING")
        print(f"  ✅ Actual scroll action: {'IMPLEMENTED' if test1_result else 'NEEDS WORK'}")
        print(f"  ✅ Scroll to exact position: {'WORKING' if test3_result else 'NEEDS WORK'}")
        print(f"  ✅ Smart click with scroll: {'WORKING' if test2_result else 'NEEDS WORK'}")
        
        overall_status = "✅ REQUIREMENT FULLY MET" if all([test1_result, test2_result, test3_result]) else "⚠️ NEEDS IMPROVEMENT"
        print(f"\n🎉 FINAL STATUS: {overall_status}")
        
        print("\nPress Enter to close session...")
        input()
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
