#!/usr/bin/env python3

"""
Test script to verify ADB timeout fixes work correctly
"""

import subprocess
import time

def test_adb_commands():
    """Test ADB commands with timeouts"""
    print("🧪 Testing ADB Commands with Timeouts")
    print("=" * 50)
    
    # Test 1: adb devices (should work quickly)
    print("1. Testing 'adb devices'...")
    try:
        start_time = time.time()
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        duration = time.time() - start_time
        print(f"   ✅ Success in {duration:.2f}s")
        print(f"   Output: {result.stdout.strip()}")
    except subprocess.TimeoutExpired:
        print("   ❌ Timed out after 10 seconds")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: adb shell pm list packages (this was hanging)
    print("\n2. Testing 'adb shell pm list packages'...")
    try:
        start_time = time.time()
        result = subprocess.run(['adb', 'shell', 'pm', 'list', 'packages'], capture_output=True, text=True, timeout=15)
        duration = time.time() - start_time
        if result.returncode == 0:
            package_count = len(result.stdout.splitlines())
            print(f"   ✅ Success in {duration:.2f}s")
            print(f"   Found {package_count} packages")
            
            # Check for target package
            if 'com.kemendikdasmen.rumahpendidikan' in result.stdout:
                print("   🎯 Target app is installed!")
            else:
                print("   ⚠️  Target app not found")
        else:
            print(f"   ❌ Failed with return code: {result.returncode}")
    except subprocess.TimeoutExpired:
        print("   ❌ Timed out after 15 seconds (this was the problem!)")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Test the fixed is_app_installed function
    print("\n3. Testing fixed is_app_installed function...")
    try:
        # Import the function from analyze.py
        import sys
        sys.path.append('.')
        from analyze import is_app_installed
        
        start_time = time.time()
        is_installed = is_app_installed('com.kemendikdasmen.rumahpendidikan')
        duration = time.time() - start_time
        print(f"   ✅ Function completed in {duration:.2f}s")
        print(f"   App installed: {is_installed}")
        
    except Exception as e:
        print(f"   ❌ Error testing function: {e}")

def test_package_detection():
    """Test the enhanced package detection"""
    print("\n🔍 Testing Enhanced Package Detection")
    print("=" * 50)
    
    try:
        import sys
        sys.path.append('.')
        from analyze import get_package_name, load_config, get_apk_path
        
        # Load config
        config = load_config()
        if not config:
            print("❌ Could not load config")
            return
        
        # Get APK path
        apk_path = get_apk_path(config['apk_folder'])
        print(f"📱 APK path: {apk_path}")
        
        # Test package detection
        start_time = time.time()
        package = get_package_name(apk_path, config)
        duration = time.time() - start_time
        print(f"✅ Package detection completed in {duration:.2f}s")
        print(f"📦 Package: {package}")
        
    except Exception as e:
        print(f"❌ Error testing package detection: {e}")

def main():
    """Run all tests"""
    print("🚀 ADB TIMEOUT FIX VERIFICATION")
    print("=" * 60)
    
    test_adb_commands()
    test_package_detection()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY")
    print("If all tests passed, the hanging issue should be resolved!")
    print("You can now run: python analyze.py")

if __name__ == "__main__":
    main()
