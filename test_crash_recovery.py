#!/usr/bin/env python3

"""
Test script to verify crash recovery functionality
"""

import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def test_crash_recovery():
    """Test the crash recovery system"""
    print("🧪 TESTING CRASH RECOVERY SYSTEM")
    print("=" * 60)
    
    try:
        # Load config
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"
        
        print(f"📱 Target package: {package}")
        print("🔗 Connecting to Appium...")
        
        # Start Appium session
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator-5554"
        options.app_package = package
        options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        
        driver = webdriver.Remote('http://localhost:4723', options=options)
        driver.implicitly_wait(10)
        
        print("✅ Appium session started!")
        
        # Test 1: Normal operation
        print("\n🔍 Test 1: Normal Operation")
        print("-" * 40)
        
        try:
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"✅ Found {len(elements)} elements normally")
            
            current_activity = driver.current_activity
            print(f"✅ Current activity: {current_activity}")
            
        except Exception as e:
            print(f"❌ Normal operation failed: {e}")
        
        # Test 2: Simulate app restart
        print("\n🔍 Test 2: App Restart Recovery")
        print("-" * 40)
        
        try:
            print("🔄 Restarting app...")
            driver.activate_app(package)
            time.sleep(5)
            
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"✅ App restart successful, found {len(elements)} elements")
            
        except Exception as e:
            print(f"❌ App restart failed: {e}")
        
        # Test 3: Check for crash indicators
        print("\n🔍 Test 3: Crash Detection")
        print("-" * 40)
        
        try:
            # Try to detect if app is responsive
            page_source = driver.page_source
            if len(page_source) > 100:
                print("✅ App appears responsive (page source available)")
            else:
                print("⚠️ App may not be fully loaded (minimal page source)")
            
            # Check activity
            activity = driver.current_activity
            if activity and package in activity:
                print(f"✅ App activity is correct: {activity}")
            else:
                print(f"⚠️ Unexpected activity: {activity}")
                
        except Exception as e:
            print(f"❌ Crash detection test failed: {e}")
        
        # Test 4: Stress test with multiple element searches
        print("\n🔍 Test 4: Stress Test")
        print("-" * 40)
        
        success_count = 0
        total_attempts = 10
        
        for i in range(total_attempts):
            try:
                elements = driver.find_elements("xpath", f"//*[position()<={i+5}]")
                print(f"  Attempt {i+1}: Found {len(elements)} elements")
                success_count += 1
                time.sleep(0.5)
                
            except Exception as e:
                error_msg = str(e)
                print(f"  Attempt {i+1}: Failed - {error_msg[:100]}...")
                
                # Check if this looks like a crash
                if "instrumentation process is not running" in error_msg:
                    print("    🚨 Crash detected! Attempting recovery...")
                    
                    try:
                        driver.activate_app(package)
                        time.sleep(3)
                        print("    ✅ Recovery successful")
                        success_count += 1
                    except Exception as recovery_error:
                        print(f"    ❌ Recovery failed: {recovery_error}")
        
        print(f"\n📊 Stress test results: {success_count}/{total_attempts} successful")
        
        # Test 5: Final verification
        print("\n🔍 Test 5: Final Verification")
        print("-" * 40)
        
        try:
            # Get final state
            final_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            final_activity = driver.current_activity
            final_page_source = driver.page_source
            
            print(f"📊 Final State:")
            print(f"  - Elements: {len(final_elements)}")
            print(f"  - Activity: {final_activity}")
            print(f"  - Page source length: {len(final_page_source)}")
            
            # Check if app is in good state
            if (len(final_elements) > 5 and 
                final_activity and package in final_activity and 
                len(final_page_source) > 1000):
                print("🎉 App is in good state after all tests!")
            else:
                print("⚠️ App may be in degraded state")
                
        except Exception as e:
            print(f"❌ Final verification failed: {e}")
        
        # Keep session open for manual inspection
        print(f"\n📱 Session ready for manual inspection...")
        print("Press Enter to close session...")
        input()
        
        # Close session
        driver.quit()
        print("✅ Crash recovery test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_crash_recovery()
