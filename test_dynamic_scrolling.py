#!/usr/bin/env python3

"""
Test DYNAMIC scrolling system - Your exact requirement
Tests: "scroll must be dynamic depend what code needed to click"
"""

import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def start_test_session(package):
    """Start test Appium session"""
    print(f"[TEST] Starting test session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)
    
    driver.activate_app(package)
    time.sleep(3)
    
    return driver

def test_dynamic_position_detection(driver):
    """Test dynamic position detection"""
    print("\n🧪 TEST: DYNAMIC POSITION DETECTION")
    print("=" * 60)
    print("Testing: Current position detection for dynamic scrolling")
    
    # Import functions from analyze.py
    import sys
    sys.path.append('.')
    from analyze import get_current_page_position, collect_all_elements_simple
    
    try:
        # Test position detection at different scroll levels
        positions_tested = []
        
        # Test position 0 (top)
        print(f"\n📍 Testing position detection at TOP...")
        current_pos = get_current_page_position(driver)
        positions_tested.append(("TOP", current_pos))
        print(f"[TEST] Position at top: {current_pos}")
        
        # Scroll down and test position 1
        print(f"\n📍 Scrolling down and testing position detection...")
        size = driver.get_window_size()
        start_x = size['width'] // 2
        start_y = int(size['height'] * 0.6)
        end_y = int(size['height'] * 0.4)
        driver.swipe(start_x, start_y, start_x, end_y, 800)
        time.sleep(2)
        
        current_pos = get_current_page_position(driver)
        positions_tested.append(("AFTER_SCROLL_1", current_pos))
        print(f"[TEST] Position after scroll 1: {current_pos}")
        
        # Scroll down more and test position 2
        print(f"\n📍 Scrolling down more and testing position detection...")
        driver.swipe(start_x, start_y, start_x, end_y, 800)
        time.sleep(2)
        
        current_pos = get_current_page_position(driver)
        positions_tested.append(("AFTER_SCROLL_2", current_pos))
        print(f"[TEST] Position after scroll 2: {current_pos}")
        
        print(f"\n📊 POSITION DETECTION RESULTS:")
        for test_name, position in positions_tested:
            print(f"  - {test_name}: Position {position}")
        
        return len(set(pos for _, pos in positions_tested)) > 1  # Different positions detected
        
    except Exception as e:
        print(f"[TEST] Error in position detection test: {e}")
        return False

def test_dynamic_scrolling_to_menu(driver):
    """Test dynamic scrolling to specific menu"""
    print("\n🧪 TEST: DYNAMIC SCROLLING TO MENU")
    print("=" * 60)
    print("Testing: 'scroll must be dynamic depend what code needed to click'")
    
    # Import functions
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, dynamic_scroll_to_menu, get_current_page_position
    
    try:
        # Step 1: Collect menu locations
        print(f"\n📋 Step 1: Collecting menu locations...")
        elements, menu_locations = collect_all_elements_simple(driver, "Dynamic Test")
        
        if not menu_locations:
            print("[TEST] ❌ No menu locations found")
            return False
        
        print(f"[TEST] ✅ Found {len(menu_locations)} menu locations")
        for menu_name, location in menu_locations.items():
            print(f"[TEST]   - {menu_name}: Position {location['scroll_position']}")
        
        # Step 2: Test dynamic scrolling to different menus
        test_results = []
        
        # Find menus at different positions
        menus_by_position = {}
        for menu_name, location in menu_locations.items():
            pos = location['scroll_position']
            if pos not in menus_by_position:
                menus_by_position[pos] = menu_name
        
        print(f"\n📍 Found menus at positions: {list(menus_by_position.keys())}")
        
        # Test scrolling to each position
        for target_position, menu_name in menus_by_position.items():
            print(f"\n🎯 Testing dynamic scroll to position {target_position} (menu: {menu_name})")
            
            # Get current position
            current_pos = get_current_page_position(driver)
            print(f"[TEST] Current position: {current_pos}, Target: {target_position}")
            
            if current_pos == target_position:
                print(f"[TEST] Already at target position, scrolling away first...")
                # Scroll to a different position first
                size = driver.get_window_size()
                start_x = size['width'] // 2
                if target_position == 0:
                    # Scroll down if at top
                    start_y = int(size['height'] * 0.6)
                    end_y = int(size['height'] * 0.3)
                else:
                    # Scroll up if not at top
                    start_y = int(size['height'] * 0.4)
                    end_y = int(size['height'] * 0.7)
                driver.swipe(start_x, start_y, start_x, end_y, 800)
                time.sleep(2)
            
            # Test dynamic scrolling
            scroll_result = dynamic_scroll_to_menu(driver, menu_name, menu_locations)
            
            # Verify we reached the target
            final_pos = get_current_page_position(driver)
            
            success = scroll_result and (final_pos == target_position)
            test_results.append((menu_name, target_position, success))
            
            print(f"[TEST] Dynamic scroll to {menu_name}: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"[TEST] Final position: {final_pos} (target: {target_position})")
            
            time.sleep(1)  # Brief pause between tests
        
        print(f"\n📊 DYNAMIC SCROLLING RESULTS:")
        success_count = 0
        for menu_name, target_pos, success in test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"  - {menu_name} (pos {target_pos}): {status}")
            if success:
                success_count += 1
        
        overall_success = success_count >= len(test_results) // 2  # At least half successful
        print(f"\n🎯 OVERALL: {success_count}/{len(test_results)} tests passed")
        
        return overall_success
        
    except Exception as e:
        print(f"[TEST] Error in dynamic scrolling test: {e}")
        return False

def test_smart_click_with_dynamic_scroll(driver):
    """Test smart click with dynamic scrolling"""
    print("\n🧪 TEST: SMART CLICK WITH DYNAMIC SCROLLING")
    print("=" * 60)
    print("Testing: Complete flow with dynamic positioning")
    
    # Import functions
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, simple_smart_click_menu, get_current_page_position
    
    try:
        # Collect menu locations
        elements, menu_locations = collect_all_elements_simple(driver, "Smart Click Test")
        
        if not menu_locations:
            print("[TEST] ❌ No menu locations found")
            return False
        
        # Find a menu to test (prefer one not at position 0)
        target_menu = None
        for menu_name, location in menu_locations.items():
            if location['scroll_position'] > 0:
                target_menu = menu_name
                break
        
        if not target_menu:
            target_menu = list(menu_locations.keys())[0]
        
        print(f"[TEST] 🎯 Testing smart click with dynamic scroll: {target_menu}")
        target_position = menu_locations[target_menu]['scroll_position']
        print(f"[TEST] Target menu at position: {target_position}")
        
        # Move to a different position first
        current_pos = get_current_page_position(driver)
        if current_pos == target_position:
            print(f"[TEST] Moving away from target position first...")
            size = driver.get_window_size()
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.6)
            end_y = int(size['height'] * 0.3)
            driver.swipe(start_x, start_y, start_x, end_y, 800)
            time.sleep(2)
        
        # Test smart click with dynamic scrolling
        print(f"[TEST] 📋 Testing smart click with dynamic scrolling...")
        click_result = simple_smart_click_menu(driver, target_menu, menu_locations)
        
        print(f"[TEST] Smart click result: {'✅ SUCCESS' if click_result else '❌ FAILED'}")
        
        if click_result:
            print(f"[TEST] ✅ Menu clicked successfully! Going back...")
            driver.back()
            time.sleep(2)
        
        return click_result
        
    except Exception as e:
        print(f"[TEST] Error in smart click test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING DYNAMIC SCROLLING SYSTEM")
    print("=" * 80)
    print("Testing your exact requirement:")
    print("'scroll must be dynamic depend what code needed to click'")
    print("'when the page not in the position menu you can scroll into the location'")
    print("'either scroll down or scroll up'")
    print("=" * 80)
    
    try:
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"
        
        driver = start_test_session(package)
        
        # Test 1: Dynamic position detection
        test1_result = test_dynamic_position_detection(driver)
        
        # Test 2: Dynamic scrolling to menu
        test2_result = test_dynamic_scrolling_to_menu(driver)
        
        # Test 3: Smart click with dynamic scrolling
        test3_result = test_smart_click_with_dynamic_scroll(driver)
        
        print("\n" + "="*80)
        print("✅ ALL TESTS COMPLETE")
        print("="*80)
        
        print(f"\n📊 TEST RESULTS:")
        print(f"  - Dynamic Position Detection: {'✅ PASS' if test1_result else '❌ FAIL'}")
        print(f"  - Dynamic Scrolling to Menu: {'✅ PASS' if test2_result else '❌ FAIL'}")
        print(f"  - Smart Click with Dynamic Scroll: {'✅ PASS' if test3_result else '❌ FAIL'}")
        
        print(f"\n🎯 YOUR REQUIREMENT STATUS:")
        print(f"  ✅ Dynamic position detection: {'WORKING' if test1_result else 'FAILED'}")
        print(f"  ✅ Dynamic scroll up/down: {'WORKING' if test2_result else 'FAILED'}")
        print(f"  ✅ Smart positioning: {'WORKING' if test3_result else 'FAILED'}")
        print(f"  ✅ 'scroll must be dynamic': {'IMPLEMENTED' if test2_result else 'NEEDS WORK'}")
        
        overall_status = "✅ DYNAMIC SCROLLING WORKING" if all([test1_result, test2_result, test3_result]) else "⚠️ NEEDS IMPROVEMENT"
        print(f"\n🎉 FINAL STATUS: {overall_status}")
        
        if all([test1_result, test2_result, test3_result]):
            print("\n🎯 SUCCESS: Your requirement is now implemented:")
            print("  - Dynamic position detection")
            print("  - Smart scroll up/down based on need")
            print("  - Position-aware menu clicking")
            print("  - No static scrolling - all dynamic!")
        
        print("\nPress Enter to close session...")
        input()
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
