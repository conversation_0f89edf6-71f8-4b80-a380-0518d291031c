#!/usr/bin/env python3
"""
Test script for the enhanced Android automation system
"""

import sys
import os
import json
from unittest.mock import Mock, MagicMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from analyze import (
        StateManager, SmartNavigationEngine, ComprehensiveElementCollector,
        CrashRecoverySystem, SmartMenuDetector, EnhancedAppCrawler,
        GherkinGenerationEngine, PageContext, NavigationState, ElementInfo
    )
    print("✅ Successfully imported all enhanced classes")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_state_manager():
    """Test StateManager functionality"""
    print("\n🧪 Testing StateManager...")
    
    try:
        # Create state manager
        state_manager = StateManager("com.test.app")
        
        # Test basic operations
        state_manager.update_navigation_path("Test Page", PageContext.NATIVE)
        state_manager.set_recovery_checkpoint("Test checkpoint")
        
        # Test state saving/loading
        state_manager.save_state()
        
        # Create new instance and load state
        new_state_manager = StateManager("com.test.app")
        loaded = new_state_manager.load_state()
        
        if loaded:
            print("✅ StateManager: Save/load functionality works")
        else:
            print("⚠️  StateManager: Could not load state (may be expected for first run)")
            
        print("✅ StateManager: Basic functionality works")
        
    except Exception as e:
        print(f"❌ StateManager test failed: {e}")
        return False
        
    return True

def test_gherkin_generation():
    """Test Gherkin generation"""
    print("\n🧪 Testing GherkinGenerationEngine...")
    
    try:
        # Mock config
        config = {'ai_model': 'phi3:medium'}
        
        # Create engine
        gherkin_engine = GherkinGenerationEngine(config)
        
        # Mock crawl results
        mock_results = {
            'collected_elements': [
                {
                    'text': 'Login Button',
                    'content_desc': 'Login',
                    'clickable': True,
                    'class_name': 'Button',
                    'page_context': 'native',
                    'navigation_level': 'main_page'
                }
            ],
            'navigation_path': {
                'final_path': ['Main Page', 'Login Page'],
                'final_depth': 1
            },
            'page_states': {
                'Main Page': {
                    'context': 'native',
                    'navigation_level': 'main_page',
                    'elements': []
                }
            },
            'visited_pages': ['Main Page', 'Login Page']
        }
        
        # Test fallback generation (when AI is not available)
        gherkin_content = gherkin_engine._generate_fallback_gherkin(mock_results, "com.test.app")
        
        if "Feature:" in gherkin_content and "Scenario:" in gherkin_content:
            print("✅ GherkinGenerationEngine: Fallback generation works")
        else:
            print("❌ GherkinGenerationEngine: Fallback generation failed")
            return False
            
        print("✅ GherkinGenerationEngine: Basic functionality works")
        
    except Exception as e:
        print(f"❌ GherkinGenerationEngine test failed: {e}")
        return False
        
    return True

def test_enhanced_crawler_initialization():
    """Test EnhancedAppCrawler initialization"""
    print("\n🧪 Testing EnhancedAppCrawler initialization...")
    
    try:
        # Mock driver
        mock_driver = Mock()
        mock_driver.current_package = "com.test.app"
        mock_driver.contexts = ["NATIVE_APP"]
        mock_driver.page_source = "<hierarchy></hierarchy>"
        
        # Mock config
        config = {
            'wait_timeout': 10,
            'ai_model': 'phi3:medium',
            'locators_folder': './test_locators',
            'features_folder': './test_features'
        }
        
        # Create crawler
        crawler = EnhancedAppCrawler(mock_driver, "com.test.app", config)
        
        # Check if all components are initialized
        assert crawler.state_manager is not None
        assert crawler.navigator is not None
        assert crawler.collector is not None
        assert crawler.recovery_system is not None
        assert crawler.menu_detector is not None
        
        print("✅ EnhancedAppCrawler: Initialization successful")
        
    except Exception as e:
        print(f"❌ EnhancedAppCrawler initialization test failed: {e}")
        return False
        
    return True

def test_element_info_creation():
    """Test ElementInfo dataclass"""
    print("\n🧪 Testing ElementInfo creation...")
    
    try:
        element = ElementInfo(
            xpath="//button[1]",
            text="Test Button",
            content_desc="Test",
            resource_id="test_button",
            class_name="Button",
            clickable=True,
            visible=True,
            bounds={'x': 0, 'y': 0, 'width': 100, 'height': 50},
            page_context=PageContext.NATIVE,
            navigation_level=NavigationState.MAIN_PAGE,
            collected_at="2024-01-01T00:00:00"
        )
        
        assert element.text == "Test Button"
        assert element.clickable == True
        assert element.page_context == PageContext.NATIVE
        
        print("✅ ElementInfo: Creation and access works")
        
    except Exception as e:
        print(f"❌ ElementInfo test failed: {e}")
        return False
        
    return True

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Enhanced Android Automation System Tests")
    print("=" * 60)
    
    tests = [
        test_element_info_creation,
        test_state_manager,
        test_gherkin_generation,
        test_enhanced_crawler_initialization
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Enhanced system is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
