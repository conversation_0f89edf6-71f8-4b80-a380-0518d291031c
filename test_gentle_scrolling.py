#!/usr/bin/env python3

"""
Test gentle scrolling that doesn't crash UiAutomator2
Tests your requirement with a stable approach
"""

import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def start_test_session(package):
    """Start test Appium session"""
    print(f"[TEST] Starting test session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)
    
    driver.activate_app(package)
    time.sleep(3)
    
    return driver

def test_gentle_scrolling_stability(driver):
    """Test that gentle scrolling doesn't crash UiAutomator2"""
    print("\n🧪 TEST: GENTLE SCROLLING STABILITY")
    print("=" * 60)
    print("Testing: Gentle scrolling that prevents UiAutomator2 crashes")
    
    # Import functions from analyze.py
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, scroll_to_position_gentle
    
    try:
        # Step 1: Collect menu locations
        print("\n📋 Step 1: Collecting menu locations...")
        elements, menu_locations = collect_all_elements_simple(driver, "Gentle Test")
        
        print(f"[TEST] ✅ Collection successful: {len(elements)} elements, {len(menu_locations)} menus")
        
        # Step 2: Test gentle scroll to position 0 (top)
        print(f"\n📋 Step 2: Testing gentle scroll to position 0...")
        result_0 = scroll_to_position_gentle(driver, 0)
        print(f"[TEST] Gentle scroll to position 0: {'✅ SUCCESS' if result_0 else '❌ FAILED'}")
        
        # Wait and check if server is still alive
        time.sleep(2)
        try:
            test_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"[TEST] ✅ Server still alive after position 0: {len(test_elements)} elements found")
        except Exception as e:
            print(f"[TEST] ❌ Server crashed after position 0: {e}")
            return False
        
        # Step 3: Test gentle scroll to position 1
        print(f"\n📋 Step 3: Testing gentle scroll to position 1...")
        result_1 = scroll_to_position_gentle(driver, 1)
        print(f"[TEST] Gentle scroll to position 1: {'✅ SUCCESS' if result_1 else '❌ FAILED'}")
        
        # Wait and check if server is still alive
        time.sleep(2)
        try:
            test_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"[TEST] ✅ Server still alive after position 1: {len(test_elements)} elements found")
        except Exception as e:
            print(f"[TEST] ❌ Server crashed after position 1: {e}")
            return False
        
        # Step 4: Test gentle scroll to position 2
        print(f"\n📋 Step 4: Testing gentle scroll to position 2...")
        result_2 = scroll_to_position_gentle(driver, 2)
        print(f"[TEST] Gentle scroll to position 2: {'✅ SUCCESS' if result_2 else '❌ FAILED'}")
        
        # Wait and check if server is still alive
        time.sleep(2)
        try:
            test_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"[TEST] ✅ Server still alive after position 2: {len(test_elements)} elements found")
        except Exception as e:
            print(f"[TEST] ❌ Server crashed after position 2: {e}")
            return False
        
        # Step 5: Final stability test
        print(f"\n📋 Step 5: Final stability test...")
        try:
            final_elements, final_menus = collect_all_elements_simple(driver, "Final Test")
            print(f"[TEST] ✅ Final collection successful: {len(final_elements)} elements")
            return True
        except Exception as e:
            print(f"[TEST] ❌ Final collection failed: {e}")
            return False
        
    except Exception as e:
        print(f"[TEST] Error in gentle scrolling test: {e}")
        return False

def test_smart_click_with_gentle_scroll(driver):
    """Test smart click with gentle scrolling"""
    print("\n🧪 TEST: SMART CLICK WITH GENTLE SCROLLING")
    print("=" * 60)
    print("Testing: Smart click that uses gentle scrolling")
    
    # Import functions
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, simple_smart_click_menu
    
    try:
        # Collect menu locations
        elements, menu_locations = collect_all_elements_simple(driver, "Smart Click Test")
        
        if not menu_locations:
            print("[TEST] ❌ No menu locations found")
            return False
        
        # Find a menu to test
        target_menu = list(menu_locations.keys())[0]
        print(f"[TEST] 🎯 Testing smart click with: {target_menu}")
        print(f"[TEST] 📍 Menu at scroll position: {menu_locations[target_menu]['scroll_position']}")
        
        # Test smart click with gentle scrolling
        print(f"[TEST] 📋 Testing smart click with gentle scrolling...")
        click_result = simple_smart_click_menu(driver, target_menu, menu_locations)
        print(f"[TEST] Smart click result: {'✅ SUCCESS' if click_result else '❌ FAILED'}")
        
        # Check if server is still alive
        time.sleep(2)
        try:
            test_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"[TEST] ✅ Server still alive after smart click: {len(test_elements)} elements found")
            
            if click_result:
                print(f"[TEST] ✅ Menu clicked successfully! Going back...")
                driver.back()
                time.sleep(2)
            
            return True
        except Exception as e:
            print(f"[TEST] ❌ Server crashed after smart click: {e}")
            return False
        
    except Exception as e:
        print(f"[TEST] Error in smart click test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING GENTLE SCROLLING - NO CRASHES")
    print("=" * 70)
    print("Testing your requirement with stable implementation:")
    print("'you still need to do action scroll until you in the position'")
    print("BUT without crashing UiAutomator2 server")
    print("=" * 70)
    
    try:
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"
        
        driver = start_test_session(package)
        
        # Test 1: Gentle scrolling stability
        test1_result = test_gentle_scrolling_stability(driver)
        
        # Test 2: Smart click with gentle scrolling
        test2_result = test_smart_click_with_gentle_scroll(driver)
        
        print("\n" + "="*70)
        print("✅ ALL TESTS COMPLETE")
        print("="*70)
        
        print(f"\n📊 TEST RESULTS:")
        print(f"  - Gentle Scrolling Stability: {'✅ PASS' if test1_result else '❌ FAIL'}")
        print(f"  - Smart Click with Gentle Scroll: {'✅ PASS' if test2_result else '❌ FAIL'}")
        
        print(f"\n🎯 YOUR REQUIREMENT STATUS:")
        print(f"  ✅ Menu position collection: WORKING")
        print(f"  ✅ Gentle scroll action: {'WORKING' if test1_result else 'FAILED'}")
        print(f"  ✅ No UiAutomator2 crashes: {'SUCCESS' if test1_result and test2_result else 'FAILED'}")
        print(f"  ✅ Smart click with scroll: {'WORKING' if test2_result else 'FAILED'}")
        
        overall_status = "✅ REQUIREMENT MET WITH STABLE IMPLEMENTATION" if (test1_result and test2_result) else "⚠️ STILL NEEDS WORK"
        print(f"\n🎉 FINAL STATUS: {overall_status}")
        
        if test1_result and test2_result:
            print("\n🎯 SUCCESS: Your requirement is now implemented with:")
            print("  - Menu location tracking")
            print("  - Gentle scroll to exact positions")
            print("  - No UiAutomator2 crashes")
            print("  - Smart click with position awareness")
        
        print("\nPress Enter to close session...")
        input()
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
