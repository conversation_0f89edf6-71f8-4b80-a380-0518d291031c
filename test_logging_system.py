#!/usr/bin/env python3

"""
Test script to verify the logging system works correctly
"""

import os
import sys
import time
import datetime
from pathlib import Path

def test_basic_logging():
    """Test basic logging functionality"""
    print("🧪 Testing Basic Logging System")
    print("=" * 50)
    
    try:
        from logger_system import TerminalLogger, create_log_summary
        
        # Test logger creation
        logger = TerminalLogger("test", "test_logs")
        print(f"✅ Logger created: {logger.log_file_path}")
        
        # Test logging start
        logger.start_logging()
        print("✅ Logging started successfully")
        
        # Test some output
        print("📝 This is a test message")
        print("🔢 Testing numbers: 1, 2, 3")
        print("⚠️  Testing warning message")
        print("❌ Testing error message")
        
        # Test error output
        import sys
        print("Testing stderr output", file=sys.stderr)
        
        # Simulate some work
        for i in range(3):
            print(f"🔄 Processing step {i+1}/3...")
            time.sleep(0.5)
        
        # Test logging stop
        logger.stop_logging()
        print("✅ Logging stopped successfully")
        
        # Verify log file exists
        if logger.log_file_path.exists():
            size = logger.log_file_path.stat().st_size
            print(f"✅ Log file created: {size} bytes")
            return True
        else:
            print("❌ Log file not created")
            return False
            
    except Exception as e:
        print(f"❌ Basic logging test failed: {e}")
        return False

def test_process_logging():
    """Test process-based logging"""
    print("\n🧪 Testing Process Logging System")
    print("=" * 50)
    
    try:
        from logger_system import ProcessLogger
        
        # Create a simple test script
        test_script = Path("temp_test_script.py")
        test_script.write_text("""
import time
print("Hello from test script!")
print("Processing...")
for i in range(3):
    print(f"Step {i+1}")
    time.sleep(0.2)
print("Test script completed!")
""")
        
        # Test process logger
        logger = ProcessLogger("temp_test", "test_logs")
        print(f"✅ Process logger created: {logger.log_file_path}")
        
        # Run the test script
        return_code = logger.run_with_logging(["python", str(test_script)])
        
        # Cleanup
        test_script.unlink()
        
        if return_code == 0:
            print("✅ Process logging test successful")
            return True
        else:
            print(f"❌ Process returned non-zero exit code: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ Process logging test failed: {e}")
        return False

def test_wrapper_scripts():
    """Test wrapper scripts"""
    print("\n🧪 Testing Wrapper Scripts")
    print("=" * 50)
    
    # Check if wrapper scripts exist
    wrappers = [
        "run_analyze_with_logging.py",
        "run_main_with_logging.py", 
        "run_with_logging.py"
    ]
    
    all_exist = True
    for wrapper in wrappers:
        if os.path.exists(wrapper):
            print(f"✅ {wrapper} exists")
        else:
            print(f"❌ {wrapper} missing")
            all_exist = False
    
    return all_exist

def test_log_structure():
    """Test log directory structure"""
    print("\n🧪 Testing Log Directory Structure")
    print("=" * 50)
    
    try:
        # Check if test logs were created
        test_logs_dir = Path("test_logs")
        if test_logs_dir.exists():
            log_files = list(test_logs_dir.rglob("*.txt"))
            print(f"✅ Found {len(log_files)} log files")
            
            for log_file in log_files:
                rel_path = log_file.relative_to(test_logs_dir)
                size = log_file.stat().st_size
                print(f"  📄 {rel_path} ({size} bytes)")
            
            return len(log_files) > 0
        else:
            print("❌ No test logs directory found")
            return False
            
    except Exception as e:
        print(f"❌ Log structure test failed: {e}")
        return False

def cleanup_test_logs():
    """Clean up test logs"""
    try:
        import shutil
        test_logs_dir = Path("test_logs")
        if test_logs_dir.exists():
            shutil.rmtree(test_logs_dir)
            print("🧹 Test logs cleaned up")
    except Exception as e:
        print(f"⚠️  Failed to cleanup test logs: {e}")

def main():
    """Run all logging system tests"""
    print("🚀 LOGGING SYSTEM COMPREHENSIVE TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    print()
    
    tests = [
        ("Basic Logging", test_basic_logging),
        ("Process Logging", test_process_logging),
        ("Wrapper Scripts", test_wrapper_scripts),
        ("Log Structure", test_log_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Logging system is ready to use.")
        print("\nTo use the logging system:")
        print("  python analyze.py          # Built-in logging")
        print("  python main.py             # Built-in logging")
        print("  python run_with_logging.py analyze.py  # Enhanced logging")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    # Cleanup
    cleanup_test_logs()

if __name__ == "__main__":
    main()
