#!/usr/bin/env python3

"""
Test script to verify menu location tracking functionality
Tests the new feature: "when you collect each menu, you also need where is menu location"
"""

import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def start_test_session(package):
    """Start test Appium session"""
    print(f"[TEST] Starting test session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)
    
    driver.activate_app(package)
    time.sleep(3)
    
    return driver

def test_menu_location_tracking(driver):
    """Test menu location tracking functionality"""
    print("\n🧪 TEST: MENU LOCATION TRACKING")
    print("=" * 60)
    print("Testing: Menu location collection and smart positioning")
    
    # Import functions from analyze.py
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, scroll_to_menu_position, simple_smart_click_menu
    
    try:
        # Test 1: Collect elements and menu locations
        print("\n📋 Step 1: Collecting elements with location tracking...")
        elements, menu_locations = collect_all_elements_simple(driver, "Test Page")
        
        print(f"\n📊 COLLECTION RESULTS:")
        print(f"  - Total elements: {len(elements)}")
        print(f"  - Menu locations found: {len(menu_locations)}")
        
        # Test 2: Display menu locations
        print(f"\n📍 MENU LOCATIONS FOUND:")
        for menu_name, location in menu_locations.items():
            print(f"  - {menu_name}:")
            print(f"    * Position: ({location['x']}, {location['y']})")
            print(f"    * Center: ({location['center_x']}, {location['center_y']})")
            print(f"    * Size: {location['width']}x{location['height']}")
            print(f"    * Scroll position: {location['scroll_position']}")
        
        # Test 3: Test scroll to menu position
        if menu_locations:
            test_menu = list(menu_locations.keys())[0]
            print(f"\n🎯 Step 2: Testing scroll to menu position for '{test_menu}'...")
            
            # First scroll to bottom to test positioning
            print("[TEST] Scrolling to bottom first...")
            size = driver.get_window_size()
            for i in range(3):
                start_x = size['width'] // 2
                start_y = int(size['height'] * 0.7)
                end_y = int(size['height'] * 0.3)
                driver.swipe(start_x, start_y, start_x, end_y, 1000)
                time.sleep(1)
            
            print(f"[TEST] Now testing scroll to menu position...")
            scroll_result = scroll_to_menu_position(driver, test_menu, menu_locations)
            print(f"[TEST] Scroll to menu result: {'✅ SUCCESS' if scroll_result else '❌ FAILED'}")
            
            # Test 4: Test smart click with location
            print(f"\n🎯 Step 3: Testing smart click with location for '{test_menu}'...")
            click_result = simple_smart_click_menu(driver, test_menu, menu_locations)
            print(f"[TEST] Smart click result: {'✅ SUCCESS' if click_result else '❌ FAILED'}")
            
            if click_result:
                print("[TEST] Menu clicked successfully! Going back...")
                driver.back()
                time.sleep(2)
        
        return True, len(menu_locations)
        
    except Exception as e:
        print(f"[TEST] Error in menu location test: {e}")
        return False, 0

def test_position_awareness(driver):
    """Test position awareness functionality"""
    print("\n🧪 TEST: POSITION AWARENESS")
    print("=" * 60)
    print("Testing: 'if you in wrong position the menu can't click'")
    
    # Import functions
    import sys
    sys.path.append('.')
    from analyze import collect_all_elements_simple, simple_smart_click_menu
    
    try:
        # Collect menu locations
        elements, menu_locations = collect_all_elements_simple(driver, "Position Test")
        
        if not menu_locations:
            print("[TEST] ⚠️ No menu locations found for position test")
            return False
        
        test_menu = list(menu_locations.keys())[0]
        print(f"[TEST] Testing position awareness with menu: {test_menu}")
        
        # Test 1: Click from correct position (top)
        print(f"\n📍 Test 1: Click from TOP position...")
        # Scroll to top first
        size = driver.get_window_size()
        for i in range(3):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.6)
            end_y = int(size['height'] * 0.8)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)
        
        # Try click with location awareness
        result1 = simple_smart_click_menu(driver, test_menu, menu_locations)
        print(f"[TEST] Click from top with location: {'✅ SUCCESS' if result1 else '❌ FAILED'}")
        
        if result1:
            driver.back()
            time.sleep(2)
        
        # Test 2: Click from wrong position (bottom)
        print(f"\n📍 Test 2: Click from BOTTOM position...")
        # Scroll to bottom
        for i in range(5):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.7)
            end_y = int(size['height'] * 0.3)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)
        
        # Try click without location awareness (should fail)
        result2 = simple_smart_click_menu(driver, test_menu, None)
        print(f"[TEST] Click from bottom without location: {'✅ SUCCESS' if result2 else '❌ FAILED (Expected)'}")
        
        # Try click with location awareness (should succeed)
        result3 = simple_smart_click_menu(driver, test_menu, menu_locations)
        print(f"[TEST] Click from bottom with location: {'✅ SUCCESS' if result3 else '❌ FAILED'}")
        
        if result3:
            driver.back()
            time.sleep(2)
        
        print(f"\n📊 POSITION AWARENESS RESULTS:")
        print(f"  - Top position with location: {'✅ PASS' if result1 else '❌ FAIL'}")
        print(f"  - Bottom position without location: {'✅ PASS (Failed as expected)' if not result2 else '❌ FAIL (Should have failed)'}")
        print(f"  - Bottom position with location: {'✅ PASS' if result3 else '❌ FAIL'}")
        
        return True
        
    except Exception as e:
        print(f"[TEST] Error in position awareness test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING MENU LOCATION TRACKING")
    print("=" * 70)
    print("Testing your requirement:")
    print("'when you collect each menu, you also need where is menu location'")
    print("'this is useful for when you in position bottom and you want to click menu'")
    print("'you need to scroll first until you in the position menu'")
    print("=" * 70)
    
    try:
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"
        
        driver = start_test_session(package)
        
        # Test 1: Menu location tracking
        test1_result, menu_count = test_menu_location_tracking(driver)
        
        # Test 2: Position awareness
        test2_result = test_position_awareness(driver)
        
        print("\n" + "="*70)
        print("✅ ALL TESTS COMPLETE")
        print("="*70)
        
        print(f"\n📊 TEST RESULTS:")
        print(f"  - Menu Location Tracking: {'✅ PASS' if test1_result else '❌ FAIL'}")
        print(f"  - Position Awareness: {'✅ PASS' if test2_result else '❌ FAIL'}")
        print(f"  - Menu Locations Found: {menu_count}")
        
        print(f"\n🎯 YOUR REQUIREMENT STATUS:")
        print(f"  ✅ Menu location collection: {'IMPLEMENTED' if test1_result else 'FAILED'}")
        print(f"  ✅ Smart scroll to menu position: {'WORKING' if test2_result else 'FAILED'}")
        print(f"  ✅ Position-aware clicking: {'WORKING' if test2_result else 'FAILED'}")
        print(f"  ✅ 'if you in wrong position the menu can't click': {'SOLVED' if test2_result else 'NOT SOLVED'}")
        
        overall_status = "✅ ALL REQUIREMENTS MET" if (test1_result and test2_result) else "⚠️ SOME ISSUES DETECTED"
        print(f"\n🎉 FINAL STATUS: {overall_status}")
        
        print("\nPress Enter to close session...")
        input()
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
