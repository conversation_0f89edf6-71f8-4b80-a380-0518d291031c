#!/usr/bin/env python3

"""
Test script to verify that <PERSON>pp<PERSON> connects to existing app without restarting it
"""

import time
import yaml
import subprocess
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def get_app_start_time(package):
    """Get the start time of the app process"""
    try:
        result = subprocess.run(
            ['adb', 'shell', 'ps', '-o', 'STIME,CMD'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            lines = result.stdout.splitlines()
            for line in lines:
                if package in line:
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        return parts[0]  # STIME (start time)
        return None
        
    except Exception as e:
        print(f"Error getting app start time: {e}")
        return None

def test_no_restart():
    """Test that <PERSON><PERSON><PERSON> connects without restarting the app"""
    print("🧪 TESTING NO-RESTART APPIUM CONNECTION")
    print("=" * 60)
    
    try:
        package = "com.kemendikdasmen.rumahpendidikan"
        
        # Step 1: Launch app manually first
        print("📱 Step 1: Launching app manually...")
        result = subprocess.run(
            ['adb', 'shell', 'monkey', '-p', package, '-c', 'android.intent.category.LAUNCHER', '1'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ App launched manually")
        else:
            print("❌ Manual app launch failed")
            return
        
        # Wait for app to start
        time.sleep(5)
        
        # Step 2: Get initial app start time
        print("\n🕐 Step 2: Recording app start time...")
        initial_start_time = get_app_start_time(package)
        print(f"Initial app start time: {initial_start_time}")
        
        # Step 3: Connect with Appium (should NOT restart app)
        print("\n🔗 Step 3: Connecting with Appium (no-restart mode)...")
        
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator-5554"
        options.app_package = package
        options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        
        # Critical: Prevent app restart
        options.set_capability('noReset', True)
        options.set_capability('fullReset', False)
        options.set_capability('autoLaunch', False)
        options.set_capability('dontStopAppOnReset', True)
        options.set_capability('skipDeviceInitialization', True)
        options.set_capability('skipServerInstallation', True)
        
        print("Appium capabilities:")
        print("  - noReset: True")
        print("  - fullReset: False") 
        print("  - autoLaunch: False")
        print("  - dontStopAppOnReset: True")
        
        driver = webdriver.Remote('http://localhost:4723', options=options)
        driver.implicitly_wait(10)
        
        print("✅ Appium session created!")
        
        # Step 4: Check if app was restarted
        print("\n🔍 Step 4: Checking if app was restarted...")
        time.sleep(2)  # Brief pause
        
        final_start_time = get_app_start_time(package)
        print(f"Final app start time: {final_start_time}")
        
        if initial_start_time and final_start_time:
            if initial_start_time == final_start_time:
                print("✅ SUCCESS: App was NOT restarted!")
                print("   The same app instance is being used.")
            else:
                print("❌ FAILURE: App was restarted!")
                print(f"   Start time changed: {initial_start_time} → {final_start_time}")
        else:
            print("⚠️ Could not determine if app was restarted (timing info unavailable)")
        
        # Step 5: Verify app functionality
        print("\n🧪 Step 5: Testing app functionality...")
        
        try:
            current_activity = driver.current_activity
            print(f"Current activity: {current_activity}")
            
            elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"Found {len(elements)} elements")
            
            # Look for key elements
            ruang_elements = driver.find_elements("xpath", "//*[contains(@content-desc, 'Ruang')]")
            print(f"Found {len(ruang_elements)} 'Ruang' elements")
            
            if len(elements) > 10 and len(ruang_elements) > 0:
                print("✅ App functionality verified - automation ready!")
            else:
                print("⚠️ App may not be fully loaded")
                
        except Exception as e:
            print(f"❌ App functionality test failed: {e}")
        
        # Step 6: Test multiple connections
        print("\n🔄 Step 6: Testing multiple connections...")
        
        try:
            # Close current session
            driver.quit()
            print("Closed first session")
            
            # Create second session
            driver2 = webdriver.Remote('http://localhost:4723', options=options)
            driver2.implicitly_wait(10)
            print("Created second session")
            
            # Check start time again
            second_final_time = get_app_start_time(package)
            print(f"After second session: {second_final_time}")
            
            if initial_start_time == second_final_time:
                print("✅ SUCCESS: App still not restarted after multiple sessions!")
            else:
                print("❌ App was restarted during second session")
            
            # Test functionality again
            elements2 = driver2.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            print(f"Second session found {len(elements2)} elements")
            
            driver2.quit()
            print("Closed second session")
            
        except Exception as e:
            print(f"❌ Multiple session test failed: {e}")
        
        print("\n🎯 Test Summary:")
        print("=" * 40)
        if initial_start_time and final_start_time and initial_start_time == final_start_time:
            print("✅ NO-RESTART MODE: WORKING CORRECTLY")
            print("   - App was not restarted by Appium")
            print("   - Same app instance used throughout")
            print("   - Automation connected to existing app")
        else:
            print("❌ NO-RESTART MODE: NEEDS IMPROVEMENT")
            print("   - App may have been restarted")
            print("   - Check Appium capabilities")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_no_restart()
