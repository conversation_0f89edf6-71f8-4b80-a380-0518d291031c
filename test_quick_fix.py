#!/usr/bin/env python3

"""
Quick test to verify the enhanced system works
"""

import sys
import os

def test_imports():
    """Test that all imports work correctly"""
    try:
        from analyze import EnhancedAppCrawler, SmartActionClicker
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_class_methods():
    """Test that the EnhancedAppCrawler has required methods"""
    try:
        from analyze import EnhancedAppCrawler
        
        # Create a dummy instance
        crawler = EnhancedAppCrawler(None, 'test', {})
        
        # Check for required methods
        required_methods = [
            'start_comprehensive_crawl',
            '_start_fresh_crawl',
            '_resume_crawl',
            '_collect_complete_page',
            '_crawl_all_main_menus'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(crawler, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ Missing methods: {missing_methods}")
            return False
        else:
            print("✅ All required methods present")
            return True
            
    except Exception as e:
        print(f"❌ Method test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Enhanced Android Automation System")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        sys.exit(1)
    
    # Test class methods
    if not test_class_methods():
        sys.exit(1)
    
    print("=" * 50)
    print("🎉 All tests passed! System is ready to use.")
    print("\nTo run the system:")
    print("python analyze.py")

if __name__ == "__main__":
    main()
