#!/usr/bin/env python3

"""
Test smart scroll to top functionality
Tests your requirement: "when you already scroll until bottom you need to go back again on top"
"but make sure if you already in top do not pull because that make refresh for the page"
"""

import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def start_test_session(package):
    """Start test Appium session"""
    print(f"[TEST] Starting test session for: {package}")
    
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "emulator-5554"
    options.app_package = package
    options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
    options.automation_name = "UiAutomator2"
    options.new_command_timeout = 300
    
    options.set_capability('noReset', True)
    options.set_capability('fullReset', False)
    options.set_capability('autoLaunch', False)
    options.set_capability('dontStopAppOnReset', True)
    options.set_capability('skipDeviceInitialization', True)
    options.set_capability('skipServerInstallation', True)
    
    driver = webdriver.Remote('http://localhost:4723', options=options)
    driver.implicitly_wait(10)
    
    driver.activate_app(package)
    time.sleep(3)
    
    return driver

def test_smart_top_detection(driver):
    """Test smart top detection - prevents pull-to-refresh"""
    print("\n🧪 TEST: SMART TOP DETECTION")
    print("=" * 60)
    print("Testing: 'if you already in top do not pull because that make refresh'")
    
    # Import functions from analyze.py
    import sys
    sys.path.append('.')
    from analyze import is_at_top_of_page, scroll_to_top_smart
    
    try:
        # Test 1: Check if at top initially
        print(f"\n📍 Test 1: Check if at top initially...")
        at_top_initial = is_at_top_of_page(driver)
        print(f"[TEST] Initially at top: {'✅ YES' if at_top_initial else '❌ NO'}")
        
        # Test 2: Smart scroll when already at top (should NOT scroll)
        if at_top_initial:
            print(f"\n📍 Test 2: Smart scroll when already at top...")
            print(f"[TEST] This should NOT perform any scroll (prevents pull-to-refresh)")
            
            # Count elements before
            elements_before = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            count_before = len(elements_before)
            
            # Try smart scroll
            scroll_result = scroll_to_top_smart(driver)
            
            # Count elements after
            elements_after = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
            count_after = len(elements_after)
            
            print(f"[TEST] Smart scroll result: {'✅ SUCCESS' if scroll_result else '❌ FAILED'}")
            print(f"[TEST] Elements before: {count_before}, after: {count_after}")
            
            # Check if page was refreshed (element count would change significantly)
            if abs(count_before - count_after) > 5:
                print(f"[TEST] ❌ Page may have been refreshed (element count changed significantly)")
                return False
            else:
                print(f"[TEST] ✅ No page refresh detected")
        
        # Test 3: Scroll to bottom first
        print(f"\n📍 Test 3: Scrolling to bottom...")
        size = driver.get_window_size()
        for i in range(3):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.7)
            end_y = int(size['height'] * 0.3)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)
            print(f"[TEST] Scroll down {i+1}/3")
        
        # Test 4: Check if at top after scrolling down
        at_top_after_scroll = is_at_top_of_page(driver)
        print(f"[TEST] At top after scrolling down: {'✅ YES' if at_top_after_scroll else '❌ NO (Expected)'}")
        
        # Test 5: Smart scroll back to top (should scroll)
        print(f"\n📍 Test 4: Smart scroll back to top from bottom...")
        print(f"[TEST] This SHOULD perform scroll (we're not at top)")
        
        scroll_back_result = scroll_to_top_smart(driver)
        print(f"[TEST] Smart scroll back result: {'✅ SUCCESS' if scroll_back_result else '❌ FAILED'}")
        
        # Test 6: Verify we're at top
        final_at_top = is_at_top_of_page(driver)
        print(f"[TEST] Finally at top: {'✅ YES' if final_at_top else '❌ NO'}")
        
        return True
        
    except Exception as e:
        print(f"[TEST] Error in smart top detection test: {e}")
        return False

def test_scroll_behavior_sequence(driver):
    """Test complete scroll behavior sequence"""
    print("\n🧪 TEST: COMPLETE SCROLL BEHAVIOR SEQUENCE")
    print("=" * 60)
    print("Testing: Complete flow with smart scroll management")
    
    # Import functions
    import sys
    sys.path.append('.')
    from analyze import is_at_top_of_page, scroll_to_top_smart
    
    try:
        sequence_results = []
        
        # Step 1: Start at top
        print(f"\n📍 Step 1: Verify starting at top...")
        at_top_1 = is_at_top_of_page(driver)
        sequence_results.append(("Start at top", at_top_1))
        print(f"[TEST] Start at top: {'✅ YES' if at_top_1 else '❌ NO'}")
        
        # Step 2: Smart scroll when at top (should not scroll)
        print(f"\n📍 Step 2: Smart scroll when at top...")
        scroll_result_1 = scroll_to_top_smart(driver)
        sequence_results.append(("Smart scroll at top", scroll_result_1))
        
        # Step 3: Scroll to bottom
        print(f"\n📍 Step 3: Scroll to bottom...")
        size = driver.get_window_size()
        for i in range(4):
            start_x = size['width'] // 2
            start_y = int(size['height'] * 0.7)
            end_y = int(size['height'] * 0.3)
            driver.swipe(start_x, start_y, start_x, end_y, 1000)
            time.sleep(1)
        
        at_top_2 = is_at_top_of_page(driver)
        sequence_results.append(("At top after scroll down", not at_top_2))  # Should be False
        print(f"[TEST] At top after scroll down: {'❌ NO (Expected)' if not at_top_2 else '✅ YES (Unexpected)'}")
        
        # Step 4: Smart scroll back to top (should scroll)
        print(f"\n📍 Step 4: Smart scroll back to top...")
        scroll_result_2 = scroll_to_top_smart(driver)
        sequence_results.append(("Smart scroll from bottom", scroll_result_2))
        
        # Step 5: Verify at top
        at_top_3 = is_at_top_of_page(driver)
        sequence_results.append(("Final at top", at_top_3))
        print(f"[TEST] Final at top: {'✅ YES' if at_top_3 else '❌ NO'}")
        
        # Step 6: Smart scroll again when at top (should not scroll)
        print(f"\n📍 Step 5: Smart scroll again when at top...")
        scroll_result_3 = scroll_to_top_smart(driver)
        sequence_results.append(("Smart scroll at top again", scroll_result_3))
        
        print(f"\n📊 SEQUENCE RESULTS:")
        for step, result in sequence_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  - {step}: {status}")
        
        success_count = sum(1 for _, result in sequence_results if result)
        overall_success = success_count >= len(sequence_results) - 1  # Allow 1 failure
        
        return overall_success
        
    except Exception as e:
        print(f"[TEST] Error in scroll behavior sequence: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING SMART SCROLL TO TOP")
    print("=" * 80)
    print("Testing your requirements:")
    print("1. 'when you already scroll until bottom you need to go back again on top'")
    print("2. 'if you already in top do not pull because that make refresh for the page'")
    print("=" * 80)
    
    try:
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"
        
        driver = start_test_session(package)
        
        # Test 1: Smart top detection
        test1_result = test_smart_top_detection(driver)
        
        # Test 2: Complete scroll behavior sequence
        test2_result = test_scroll_behavior_sequence(driver)
        
        print("\n" + "="*80)
        print("✅ ALL TESTS COMPLETE")
        print("="*80)
        
        print(f"\n📊 TEST RESULTS:")
        print(f"  - Smart Top Detection: {'✅ PASS' if test1_result else '❌ FAIL'}")
        print(f"  - Scroll Behavior Sequence: {'✅ PASS' if test2_result else '❌ FAIL'}")
        
        print(f"\n🎯 YOUR REQUIREMENT STATUS:")
        print(f"  ✅ Smart top detection: {'WORKING' if test1_result else 'FAILED'}")
        print(f"  ✅ No pull-to-refresh when at top: {'WORKING' if test1_result else 'FAILED'}")
        print(f"  ✅ Scroll back to top from bottom: {'WORKING' if test2_result else 'FAILED'}")
        print(f"  ✅ Complete scroll management: {'WORKING' if test2_result else 'FAILED'}")
        
        overall_status = "✅ ALL REQUIREMENTS MET" if (test1_result and test2_result) else "⚠️ NEEDS IMPROVEMENT"
        print(f"\n🎉 FINAL STATUS: {overall_status}")
        
        if test1_result and test2_result:
            print("\n🎯 SUCCESS: Your requirements are now implemented:")
            print("  - Smart detection if already at top")
            print("  - No scroll when at top (prevents pull-to-refresh)")
            print("  - Scroll back to top after reaching bottom")
            print("  - Intelligent scroll management")
        
        print("\nPress Enter to close session...")
        input()
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
