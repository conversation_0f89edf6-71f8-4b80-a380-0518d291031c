#!/usr/bin/env python3

"""
Test script to demonstrate the smart wait functionality
"""

import time
import yaml
from appium import webdriver
from appium.options.android import UiAutomator2Options

def load_config():
    """Load configuration from YAML file"""
    with open("config/config.yaml", 'r') as f:
        return yaml.safe_load(f)

def test_smart_wait_system():
    """Test the smart wait system with the target app"""
    print("🧪 TESTING SMART WAIT SYSTEM")
    print("=" * 60)
    
    try:
        # Load config
        config = load_config()
        package = "com.kemendikdasmen.rumahpendidikan"
        
        print(f"📱 Target package: {package}")
        print("🔗 Connecting to Appium...")
        
        # Start Appium session
        options = UiAutomator2Options()
        options.platform_name = "Android"
        options.device_name = "emulator-5554"
        options.app_package = package
        options.app_activity = "com.kemendikdasmen.rumahpendidikan.MainActivity"
        options.automation_name = "UiAutomator2"
        options.new_command_timeout = 300
        
        driver = webdriver.Remote('http://localhost:4723', options=options)
        driver.implicitly_wait(10)
        
        print("✅ Appium session started!")
        
        # Test the smart wait functions
        print("\n" + "="*60)
        print("TESTING SMART WAIT FUNCTIONS")
        print("="*60)
        
        # Test 1: Basic page stability check
        print("\n🔍 Test 1: Page Stability Check")
        print("-" * 40)
        
        start_time = time.time()
        
        # Get initial state
        initial_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
        initial_page_source = driver.page_source
        
        print(f"Initial state: {len(initial_elements)} elements, {len(initial_page_source)} chars")
        
        # Wait and check again
        time.sleep(5)
        
        final_elements = driver.find_elements("xpath", "//*[@text!='' or @content-desc!='']")
        final_page_source = driver.page_source
        
        print(f"After 5s: {len(final_elements)} elements, {len(final_page_source)} chars")
        
        # Check stability
        element_change = abs(len(final_elements) - len(initial_elements))
        page_change = abs(len(final_page_source) - len(initial_page_source))
        
        if element_change <= 2 and page_change <= 100:
            print("✅ Page appears stable!")
        else:
            print(f"⚠️ Page is changing (elements: ±{element_change}, page: ±{page_change})")
        
        # Test 2: Key element detection
        print("\n🔍 Test 2: Key Element Detection")
        print("-" * 40)
        
        key_patterns = [
            "//*[@text='Ruang GTK' or @content-desc='Ruang GTK']",
            "//*[@text='Ruang Murid' or @content-desc='Ruang Murid']",
            "//*[contains(@text, 'Ruang')]",
            "//android.widget.TextView[@text!='']",
            "//*[@clickable='true']"
        ]
        
        for i, pattern in enumerate(key_patterns, 1):
            try:
                elements = driver.find_elements("xpath", pattern)
                print(f"  Pattern {i}: {len(elements)} elements found - {pattern[:50]}...")
                
                if elements and len(elements) > 0:
                    # Show first few elements
                    for j, elem in enumerate(elements[:3]):
                        try:
                            text = elem.get_attribute('text') or ''
                            desc = elem.get_attribute('content-desc') or ''
                            display_text = text or desc or 'No text'
                            print(f"    {j+1}. '{display_text[:30]}'")
                        except:
                            print(f"    {j+1}. <Error getting text>")
                            
            except Exception as e:
                print(f"  Pattern {i}: Error - {e}")
        
        # Test 3: Loading indicator detection
        print("\n🔍 Test 3: Loading Indicator Detection")
        print("-" * 40)
        
        loading_patterns = [
            "//*[contains(@text, 'Loading') or contains(@text, 'loading')]",
            "//*[contains(@content-desc, 'Loading') or contains(@content-desc, 'loading')]",
            "//android.widget.ProgressBar",
            "//*[contains(@class, 'ProgressBar')]",
            "//*[contains(@class, 'Spinner')]"
        ]
        
        total_loading_indicators = 0
        for i, pattern in enumerate(loading_patterns, 1):
            try:
                elements = driver.find_elements("xpath", pattern)
                total_loading_indicators += len(elements)
                print(f"  Loading pattern {i}: {len(elements)} indicators - {pattern[:50]}...")
            except Exception as e:
                print(f"  Loading pattern {i}: Error - {e}")
        
        if total_loading_indicators == 0:
            print("✅ No loading indicators detected - app appears ready!")
        else:
            print(f"⚠️ {total_loading_indicators} loading indicators found - app may still be loading")
        
        # Test 4: Overall readiness assessment
        print("\n🔍 Test 4: Overall Readiness Assessment")
        print("-" * 40)
        
        total_elements = len(final_elements)
        has_meaningful_content = total_elements > 10
        has_clickable_elements = len(driver.find_elements("xpath", "//*[@clickable='true']")) > 0
        no_loading_indicators = total_loading_indicators == 0
        
        readiness_score = sum([has_meaningful_content, has_clickable_elements, no_loading_indicators])
        
        print(f"📊 Readiness Assessment:")
        print(f"  - Meaningful content: {'✅' if has_meaningful_content else '❌'} ({total_elements} elements)")
        print(f"  - Clickable elements: {'✅' if has_clickable_elements else '❌'}")
        print(f"  - No loading indicators: {'✅' if no_loading_indicators else '❌'}")
        print(f"  - Overall score: {readiness_score}/3")
        
        if readiness_score >= 2:
            print("🎉 App appears ready for automation!")
        else:
            print("⚠️ App may need more time to load")
        
        # Keep session open for manual inspection
        print(f"\n📱 Session ready for manual inspection...")
        print("Press Enter to close session...")
        input()
        
        # Close session
        driver.quit()
        print("✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_smart_wait_system()
