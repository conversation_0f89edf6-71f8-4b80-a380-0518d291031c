# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

BUILD_TOOLS_VERSION = 'buildToolsVersion'


class BuildToolsVersionOption(SupportsCapabilities):
    @property
    def build_tools_version(self) -> Optional[str]:
        """
        Version of Android build tools to use.
        """
        return self.get_capability(BUILD_TOOLS_VERSION)

    @build_tools_version.setter
    def build_tools_version(self, value: str) -> None:
        """
        The version of Android build tools to use. By default, UiAutomator2
        driver uses the most recent version of build tools installed on
        the machine, but sometimes it might be necessary to give it a hint
        (let say if there is a known bug in the most recent tools version).
        Example: 28.0.3
        """
        self.set_capability(BUILD_TOOLS_VERSION, value)
