# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

UNINSTALL_OTHER_PACKAGES = 'uninstallOtherPackages'


class UninstallOtherPackagesOption(SupportsCapabilities):
    @property
    def uninstall_other_packages(self) -> Optional[str]:
        """
        Identifiers of packages to be uninstalled from the device before a test starts.
        """
        return self.get_capability(UNINSTALL_OTHER_PACKAGES)

    @uninstall_other_packages.setter
    def uninstall_other_packages(self, value: str) -> None:
        """
        Allows to set one or more comma-separated package
        identifiers to be uninstalled from the device before a test starts.
        """
        self.set_capability(UNINSTALL_OTHER_PACKAGES, value)
