# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import List, Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

CHROMEDRIVER_ARGS = 'chromedriverArgs'


class ChromedriverArgsOption(SupportsCapabilities):
    @property
    def chromedriver_args(self) -> Optional[List[str]]:
        """
        Array of chromedriver CLI arguments.
        """
        return self.get_capability(CHROMEDRIVER_ARGS)

    @chromedriver_args.setter
    def chromedriver_args(self, value: List[str]) -> None:
        """
        Array of chromedriver [command line
        arguments](http://www.assertselenium.com/java/list-of-chrome-driver-command-line-arguments/).
        Note, that not all command line arguments that are available for the desktop
        browser are also available for the mobile one.
        """
        self.set_capability(CHROMEDRIVER_ARGS, value)
