# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

USE_KEYSTORE = 'useKeystore'


class UseKeystoreOption(SupportsCapabilities):
    @property
    def use_keystore(self) -> Optional[bool]:
        """
        Whether to use custom keystore.
        """
        return self.get_capability(USE_KEYSTORE)

    @use_keystore.setter
    def use_keystore(self, value: bool) -> None:
        """
        Whether to use a custom keystore to sign the app under test.
        false by default, which means apps are always signed with the default A
        ppium debug certificate (unless canceled by noSign capability).
        This option is used in combination with keystorePath, keystorePassword,
        keyAlias and keyPassword options.
        """
        self.set_capability(USE_KEYSTORE, value)
