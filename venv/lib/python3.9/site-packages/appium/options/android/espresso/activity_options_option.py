# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Dict, Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

ACTIVITY_OPTIONS = 'activityOptions'


class ActivityOptionsOption(SupportsCapabilities):
    @property
    def activity_options(self) -> Optional[Dict]:
        """
        Activity options.
        """
        return self.get_capability(ACTIVITY_OPTIONS)

    @activity_options.setter
    def activity_options(self, value: Dict) -> None:
        """
        The mapping of custom options for the main app activity that is going to
        be started. Check
        https://github.com/appium/appium-espresso-driver#activity-options
        for more details.
        """
        self.set_capability(ACTIVITY_OPTIONS, value)
