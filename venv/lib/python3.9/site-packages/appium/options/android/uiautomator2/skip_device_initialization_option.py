# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

SKIP_DEVICE_INITIALIZATION = 'skipDeviceInitialization'


class SkipDeviceInitializationOption(SupportsCapabilities):
    @property
    def skip_device_initialization(self) -> Optional[bool]:
        """
        Whether initial device startup checks by the server are disabled.
        """
        return self.get_capability(SKIP_DEVICE_INITIALIZATION)

    @skip_device_initialization.setter
    def skip_device_initialization(self, value: bool) -> None:
        """
        If set to true then device startup checks (whether it is ready and whether
        Settings app is installed) will be canceled on session creation.
        Could speed up the session creation if you know what you are doing. false by default
        """
        self.set_capability(SKIP_DEVICE_INITIALIZATION, value)
