# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Dict, Optional

from .supports_capabilities import SupportsCapabilities

PRERUN = 'prerun'


class PrerunOption(SupportsCapabilities):
    @property
    def prerun(self) -> Optional[Dict[str, str]]:
        """
        System script which is supposed to be executed before
        a driver session is initialised.
        """
        return self.get_capability(PRERUN)

    @prerun.setter
    def prerun(self, value: Dict[str, str]) -> None:
        """
        Set a system script which is supposed to be executed before
        a driver session is initialised.
        """
        self.set_capability(PRERUN, value)
