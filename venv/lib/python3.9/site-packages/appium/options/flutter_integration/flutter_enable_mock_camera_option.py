# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


from appium.options.common.supports_capabilities import SupportsCapabilities

FLUTTER_ENABLE_MOCK_CAMERA = 'flutterEnableMockCamera'


class FlutterEnableMockCameraOption(SupportsCapabilities):
    @property
    def flutter_enable_mock_camera(self) -> bool:
        """
        Get state of the mock camera for Flutter integration test

        Returns:
            bool: A boolean indicating whether the mock camera is enabled (True) or disabled (False).
        """
        return self.get_capability(FLUTTER_ENABLE_MOCK_CAMERA)

    @flutter_enable_mock_camera.setter
    def flutter_enable_mock_camera(self, value: bool) -> None:
        """
        Setter method enable or disable the mock camera for Flutter integration test
        Default state is `False`

        Args:
            value (bool): A boolean value indicating whether to enable (True) or disable (False) the mock camera.
        """
        self.set_capability(FLUTTER_ENABLE_MOCK_CAMERA, value)
