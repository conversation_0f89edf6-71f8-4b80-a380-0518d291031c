# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

DISABLE_AUTOMATIC_SCREENSHOTS = 'disableAutomaticScreenshots'


class DisableAutomaticScreenshotsOption(SupportsCapabilities):
    @property
    def disable_automatic_screenshots(self) -> Optional[bool]:
        """
        Whether to disable automatic XCTest screenshots.
        """
        return self.get_capability(DISABLE_AUTOMATIC_SCREENSHOTS)

    @disable_automatic_screenshots.setter
    def disable_automatic_screenshots(self, value: bool) -> None:
        """
        Disable automatic screenshots taken by XCTest at every interaction.
        Default is up to WebDriverAgent's config to decide, which currently
        defaults to true.
        """
        self.set_capability(DISABLE_AUTOMATIC_SCREENSHOTS, value)
