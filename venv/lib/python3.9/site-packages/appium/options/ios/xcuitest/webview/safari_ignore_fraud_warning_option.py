# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

SAFARI_IGNORE_FRAUD_WARNING = 'safariIgnoreFraudWarning'


class SafariIgnoreFraudWarningOption(SupportsCapabilities):
    @property
    def safari_ignore_fraud_warning(self) -> Optional[bool]:
        """
        Whether to prevent <PERSON>fari from showing a fraudulent website warning.
        """
        return self.get_capability(SAFARI_IGNORE_FRAUD_WARNING)

    @safari_ignore_fraud_warning.setter
    def safari_ignore_fraud_warning(self, value: bool) -> None:
        """
        Prevent Safari from showing a fraudulent website warning.
        Default keeps current sim setting..
        """
        self.set_capability(SAFARI_IGNORE_FRAUD_WARNING, value)
