# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

SAFARI_IGNORE_WEB_HOSTNAMES = 'safariIgnoreWebHostnames'


class SafariIgnoreWebHostnamesOption(SupportsCapabilities):
    @property
    def safari_ignore_web_hostnames(self) -> Optional[str]:
        """
        Comma-separated list of host names to be ignored.
        """
        return self.get_capability(SAFARI_IGNORE_WEB_HOSTNAMES)

    @safari_ignore_web_hostnames.setter
    def safari_ignore_web_hostnames(self, value: str) -> None:
        """
        Provide a list of hostnames (comma-separated) that the Safari automation
        tools should ignore. This is to provide a workaround to prevent a webkit
        bug where the web context is unintentionally changed to a 3rd party website
        and the test gets stuck. The common culprits are search engines (yahoo, bing,
        google) and about:blank.
        """
        self.set_capability(SAFARI_IGNORE_WEB_HOSTNAMES, value)
