# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from os import PathLike, fspath
from typing import Optional, Union

from appium.options.common.supports_capabilities import SupportsCapabilities

APP_PATH = 'appPath'


class AppPathOption(SupportsCapabilities):
    @property
    def app_path(self) -> Optional[str]:
        """
        The path of the application to automate.
        """
        return self.get_capability(APP_PATH)

    @app_path.setter
    def app_path(self, value: Union[str, PathLike]) -> None:
        """
        Set the path of the application to automate.
        """
        self.set_capability(APP_PATH, fspath(value))
