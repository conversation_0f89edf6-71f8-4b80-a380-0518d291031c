# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import List, Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

ARGUMENTS = 'arguments'


class ArgumentsOption(SupportsCapabilities):
    @property
    def arguments(self) -> Optional[List[str]]:
        """
        Array of application command line arguments.
        """
        return self.get_capability(ARGUMENTS)

    @arguments.setter
    def arguments(self, value: List[str]) -> None:
        """
        Set the array of application command line arguments. This capability is
        only going to be applied if the application is not running on session startup.
        """
        self.set_capability(ARGUMENTS, value)
