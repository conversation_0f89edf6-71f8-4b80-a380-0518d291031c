# Licensed to the Software Freedom Conservancy (SFC) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The SFC licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from appium.options.common.supports_capabilities import SupportsCapabilities

EXPERIMENTAL_WEB_DRIVER = 'ms:experimental-webdriver'


class ExperimentalWebDriverOption(SupportsCapabilities):
    @property
    def experimental_webdriver(self) -> Optional[bool]:
        """
        Whether to enable experimental features and optimizations.
        """
        return self.get_capability(EXPERIMENTAL_WEB_DRIVER)

    @experimental_webdriver.setter
    def experimental_webdriver(self, value: bool) -> None:
        """
        Enables experimental features and optimizations. See Appium Windows
        Driver release notes for more details on this capability.
        """
        self.set_capability(EXPERIMENTAL_WEB_DRIVER, value)
